<?php

use Illuminate\Support\Facades\Broadcast;
use App\Models\Chamado;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('notificacao-chamado.{userId}', function ($user, $userId) {
    // Permite que apenas o usuário com o ID correspondente escute
    return (int) $user->id_usuario === (int) $userId;
});

Broadcast::channel('chat-chamado.{chamadoId}', function ($user, $chamadoId) {
    // Permite que apenas usuários participantes do chamado escutem
     return Chamado::where('id_chamado', $chamadoId)
        ->where(function ($query) use ($user) {
            $query->where('usuario_solicitante_id', $user->id_usuario)
                  ->orWhere('usuario_atendente_id', $user->id_usuario);
        })
        ->exists();
});