<?php

use App\Http\Controllers\{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>roller, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>roller, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>roller, <PERSON><PERSON>io<PERSON>ontroller, <PERSON>ulacaoController, NotificacaoController, GrupoSetorController, RelatorioMetricaController};
use Illuminate\Support\Facades\Route;

Route::get('/', [LoginController::class, 'index'])->name('loginForm');
Route::get('/login', [LoginController::class, 'index'])->name('loginForm');
Route::post('/login', [LoginController::class, 'login'])->name('login');
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

Route::group(['middleware' => ['auth']], function () {
   
    Route::resource('home', HomeController::class);
    
    Route::resource('servic<PERSON>', <PERSON><PERSON>oController::class);

    Route::prefix('notificacoes')->group(function (){
        Route::get('/listagem', [NotificacaoController::class, 'index'])->name('notificacoes.index');
        Route::post('/marcar-como-lida/{id}', [NotificacaoController::class, 'marcarComoLida'])->name('notificacoes.marcarComoLida');
    });

    Route::get('/usuario/perfil', [UsuarioController::class,'perfil'])->name('usuario.perfil');
    Route::get('/usuario/listagem', [UsuarioController::class,'index'])->name('usuarios.index');
    Route::get('/usuario/{id}/editar',[UsuarioController::class,'edit'])->name('usuario.edit');
    Route::put('/usuario/{id}', [UsuarioController::class, 'update'])->name('usuario.update');


    Route::get('/setor/{tipoSetor}/listagem', [SetorController::class,'index'])->name('setores.index');
    Route::get('/setor/{tipoSetor}/{id}/editar',[SetorController::class,'edit'])->name('setor.edit');
    Route::put('/setor/{tipoSetor}/{id}', [SetorController::class, 'update'])->name('setor.update');

    Route::get('/chamado/selecionar-unidade', [ChamadoController::class,'selecionarUnidade'])->name('chamado.selecionarUnidade');
    Route::get('/chamado/novo/{setorAtendenteId}', [ChamadoController::class,'create'])->name('chamado.create');
    Route::post('/chamado/novo', [ChamadoController::class,'store'])->name('chamado.store');
    Route::get('/chamado/preview/{chamado:id_chamado}', [ChamadoController::class,'preview'])->name('chamado.preview');
    Route::get('/chamado/{chamado:id_chamado}', [ChamadoController::class,'show'])->name('chamado.show');
    Route::post('/chamado/{chamado:id_chamado}/mensagem', [ChamadoController::class,'storeMessage'])->name('chamado.storeMessage');
    Route::post('/chamado/{chamado:id_chamado}/atender', [ChamadoController::class,'atender'])->name('chamado.atender');
    Route::post('/chamado/{chamado:id_chamado}/fechamento', [ChamadoController::class,'fechamentoChamado'])->name('chamado.fechamento');
    
    Route::get('/regulacao', [RegulacaoController::class, 'index'])->name('regulacao.index');
    Route::get('/regulacao/{chamado:id_chamado}/editar', [RegulacaoController::class, 'edit'])->name('regulacao.edit');
    Route::put('/regulacao/{chamado:id_chamado}', [RegulacaoController::class, 'update'])->name('regulacao.update');

    Route::get('/grupoSetores/listagem', [GrupoSetorController::class, 'index'])->name('grupoSetores.index');
    Route::get('/grupoSetores/novo', [GrupoSetorController::class, 'create'])->name('grupoSetores.create');
    Route::post('/grupoSetores/novo', [GrupoSetorController::class, 'store'])->name('grupoSetores.store');
    Route::get('/grupoSetores/{grupoSetor:id}', [GrupoSetorController::class, 'show'])->name('grupoSetores.show');
    Route::get('/grupoSetores/{grupoSetor:id}/editar', [GrupoSetorController::class, 'edit'])->name('grupoSetores.edit');
    Route::put('/grupoSetores/{grupoSetor:id}', [GrupoSetorController::class, 'update'])->name('grupoSetores.update');
    Route::delete('/grupoSetores/{grupoSetor:id}', [GrupoSetorController::class, 'destroy'])->name('grupoSetores.destroy');

    Route::get('/get/setores/{campusId}', [SetorController::class,'getSetores'])->name('get.setores');
    Route::get('/get/setoresHabilitadosByUnidade/{unidadeId}', [SetorController::class, 'getSetoresHabilitadosByUnidade'])->name('get.setoresHabilitadosByUnidade');
    Route::get('/get/servicosBySetor/{setorId}/andUsuario/{usuarioId}', [ServicoController::class, 'getServicosBySetorAndUsuario'])->name('get.servicosBySetorAndUsuario');
    Route::get('/get/servicosOnlyByUnidade/{unidadeId}/andUsuario/{usuarioId}', [ServicoController::class, 'getServicosOnlyByUnidadeAndUsuario'])->name('get.servicosOnlyByUnidadeAndUsuario');
    Route::get('/get/atendentesBySetor/{setorId}', [UsuarioController::class, 'getAtendentesBySetor'])->name('get.atendentesBySetor');
    Route::get('/get/atendentesByUnidade/{unidadeId}', [UsuarioController::class, 'getAtendentesByUnidade'])->name('get.atendentesByUnidade');
    Route::get('/get/gruposByUnidade/{unidadeId}', [GrupoSetorController::class, 'getGruposByUnidade'])->name('get.gruposByUnidade');
    Route::get('/get/servicosByGrupo/{grupoId}/andUsuario/{usuarioId}', [ServicoController::class, 'getServicosByGrupoAndUsuario'])->name('get.servicosByGrupoAndUsuario');

    // Route::get('/relatorios', [RelatorioMetricaController::class, 'index'])->name('relatorios.index');
    Route::get('/relatorios', [RelatorioMetricaController::class, 'index'])->name('relatorios.index');

    Route::prefix('relatorio')->group(function () {
        // Route::get('/chamados', [RelatorioController::class, 'relatorioChamados'])->name('relatorio.chamados');
        Route::get('/servicos', [RelatorioController::class, 'relatorioServicos'])->name('relatorio.servicos');
    });
    
    Route::resource('perfil', PerfilController::class);
});
