

services:
  app:
    build:
      context: ./
      dockerfile: Dockerfile
    container_name: central-atendimento-app
    environment:
      - TZ=America/Recife
    restart: unless-stopped
    env_file:
      - ./.env
    working_dir: /var/www
    volumes:
      - "./:/var/www"
    ports:
      - "9000:9000"
    depends_on:
      - db
    #  - maildev
    networks:
      - centralatendimento-network

  nginx:
    image: nginx:1.27-alpine
    container_name: central-atendimento-nginx
    restart: unless-stopped
    env_file:
      - ./.env
    environment:
      TIMEZONE: America/Recife
    volumes:
      - "./:/var/www"
      - "./.docker/nginx/${APP_ENV}.conf:/etc/nginx/conf.d/default.conf"
    ports:
      - "8000:80"
      - "5173:5173"
    depends_on:
      - app
    networks:
      - centralatendimento-network

  db:
    container_name: central-atendimento-db
    image: postgres:15-alpine
    restart: unless-stopped
    tty: true
    env_file:
      - ./.env
    environment:
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_DATABASE}
    volumes:
      - ".docker/postgres/data:/var/lib/postgresql/data"
    ports:
      - "5432:5432"
    networks:
      - centralatendimento-network

  soketi:
    container_name: central-atendimento-websocket
    image: 'quay.io/soketi/soketi:latest-16-alpine'
    restart: unless-stopped
    env_file:
      - ./.env
    environment:
        SOKETI_DEBUG: '1'
        # SOKETI_METRICS_SERVER_PORT: '9601'
        SOKETI_APP_KEY: ${PUSHER_APP_KEY}
        SOKETI_APP_SECRET: ${PUSHER_APP_SECRET}
    ports:
        - '${SOKETI_PORT:-6001}:6001'
        # - '${SOKETI_METRICS_SERVER_PORT:-9601}:9601' --apenas para desenvolvimento
    networks:
      - centralatendimento-network

networks:
  centralatendimento-network:
    name: centralatendimento-network
    driver: bridge
