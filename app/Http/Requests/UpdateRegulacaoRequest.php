<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateRegulacaoRequest extends FormRequest
{
    /**
     * Rotas permitidas para redirecionamento após o update.
     */
    public const REDIRECT_ROUTES = ['home.index', 'regulacao.index'];

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function prepareForValidation()
    {
        $chamado = $this->route('chamado');
        //Se a regulação for feita para outra unidade, limpa os campos de setor, serviço e atendente
        if($this->unidadeId != $chamado->unidade_atendente_id){
            $this->merge([
                'setorId' => null,
                'grupoId' => null,
                'servico_id' => null,
                'atendenteId' => null,
            ]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $chamado = $this->route('chamado');

        return [
            'unidadeId' => ['required', 'exists:habilitar_unidades_organizacionais,unidade_id'],
            'observacao' => [
                'required_with:imagemEncaminhamento',
                'nullable',
                'string'],
            'imagemEncaminhamento' => ['nullable', 'image', 'mimes:jpeg,png,jpg', 'max:1024'],
            'setorId' => [
                'nullable',
                'exists:pgsql_sgs.setores,id_setor'],
            'grupoId' => ['nullable', 'exists:grupo_setores,id'],
            'servico_id' => [
                Rule::requiredIf($this->unidadeId == $chamado->unidade_atendente_id),//Se a regulação for feita dentro da mesma unidade, o serviço é obrigatorio
                'nullable', 
                'exists:servicos,id'],
            'redirect_to' => ['required', Rule::in(self::REDIRECT_ROUTES)],
            'atendenteId' => ['nullable', 'exists:pgsql_sgs.usuarios,id_usuario'],
            'tipoEncaminhamento' => ['required', Rule::in(['encaminhar', 'encaminharEIniciar'])],
        ];
    }

    public function attributes()
    {
        return [
            'unidadeId' => 'unidade',
            'setorId' => 'setor',
            'grupoId' => 'grupo',
            'servico_id' => 'serviço',
            'observacao' => 'informação adicional',
            'imagemEncaminhamento' => 'imagem',
            'redirect_to' => 'rota de redirecionamento',
            'atendenteId' => 'atendente',
        ];
    }
}
