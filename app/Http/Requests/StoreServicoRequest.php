<?php

namespace App\Http\Requests;

use App\Enums\PessoasCategoriaEnum;
use Gate;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class StoreServicoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $categoriasValidas = array_column(PessoasCategoriaEnum::cases(), 'value');

        return [
            'nome' => ['required', 'string', 'max:255'],
            'descricao' => ['required', 'string', 'max:255'],
            'unidade_responsavel' => ['required', 'exists:habilitar_unidades_organizacionais,unidade_id'],
            'setor_responsavel' => ['nullable', 'exists:habilitar_setores,setor_id'],
            'criticidade' => ['required', Rule::in([1,2,3,4])],
            'status' => ['required', Rule::in([0,1])],
            'categorias_permitidas' => ['nullable', 'array'],
            'categorias_permitidas.*' => ['required', 'string', Rule::in($categoriasValidas)],
            'grupo_responsavel' => ['nullable', 'exists:grupo_setores,id'],
        ];
    }

    public function attributes()
    {
        return [
            'nome' => 'nome do serviço',
            'descricao' => 'atividade realizada',
            'unidade_responsavel' => 'unidade responsável',
            'setor_responsavel' => 'setor responsável',
            'categorias_permitidas' => 'categorias permitidas',
            'grupo_responsavel' => 'grupo responsável',
        ];
    }

    public function after()
    {
        return [
            function (Validator $validator) {
                $setor = $this->input('setor_responsavel');
                $grupo = $this->input('grupo_responsavel');
                
                if($setor && $grupo) {
                    $mensagem = 'Setor e Grupo não podem ser informados simultaneamente. Escolha no máximo uma das opções.';
                    $validator->errors()->add('setor_responsavel', $mensagem);
                    $validator->errors()->add('grupo_responsavel', $mensagem);
                }
            }
        ];
    }
}
