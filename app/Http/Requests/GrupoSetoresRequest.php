<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GrupoSetoresRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'nome' => ['required', 'string', 'max:255'],
            'unidade_responsavel_id' => ['required', 'exists:habilitar_unidades_organizacionais,unidade_id'],
            'habilitado' => ['required', Rule::in([0,1])],
            'setores' => ['required', 'array', 'min:2'],
            'setores.*.setor_id' => ['required', 'exists:habilitar_setores,setor_id'],
            'setores.*.peso' => ['required', 'integer', 'min:1'],
        ];
    }

    public function attributes()
    {
        return [
            'nome' => 'nome do grupo',
            'unidade_responsavel_id' => 'unidade responsável',
            'habilitado' => 'status do grupo',
            'setores' => 'setores',
            'setores.*.setor_id' => 'setor',
            'setores.*.peso' => 'peso',
        ];
    }
}
