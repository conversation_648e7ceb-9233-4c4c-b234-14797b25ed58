<?php

namespace App\Http\ViewComposers;

use Illuminate\View\View;
use App\Models\Notificacao;

class NotificacaoComposer
{
    public function compose(View $view)
    {
        $notificacoes = Notificacao::where('usuario_id', auth()->user()->id_usuario)
            ->where('visualizada', false)
            ->count();

        $usuarioId = auth()->user()->id_usuario;
        // Passa as variáveis para a view
        $view->with(compact('notificacoes', 'usuarioId'));
    }
}
