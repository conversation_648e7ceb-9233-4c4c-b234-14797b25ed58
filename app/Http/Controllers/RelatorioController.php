<?php

namespace App\Http\Controllers;

use Barryvdh\DomPDF\Facade\Pdf;
use App\Services\Relatorios\Relatorio_chamados_servicos;
use App\Services\Relatorios\Relatorio_servicos_servicos;
use App\Util\Notificacao;

use Illuminate\Http\Request;

use Illuminate\Validation\Rules\Exists;

class RelatorioController extends Controller
{


    public function __construct(private Relatorio_chamados_servicos $Relatorio_chamados_servicos, private Relatorio_servicos_servicos $relatorio_servicos_servicos)
    {

    }

    public function relatorioChamados(Request $request)
    {
        if(!session("filtros_form_chamados")){
            Notificacao::mensagem('error', 'Não há filtros definidos para gerar o relatório.');
            return redirect()->route('home.index');
        }

        $filtros = session("filtros_form_chamados");
        $dataAbertura = $request->input('data_inicio');

        // Se houver data de abertura
        if ($dataAbertura) {
            // Se status for diferente de 'todos', força ordenação por data e iguala data início e fim
            if ($filtros['chamado_status'] !== 'Todos') {
                $ordenacao = 'data';
                $filtros['data_inicio'] = $dataAbertura;
                $filtros['data_fim'] = $dataAbertura;
            } else {
                // Se status for 'todos', mantém as datas informadas mas não permite ordenar por data
                $ordenacao = 'status';
                $filtros['data_inicio'] = $dataAbertura;
                $filtros['data_fim'] = $request->input('data_fim') ?? $dataAbertura;
            }
        } else {
            // Se não houver data, usa a ordenação do request
            $ordenacao = $request->input('ordenar_por') === 'data' ? "data" : "status";
            $filtros['data_inicio'] = null;
            $filtros['data_fim'] = null;
        }

        $resultado = $this->Relatorio_chamados_servicos->gerarRelatorioChamados("relatorios.relatorio-chamados", $ordenacao, $filtros);
        
        if(is_array($resultado) && isset($resultado['status']) && $resultado['status'] === 'error') {
            Notificacao::mensagem('error', $resultado['message']);
            return redirect()->route('home.index');
        }
        
        return $resultado->stream('relatorio-chamados.pdf', [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="relatorio-chamados.pdf"',
            'Cache-Control' => 'public, must-revalidate, max-age=0',
            'target' => '_blank'
        ]);
    }

    public function relatorioServicos(Request $request)
    {
        // Verificar se há filtros na sessão
        if(!session("filtros_form_servicos")){
            session(['filtros_form_servicos' => ['servico_status' => 1]]);
        }

        $filtros = session("filtros_form_servicos");
        $ordenacao = $request->input('ordenar_por_status') && $filtros['servico_status'] == 'todos' 
            ? "status" 
            : "criticidade";

        // Gerar o relatório usando o serviço
        $resultado = $this->relatorio_servicos_servicos->gerarRelatorioServicos(
            "relatorios.relatorio-servicos", 
            $ordenacao, 
            $filtros
        );
        
        // Verificar se o resultado é um array de erro
        if(is_array($resultado) && isset($resultado['status']) && $resultado['status'] === 'error') {
            Notificacao::mensagem('error', $resultado['message']);
            return redirect()->route('servicos.index');
        }
        
        // Se não for um erro, é um PDF que pode ser transmitido
        return $resultado->stream('relatorio-servicos.pdf', [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="relatorio-servicos.pdf"',
            'Cache-Control' => 'public, must-revalidate, max-age=0',
            'target' => '_blank'
        ]);
    }

}
