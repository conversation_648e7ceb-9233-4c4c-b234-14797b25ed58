<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Chamado;
use App\Models\Servico;
use App\Models\SGS\Usuario;
use App\Models\SGS\Setor;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class RelatorioMetricaController extends Controller
{
    public function index(Request $request)
    {
        
        $chamadosQuery = Chamado::with(['usuarioSolicitante', 'usuarioAtendente', 'servico', 'setorSolicitante']);

        if ($request->filled('status')) {
            $chamadosQuery->where('status', $request->status);
        }
         
        if ($request->filled('usuario_id')) {
            $chamadosQuery->where('usuario_solicitante_id', $request->usuario_id);
        }

        if ($request->filled('setor_id')) {
            $chamadosQuery->where('setor_solicitante_id', $request->setor_id);
        }

        if ($request->filled('atendente_id')) {
            $chamadosQuery->where('usuario_atendente_id', $request->atendente_id);
        }

        if ($request->filled('servico_id')) {
            $chamadosQuery->where('servico_id', $request->servico_id);
        }
        
        $chamados = $chamadosQuery->with('servico')->paginate(10);

        $usuarios = Usuario::find(auth()->id());
        $setores = Cache::remember('metricas_setores', 3600, function () {
            return Setor::select('id_setor as id', 'ds_nomesetor as nome')->orderBy('ds_nomesetor')->get();
        });

        $servicos = Cache::remember('metricas_servicos', 3600, function () {
            return Servico::select('id', 'nome')->orderBy('nome')->get();
        });

        $atendentes = Cache::remember('metricas_atendentes', 3600, function () {
            return Usuario::join('pessoas', 'usuarios.ds_cpf', '=', 'pessoas.ds_cpf')
                        ->select('usuarios.id_usuario as id', 'pessoas.ds_nomepessoa as nome')
                        ->orderBy('pessoas.ds_nomepessoa')
                        ->get();
        });

        return view('relatorios.relatorios', compact(
            'chamados',
            'usuarios',
            'setores',
            'servicos',
            'atendentes',
            'request' 
        ));
    }
}