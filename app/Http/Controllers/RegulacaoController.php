<?php

namespace App\Http\Controllers;

use App\Http\Requests\UpdateRegulacaoRequest;
use App\Models\{Chamado, HabilitarUnidadeOrganizacional};
use App\Services\{SGS\SetoresServicos, ChamadoServicos};
use App\Util\{Notificacao, TratarExcecao};
use Exception;
use App\Services\NotificacaoServicos;
use Illuminate\Support\Facades\Gate;

class RegulacaoController extends Controller
{
    public function __construct(
        protected SetoresServicos $setoresServicos,
        protected ChamadoServicos $chamadoServicos,
        protected NotificacaoServicos $notificacaoServicos,
    )
    {}

    public function index()
    {
        if(Gate::denies('regulacao-visualizar')){
            abort(403);
        }
        $id = auth()->user()->unidade_id;
        $chamados = Chamado::with('usuarioSolicitante','usuarioSolicitante.pessoa','setorSolicitante')
            ->where('unidade_atendente_id', $id)
            ->where('servico_id', NULL)
            ->where('usuario_atendente_id', NULL)
            ->where('status', 'Aberto')
            ->orderBy('created_at', 'desc')->paginate();

        $unidade = HabilitarUnidadeOrganizacional::where('unidade_id', $id)->first();
        if (!$unidade) {
            Notificacao::mensagem('error', 'Unidade Organizacional não encontrada');
            return redirect()->back();
        }
        return view('regulacao.index', compact('chamados', 'unidade'));
    }

    public function edit(Chamado $chamado)
    {
        if(Gate::denies('regulacao-visualizar')){
            abort(403);
        }
        if(isset($chamado->servico_id) || isset($chamado->usuario_atendente_id) || isset($chamado->setor_atendente_id) || ($chamado->status != 'Aberto')){
            Notificacao::mensagem('error', 'Chamado já direcionado.');
            return redirect()->back();
        }
        $unidade = HabilitarUnidadeOrganizacional::where('unidade_id', $chamado->unidade_atendente_id)->firstOrFail();
        $chamado->temMensagens = $chamado->mensagens()->skip(1)->exists();
        
        return view('regulacao.edit', compact('chamado', 'unidade'));
    }

    public function update(UpdateRegulacaoRequest $request, Chamado $chamado)
    {
        try {
            $this->chamadoServicos->encaminharChamado($chamado,$request);

            if($request->tipoEncaminhamento == 'encaminharEIniciar'){
                $usuarioId = auth()->user()->id_usuario;
                $this->chamadoServicos->iniciarAtendimento($chamado, $usuarioId);

                Notificacao::mensagem('success', 'Você agora é o atendente deste chamado.', 'Atendimento iniciado');
                return redirect()->route('chamado.show',$chamado->id_chamado);

            } else{

                Notificacao::mensagem('success', 'Chamado encaminhado com sucesso.');

                //Cria notificação de encaminhamento de chamado
                $this->notificacaoServicos->criaNotificacao( 
                        usuario_id: $chamado->usuario_solicitante_id,
                        id_chamado: $chamado->id_chamado,
                        mensagem: 'Seu chamado foi encaminhado para '.$chamado->setorAtendente->ds_nomesetor,
                        tipo: 'Atualização de chamado',
                    );

                return redirect()->route($request->redirect_to);
            } 
            
        } catch (Exception $e) {
            TratarExcecao::excecao($e);
            return redirect()->back()->withInput();
        }
    }
}
