<?php
namespace App\Http\Controllers;
use Illuminate\Http\Request;
use Symfony\Component\CssSelector\Parser\Shortcut\ElementParser;
use App\Services\{ChamadoServicos, SGS\SetoresServicos};
use App\Models\Servico;
use Hamcrest\Core\Set;
class HomeController extends Controller
{
    protected $chamadoServicos ;
    public function __construct(ChamadoServicos $chamadoServicos, protected SetoresServicos $setoresServicos)
    {
        $this->chamadoServicos =$chamadoServicos;
    }
    public function index(Request $request)
    {
        $consultaPadrao = [
            'chamado_titulo' => null,
            'chamado_servico' => 'todos',
            'chamado_status' => 'Aberto',
            'chamado_direcionamento'=> 'direcionado_setor',
            'ordenacao' => 'data',
            'chamado_periodo_inicio' => null,
            'chamado_periodo_fim' => null,
            'chamado_setor_solicitante' => null,
            'chamado_usuario_solicitante' => null,
            'chamado_setor_atendente' => null,
            'chamado_unidade_atendente' => null,
        ];

        
        $filtro = FormSaveInSession($request->all(), 'chamados',$consultaPadrao);

        // 1. Executa a consulta principal de listagem (COM o filtro de serviço)
        $chamados = $this->chamadoServicos->localizarChamadosPeloFiltro($filtro);

        // 2. Executa a consulta MAIS AMPLA para obter todos os serviços válidos no contexto
        $filtroParaServicos = $filtro; 
        $filtroParaServicos['chamado_servico'] = 'todos'; 
       
        $chamadosParaServicos = $this->chamadoServicos->localizarChamadosPeloFiltro($filtroParaServicos);

        // 3. Preenche a variável de serviços com base nos IDs do conjunto mais amplo
        $servicos = Servico::query()
            ->whereIn('id', $chamadosParaServicos->pluck('servico_id')->unique())
            ->get();
        
            
        return view('home.index', compact('filtro','chamados','servicos'));
    }
}