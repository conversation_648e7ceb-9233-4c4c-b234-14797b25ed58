<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Notificacao;
use App\Services\NotificacaoServicos;

class NotificacaoController extends Controller
{
    public function __construct(private NotificacaoServicos $notificacaoServicos)
    {
        $this->notificacaoServicos = $notificacaoServicos;
    }
    
    public function index(Request $request)
    {
        $notificacao_status = $request->input('notificacao_status') ?? '0';

        $notificacoes = Notificacao::with('chamado:id_chamado,titulo,servico_id')
            ->where('usuario_id', auth()->user()->id_usuario)
            ->where('visualizada', $notificacao_status)
            ->orderBy('created_at', 'desc') 
            ->paginate(15)
            ->appends(['notificacao_status' => $notificacao_status]); 

        //Altera o status das notificacoes selecionadas que estão como não visualizadas para visualizadas
        $this->notificacaoServicos->alteraStatusNotificacao($notificacoes->where('visualizada', 0)->pluck('id')->toArray());
         
        return view('notificacoes.index', compact('notificacoes'));
    }

    public function marcarComoLida(Request $request)
    {
        $notificacao = Notificacao::find($request->id);
        $this->notificacaoServicos->alteraStatusNotificacao($notificacao->pluck('id')->toArray());
        return response()->json(['success' => true]);
    }
}
