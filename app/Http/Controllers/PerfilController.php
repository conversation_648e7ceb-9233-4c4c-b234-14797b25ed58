<?php

namespace App\Http\Controllers;

use App\Services\{PerfilServicos, PermissaoServicos};
use App\Util\Notificacao;
use App\Util\TratarExcecao;
use Exception;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;


define('ROTAS', [
    "perfil" => 'perfil.index'
]);
class PerfilController extends Controller
{

    protected $view_adm_perfil_manutencao = "administrador.perfil.manutencao";

    public function __construct(private PerfilServicos $perfilServicos, private PermissaoServicos $permissaoServicos)
    {
    }
    
    public function index(): View
    {
        if(Gate::denies('perfis-listar')){
            abort(403);
        }

        $perfis = $this->perfilServicos->obterTodosPaginado(10);

        return view('administrador.perfil.index', compact('perfis'));
    }
    
    public function create(): View
    {
        if(Gate::denies('perfis-criar')){
            abort(403);
        }
        $atualizacao = false;
        $visualizacao = false;
        $rota = route('perfil.store');
        $permissoes = $this->permissaoServicos->obterTodos();
        $perfil = null;

        return view($this->view_adm_perfil_manutencao, compact('perfil','permissoes','atualizacao','rota','visualizacao'));
    }
    
    public function store(Request $request)
    {
        if(Gate::denies('perfis-criar')){
            abort(403);
        }
        $request->validate([
            'nome' => ['required', 'max:100'],
            'descricao' => ['max:255'],
        ]);

        try 
        {
            
            $dadosPerfil = $request->only('nome', 'descricao');
            $dadosPermissoes = $request->permissoes;

            $this->perfilServicos->salvar($dadosPerfil, $dadosPermissoes);

            Notificacao::mensagem('success', 'Perfil cadastrado com sucesso.');

            return redirect()->route(ROTAS['perfil']);

        }
        catch (Exception $ex)
        {
            TratarExcecao::excecao($ex);

            return redirect()->back()->withInput();
        }    
    }

    public function edit(string $id): View
    {
        if(Gate::denies('perfis-editar')){
            abort(403);
        }
        $atualizacao = true;
        $visualizacao = false;
        $rota = route('perfil.update', $id);
        $permissoes = $this->permissaoServicos->obterTodos();
        $perfil = $this->perfilServicos->obterPorId($id);

        return view($this->view_adm_perfil_manutencao, compact('perfil','permissoes','atualizacao','rota','visualizacao'));
    }

    public function show(string $id): View
    {
        if(Gate::denies('perfis-visualizar')){
            abort(403);
        }
        $atualizacao = false;
        $visualizacao = true;
        $rota = null;
        $permissoes = $this->permissaoServicos->obterTodos();
        $perfil = $this->perfilServicos->obterPorId($id);

        return view($this->view_adm_perfil_manutencao, compact('perfil','permissoes','atualizacao','rota','visualizacao'));
    }


    public function update(Request $request, string $id)
    {
        if(Gate::denies('perfis-editar')){
            abort(403);
        }
        $request->validate([
            'nome' => ['required', 'max:100'],
            'descricao' => ['max:255'],
        ]);

        try 
        {
            $dadosPerfil = $request->only('nome', 'descricao');
            $dadosPermissoes = $request->permissoes;

            $this->perfilServicos->alterar($id, $dadosPerfil, $dadosPermissoes);

            Notificacao::mensagem('success', 'Perfil atualizado com sucesso.');

            return redirect()->route(ROTAS['perfil']);

        }
        catch (Exception $ex)
        {
            TratarExcecao::excecao($ex);

            return redirect()->back()->withInput();
        }               
    }
  
     public function destroy(string $id)
     {
        if(Gate::denies('perfis-editar')){
            abort(403);
        }

        try 
        {
            $this->perfilServicos->excluir($id);
            Notificacao::mensagem('success', 'Perfil excluído com sucesso.');
            return redirect()->route(ROTAS['perfil']);

        }
        catch (Exception $ex)
        {
            TratarExcecao::excecao($ex);
            return redirect()->back()->withInput();
        }
     }
}
