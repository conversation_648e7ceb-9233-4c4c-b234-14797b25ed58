<?php

namespace App\Http\Controllers;

use App\Models\{Chamado, ChamadoTombo, HabilitarUnidadeOrganizacional, Servico, SGS\Campus, ChamadoMensagem};
use App\Services\{ChamadoHistoricoServicos, HabilitarUnidadesOrganizacionaisServicos, NotificacaoServicos ,SGS\SetoresServicos, ServicoServicos, ChamadoServicos, ChamadoTomboServicos};
use App\Util\{Notificacao, TratarExcecao};
use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ChamadoController extends Controller
{
    /**
     * Display a listing of the resource.
     */

    public function __construct(
        protected SetoresServicos $setoresServicos,
        protected HabilitarUnidadesOrganizacionaisServicos $habilitarUnidadesOrganizacionaisServicos,
        protected ChamadoHistoricoServicos $chamadoHistoricoServicos,
        protected ChamadoServicos $chamadoServicos,
        protected ChamadoTomboServicos $chamadoTomboServicos,
        protected NotificacaoServicos $notificacaoServicos,
        )
    {}
    public function selecionarUnidade()
    {
        $categorias = auth()->user()->categorias->pluck('value')->toArray();
        $unidades = $this->habilitarUnidadesOrganizacionaisServicos->getHabilitadaPorCategoria($categorias);
        return view('chamado.selecionar-unidade', compact('unidades'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create($unidadeAtendenteId)
    {
        $unidadeAtendente = HabilitarUnidadeOrganizacional::where('unidade_id', $unidadeAtendenteId)->firstOrFail();

        $categorias = auth()->user()->categorias->pluck('value')->toArray();

        $servicos = Servico::servicoAllByUnidadeAndCategorias($unidadeAtendente->unidade_id, $categorias)->select('servicos.id', 'servicos.nome')->orderBy('nome')->get();

        $campi = Campus::all();
        $usuario = auth()->user();
        
        return view('chamado.create', compact('unidadeAtendente', 'campi', 'usuario', 'servicos'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, ServicoServicos $servicoServicos)
    {
        
        $request->validate([
            'servico' => 'nullable|exists:servicos,id',
            'titulo' => 'required|string|max:255',
            'descricao' => 'required|string',
            'unidadeAtendenteId' => 'required|exists:habilitar_unidades_organizacionais,unidade_id',
            'setorSolicitanteId' => 'required|exists:pgsql_sgs.setores,id_setor',
            'telefone' => 'required|string|max:20',
            'tombos' => 'nullable|array',
            'tombos.*' => ['nullable', 'regex:/^\d+$/'],
            'imagem' => 'nullable|image|mimes:jpeg,png,jpg|max:1024',
        ]);
        
        try {
            // Upload da imagem usando o serviço
            $caminhoArquivo = null;
            if ($request->hasFile('imagem')) {
                $caminhoArquivo = $this->chamadoServicos->uploadImagemChamado($request->imagem, $request->servico, $request->siglaSetor, $request->tombos);
            }

            $setorAtendente = $servicoServicos->getSetorResponsavelId($request->servico ?? null);
    
            $chamado =Chamado::create([
                'servico_id' => $request->servico ?? null,
                'titulo' => $request->titulo,
                'descricao' => $request->descricao,
                'usuario_solicitante_id' => $request->usuarioId,
                'unidade_atendente_id' => $request->unidadeAtendenteId,
                'setor_atendente_id' => $setorAtendente,
                'setor_solicitante_id' => $request->setorSolicitanteId,
                'telefone_contato' => $request->telefone,
                'caminho_arquivo' => $caminhoArquivo ?? null, 
            ]);

            $this->chamadoHistoricoServicos->create($chamado->toArray(), ['atividade' => 'Criado']);

            ChamadoMensagem::create([
                'chamado_id' => $chamado->id_chamado,
                'mensagem' => $chamado->descricao,
                'usuario_id' => $chamado->usuario_solicitante_id,
                'tipo_usuario' => 'Solicitante',
                'tipo_mensagem' => 'Descricao',
                'caminho_arquivo' => $chamado->caminho_arquivo,
            ]);
    
            if(!empty($request->tombos) && !in_array(null, $request->tombos)){
                $tombosData = array_map(fn($tombo) => [
                    'chamado_id' => $chamado->id_chamado,
                    'nm_tombo' => $tombo,
                ], $request->tombos);
    
                ChamadoTombo::insert($tombosData);
            }

    
    
            Notificacao::mensagem('success', 'Chamado criado com sucesso.');
            return redirect()->route('home.index');
            
        } catch (Exception $e) {
            TratarExcecao::excecao($e);
            return redirect()->back()->withInput();
        }

    }

    public function preview(Chamado $chamado)
    {
        $statusConfig = [
            'Aberto' => ['class' => 'alert-secondary', 'icon' => 'fa-info-circle'],
            'Em andamento' => ['class' => 'alert-info', 'icon' => 'fa-spinner fa-spin'],
            'Parado' => ['class' => 'alert-warning', 'icon' => 'fa-pause-circle'],
            'Concluido' => ['class' => 'alert-success', 'icon' => 'fa-check-circle'],
            'Solicitado fechamento' => ['class' => 'alert-danger', 'icon' => 'fa-pause-circle'],
        ];
        
        $chamado->infoAdicional = $chamado->mensagens()->where('tipo_mensagem', 'Informacao adicional')->oldest()->first();

        $chamado->atendimentoIniciado = $this->chamadoServicos->atendimentoIniciado($chamado);

        $chamado->statusClass = $statusConfig[$chamado->status]['class'];
        $chamado->statusIcon = $statusConfig[$chamado->status]['icon'];


        return view('chamado.preview', compact('chamado'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Chamado $chamado)
    {
        $statusConfig = [
            'Aberto' => 'bg-secondary',
            'Em andamento' => 'bg-info',
            'Parado' => 'bg-warning',
            'Concluido' => 'bg-success',
            'Solicitado fechamento' => 'bg-danger',
        ];

        $chamado->statusClass = $statusConfig[$chamado->status];
        $chamado->usuarioAtual = $chamado->usuario_solicitante_id == auth()->user()->id_usuario ? 'Solicitante' : 'Atendente';
        
        $mensagens = $chamado->mensagens()->with('usuario', 'usuario.pessoa')->get();
        $ultimaSolicitacaoFechamento = $mensagens->where('tipo_mensagem', 'Fechamento solicitado')->sortByDesc('created_at')->first()->id ?? null;
        return view('chamado.show', compact('chamado', 'mensagens', 'ultimaSolicitacaoFechamento'));
    }

    public function storeMessage(Request $request, Chamado $chamado)
    {
        $request->validate([
            'mensagem' => 'required|string',
            'imagem' => 'nullable|image|mimes:jpeg,png,jpg|max:1024',
        ]);
        $relacao = $chamado->setorAtendente ? 'setorAtendente' : 'setorUnidadeAtendente';

        try {
            // Upload da imagem usando o serviço
            $caminhoArquivo = null;
            if ($request->hasFile('imagem')) {
                // Obtém os tombos associados ao chamado
                $tombos = $this->chamadoTomboServicos->getTombosByChamadoId($chamado->id_chamado);
                
                $caminhoArquivo = $this->chamadoServicos->uploadImagemChamado($request->imagem, $chamado->servico_id, $chamado->$relacao->sg_setor, $tombos);
            }
            $usuarioId = auth()->user()->id_usuario;
            $tipoUsuario = $usuarioId == $chamado->usuario_solicitante_id ? 'Solicitante' : 'Atendente';
            $mensagem = ChamadoMensagem::create([
                'chamado_id' => $chamado->id_chamado,
                'mensagem' => $request->mensagem,
                'usuario_id' => $usuarioId,
                'tipo_usuario' => $tipoUsuario,
                'caminho_arquivo' => $caminhoArquivo ?? null,
            ]);


            // Eager load da relação para evitar N+1
            $mensagem->load('usuario.pessoa');

            // Buscar a última solicitação de fechamento, se necessário
            $ultimaSolicitacaoFechamento = ChamadoMensagem::where('chamado_id', $chamado->id_chamado)
                ->where('mensagem', 'like', '%Solicitado fechamento%')
                ->latest()
                ->value('id');

            // Montagem do payload final
            $payload = [
                        'mensagem' => [
                            'id' => $mensagem->id ?? null,
                            'mensagem' => $mensagem->mensagem ?? '',
                            'caminho_arquivo' => $mensagem->caminho_arquivo ?? '',
                            'tipo_usuario' => $mensagem->tipo_usuario ?? '',
                            'created_at' => $mensagem->created_at->format('d/m/Y, H:i'),
                            'usuario' => [
                                'id' => $mensagem->usuario->id_usuario ?? null,
                                'nome' => $mensagem->usuario->pessoa->nome ?? 'Sistema',
                            ],
                        ],
                        'chamado' => [
                            'id' => $chamado->id_chamado,
                            'status' => $chamado->status,
                            'statusClass' => $chamado->statusClass, // necessário para atualizar o badge
                            'ultimaAtualizacao' => $chamado->updated_at->format('d/m/Y, H:i'), // para atualizar a data
                            'mostrarEncaminhar' => $chamado->usuario_atendente_id, // botão encaminhar
                            'mostrarFinalizar' => $chamado->status == 'Em andamento' && in_array(auth()->id(), [$chamado->usuario_solicitante_id, $chamado->usuario_atendente_id]),
                            'acaoFinalizar' => $chamado->usuarioAtual == 'Solicitante' ? 'finalizar' : 'solicitar', // para o botão finalizar
                            'atendenteId' => $chamado->usuario_atendente_id ?? null,
                        ],
                        'ultimaSolicitacaoFechamento' => $ultimaSolicitacaoFechamento,
                    ];

                    

            //Cria notificação de atualização de chamado
            $this->notificacaoServicos->criaNotificacao( 
                usuario_id: $tipoUsuario == 'Solicitante' ? $chamado->usuario_atendente_id : $chamado->usuario_solicitante_id,
                id_chamado: $chamado->id_chamado,
                mensagem: 'Nova mensagem no chamado',
                tipo: 'Atualização de chamado',
            ); 
            //Atualiza o chat para o usuário que recebe a mensagem
            $this->notificacaoServicos->atualizaChat($payload);

            // Se for AJAX, retorna JSON
            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'mensagem' => $payload['mensagem']
                ]);
            }

            return redirect()->back();

        } catch (Exception $e) {
            TratarExcecao::excecao($e);
            return redirect()->back()->withInput();
        }
    }

    public function atender(Chamado $chamado)
    {
        try {
            $usuarioId = auth()->user()->id_usuario;
            
            $this->chamadoServicos->iniciarAtendimento($chamado, $usuarioId);
            
            Notificacao::mensagem('success', 'Você agora é o atendente deste chamado.', 'Atendimento iniciado');
            return redirect()->route('chamado.show',$chamado->id_chamado); 
        } catch (Exception $e) {
            TratarExcecao::excecao($e);
            return redirect()->back()->withInput();
        }
    }

    public function fechamentoChamado(Request $request, Chamado $chamado)
    {
        $request->validate([
            'acao' => 'required|in:solicitar,finalizar,continuar',
        ]);

        try {
            $usuario = auth()->user();
            $updateData = [];
            switch ($request->acao) {
                case 'solicitar':
                    $updateData['status'] = 'Solicitado fechamento';
                    $mensagem = "{$usuario->pessoa->nome} solicitou o fechamento do chamado";
                    $atividade = "Fechamento solicitado";
                    break;
                case 'finalizar':
                    $updateData['status'] = 'Concluido';
                    $updateData['concluded_at'] = now();
                    $mensagem = "Fechamento do chamado feito por {$usuario->pessoa->nome}";
                    $atividade = "Fechamento realizado";
                    break;
                case 'continuar':
                    $updateData['status'] = 'Em andamento';
                    $mensagem = "Fechamento do chamado cancelado por {$usuario->pessoa->nome}";
                    $atividade = "Fechamento cancelado";
                    break;
                default:
                    Notificacao::mensagem('error', 'Ação inválida');
                    return redirect()->back();
                }


                //Cria notificação para atualizações de chamado feitas pela interface do chat
                $usuarioNotificado = $chamado->usuario_atendente_id == auth()->user()->id_usuario ? $chamado->usuario_solicitante_id : $chamado->usuario_atendente_id;
                $mensagem = "{$atividade} por {$usuario->pessoa->nome}";
                $this->notificacaoServicos->criaNotificacao( 
                    usuario_id: $usuarioNotificado,
                    id_chamado: $chamado->id_chamado,
                    mensagem: $mensagem,
                    tipo: 'Atualização de chamado',
                );
               
                
                $chamado->update($updateData);
                $this->chamadoHistoricoServicos->create($chamado->toArray(), ['atividade' => $atividade]);
                $mensagemModel = ChamadoMensagem::create([
                    'chamado_id' => $chamado->id_chamado,
                    'mensagem' => $mensagem,
                    'usuario_id' => $usuario->id_usuario,
                    'tipo_usuario' => 'Sistema',
                    'tipo_mensagem' => $atividade,
                ]);

                // eager load para ter os dados do usuário/pessoa prontos
                $mensagemModel->load('usuario.pessoa');

                // Buscar a última solicitação de fechamento, se necessário
                $ultimaSolicitacaoFechamento = ChamadoMensagem::where('chamado_id', $chamado->id_chamado)
                    ->where('mensagem', 'like', '%Fechamento solicitado por%')
                    ->latest()
                    ->value('id');

                // Montagem do payload padronizado (mesma estrutura que mensagens normais)
                $payload = [
                    'mensagem' => [
                        'id' => $mensagemModel->id ?? null,
                        'mensagem' => $mensagemModel->mensagem ?? '',
                        'caminho_arquivo' => $mensagemModel->caminho_arquivo ?? '',
                        'tipo_usuario' => $mensagemModel->tipo_usuario ?? '',
                        'created_at' => $mensagemModel->created_at->format('d/m/Y, H:i'),
                        'usuario' => [
                            'id' => $mensagemModel->usuario->id_usuario ?? null,
                            'nome' => $mensagemModel->usuario->pessoa->nome ?? 'Sistema',
                        ],
                    ],
                    'chamado' => [
                        'id' => $chamado->id_chamado,
                        'status' => $chamado->status,
                        'ultimaAtualizacao' => $chamado->updated_at->format('d/m/Y, H:i'), // para atualizar a data
                        'mostrarEncaminhar' => $chamado->usuario_atendente_id , // botão encaminhar
                        'mostrarFinalizar' => $chamado->status == 'Em andamento' && in_array(auth()->id(), [$chamado->usuario_solicitante_id, $chamado->usuario_atendente_id]),
                        'acaoFinalizar' => $chamado->usuarioAtual == 'Solicitante' ? 'finalizar' : 'solicitar', // para o botão finalizar
                        'atendenteId' => $chamado->usuario_atendente_id ?? null,
                        'atendenteNome' => $chamado->usuarioAtendente->pessoa->nome ?? null,
                        'unidadeAtendenteNome' => $chamado->setorUnidadeAtendente->ds_nomesetor ?? null,
                        'servicoNome' => $chamado->servico->nome ?? null,
                    ],
                    'ultimaSolicitacaoFechamento' => $ultimaSolicitacaoFechamento,
                ];

                
                $this->notificacaoServicos->atualizaChat($payload);
                
                if ($request->ajax() || $request->wantsJson()) {
                    return response()->json($payload);
                }else{
                    return redirect()->back();
                }
        } catch (Exception $e) {
            TratarExcecao::excecao($e);
            return response()->json([
                'error' => true,
                'message' => $e->getMessage(),
            ], 500);

            }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

}
