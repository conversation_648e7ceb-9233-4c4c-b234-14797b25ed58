<?php

namespace App\Http\Controllers;

use App\Enums\{PessoasCategoriaEnum, ServicoCriticidadeEnum};
use App\Http\Requests\StoreServicoRequest;
use App\Models\SGS\Usuario;
use App\Services\{HabilitarUnidadesOrganizacionaisServicos};
use App\Util\{TratarExcecao, Notificacao};
use Exception;
use Illuminate\Http\Request;
use Illuminate\Contracts\View\View;
use App\Models\{Servico, HabilitarUnidadeOrganizacional};
use Illuminate\Support\Facades\Gate;


define('VIEWS', [
    "manutencao" => 'administrador.servicos.manutencao'
]);
class ServicoController extends Controller
{
    public function __construct(protected HabilitarUnidadesOrganizacionaisServicos $habilitarUnidadesOrganizacionaisServicos)
    {}

   
    public function index(Request $request): View
    {

        if(Gate::denies('servicos-listar')){
            abort(403);
        }
        
        $unidadeNaSessao = session('filtros_form_servicos.unidade_responsavel');
        $unidadeRequest = $request->input('unidade_responsavel') ?? null;

        FormSaveInSession($request, 'servicos', ['unidade_responsavel' => auth()->user()->unidade_id]);
        
        if($unidadeRequest != null && $unidadeNaSessao != $unidadeRequest){
            // Se a unidade for alterada, limpa o setor responsável
            session()->forget('filtros_form_servicos.setor_responsavel');
            // Se a unidade for alterada, limpa o grupo responsável
            session()->forget('filtros_form_servicos.grupo_responsavel');
        }

        //Se for o administrador, pega todas as unidades habilitadas, caso contrário, pega apenas a sua unidade
        $unidadesHabilitadas = auth()->user()->perfis()->where('nome', 'Administrador')->exists() 
        ? $this->habilitarUnidadesOrganizacionaisServicos->getHabilitadas()
        : [HabilitarUnidadeOrganizacional::where('unidade_id', auth()->user()->unidade_id)->where('habilitado', true)->with('setor')->firstOrFail()];

        $criticidadeList = ServicoCriticidadeEnum::cases();
        $servicos = Servico::query()->with('unidade', 'setor')->filter(session('filtros_form_servicos'))->orderBy('nome')->paginate(15); 

        return view('administrador.servicos.index',compact('servicos', 'unidadesHabilitadas', 'criticidadeList'));
    }

    public function create()
    {
        if(Gate::denies('servicos-criar')){
            abort(403);
        }
    
        $rota = route('servicos.store');
        $atividade = 'Criar';
        $unidadesHabilitadas = auth()->user()->perfis()->where('nome', 'Administrador')->exists() 
        ? $this->habilitarUnidadesOrganizacionaisServicos->getHabilitadas()
        : [HabilitarUnidadeOrganizacional::where('unidade_id', auth()->user()->unidade_id)->firstOrFail()];
        
        return view(VIEWS['manutencao'],[
            'rota'=>$rota,
            'atividade'=>$atividade,
            'unidadesHabilitadas'=>$unidadesHabilitadas,
            'categorias' => PessoasCategoriaEnum::cases(),
        ]);


    }

    public function store(StoreServicoRequest $request)
    {
        if(Gate::denies('servicos-criar')){
            abort(403);
        }

        $dadosValidados = $request->validated();
        $dadosValidados['categorias_permitidas'] ??= [];
        
        try {
            Servico::create($dadosValidados);
            Notificacao::mensagem('success', 'Serviço cadastrado com sucesso.');
            return redirect()->route('servicos.index');
        } catch (Exception $e) {
            TratarExcecao::excecao($e);
            return redirect()->back()->withInput();
        }
    }

    public function show(string $id)
    {
        if(Gate::denies('servicos-visualizar')){
            abort(403);
        }
        if(!$servico = Servico::find($id)){
            Notificacao::mensagem('error', 'Serviço nao encontrado.');
            return redirect()->route('servicos.index');
        }
        $unidadesHabilitadas = [$servico->unidadeHabilitada];

        $categorias = PessoasCategoriaEnum::cases();

        $atividade = 'Visualizar';
        $rota = '#';
         
        return view(VIEWS['manutencao'],compact('rota','servico','atividade', 'unidadesHabilitadas', 'categorias'));
    }

    public function edit(string $id)
    {
        if(Gate::denies('servicos-editar')){
            abort(403);
        }
        //verificação da existência do serviço e chamando a view
        if(!$servico = Servico::find($id)){
            Notificacao::mensagem('error', 'Serviço nao encontrado.');
            return redirect()->route('servicos.index');
        }

        $rota = route('servicos.update', $id);
        $atividade = 'Editar';
        $unidadesHabilitadas = auth()->user()->perfis()->where('nome', 'Administrador')->exists() 
        ? $this->habilitarUnidadesOrganizacionaisServicos->getHabilitadas()
        : [HabilitarUnidadeOrganizacional::where('unidade_id', auth()->user()->unidade_id)->firstOrFail()];

        $categorias = PessoasCategoriaEnum::cases();

        return view(VIEWS['manutencao'],compact('rota','servico','atividade', 'unidadesHabilitadas', 'categorias'));
    }

    public function update(StoreServicoRequest $request, string $id)
    {
        if(Gate::denies('servicos-editar')){
            abort(403);
        }

        $dadosValidados = $request->validated();
        $dadosValidados['categorias_permitidas'] ??= [];
           
        try {
            Servico::findOrFail($id)->update($dadosValidados);
            Notificacao::mensagem('success', 'Serviço atualizado com sucesso.');
            return redirect()->route('servicos.index');

        } catch (Exception $e) {
            TratarExcecao::excecao($e);
            return redirect()->back()->withInput();
        }
    }

    public function destroy(string $id)
    {
        try {
            Servico::findOrFail($id)->delete();
            Notificacao::mensagem('success', 'Serviço apagado com sucesso.');
            return redirect()->route('servicos.index');
        } catch (Exception $e) {
            TratarExcecao::excecao($e);
            return redirect()->back()->withInput();
        }
    }

    public function getServicosBySetorAndUsuario($setorId, $usuario){
        if(!request()->ajax()){
            abort(403, 'Acesso não autorizado.');
        }

        $categorias = Usuario::findOrFail($usuario)->categorias->pluck('value')->toArray();

        $servicos = Servico::servicoBySetoresAndCategorias([$setorId], $categorias)->select('servicos.id', 'servicos.nome')->orderBy('nome')->get();
        return response()->json($servicos);
    }

    public function getServicosOnlyByUnidadeAndUsuario($unidadeId, $usuario){
        if(!request()->ajax()){
            abort(403, 'Acesso não autorizado.');
        }

        $categorias = Usuario::findOrFail($usuario)->categorias->pluck('value')->toArray();
        $servicos = Servico::servicoOnlyByUnidadeAndCategorias($unidadeId, $categorias)->select('servicos.id', 'servicos.nome')->orderBy('nome')->get();

        return response()->json($servicos);
    }

    public function getServicosByGrupoAndUsuario($grupoId, $usuario)
    {
        if(!request()->ajax()){
            abort(403, 'Acesso não autorizado.');
        }
        $categorias = Usuario::findOrFail($usuario)->categorias->pluck('value')->toArray();
        $servicos = Servico::servicoByGrupoAndCategorias($grupoId, $categorias)->select('servicos.id', 'servicos.nome')->orderBy('nome')->get();

        return response()->json($servicos);
    }

}
