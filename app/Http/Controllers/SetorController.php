<?php

namespace App\Http\Controllers;

use App\Models\HabilitarSetor;
use App\Models\HabilitarUnidadeOrganizacional;
use App\Models\SGS\Campus;
use App\Models\SGS\Setor;
use App\Services\HabilitarUnidadesOrganizacionaisServicos;
use App\Services\SGS\SetoresServicos;
use App\Util\Notificacao;
use App\Util\TratarExcecao;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\ValidationException;

class SetorController extends Controller
{
    protected $mapaSetor= [
        'setor' => [
            'viewIndex' => 'administrador.setores.index',
            'viewEdit' => 'administrador.setores.edit',
            'relation' => 'habilitarSetor',
            'routeIndex' => 'setores.index',
            'model' => HabilitarSetor::class
        ],
        'unidadeOrganizacional' => [
            'viewIndex' => 'administrador.unidadesOrganizacionais.index',
            'viewEdit' => 'administrador.unidadesOrganizacionais.edit',
            'relation' => 'habilitarUnidadeOrganizacional',
            'routeIndex' => 'setores.index',
            'model' => HabilitarUnidadeOrganizacional::class
        ],
    ];

    public function __construct(protected SetoresServicos $setoresServicos)
    {}
    public function index(Request $request, $tipoSetor, HabilitarUnidadesOrganizacionaisServicos $habilitarUnidadesOrganizacionaisServicos)
    {

        $usuario = auth()->user();
        FormSaveInSession($request, $tipoSetor, ['unidadeId' => $usuario->unidade_id]);

        if($tipoSetor == 'setor' && Gate::denies('setores-listar') || $tipoSetor == 'unidadeOrganizacional' && Gate::denies('unidades-organizacionais-listar')){
            abort(403);
        }

        $setorInfo = $this->mapaSetor[$tipoSetor] ?? $this->mapaSetor['unidadeOrganizacional']; 

        $query = $tipoSetor == 'setor' ? 
            $this->setoresServicos->getSetores($request->input('unidadeId')) : 
            $this->setoresServicos->getUnidadesOrganizacionais();

        if(!$query){
            Notificacao::mensagem('error', 'Unidade Organizacional não encontrada');
            return redirect()->back();
        }

        $campiIds = (clone $query)->distinct()->pluck('id_campus');
        $campi = Campus::whereIn('id_campus', $campiIds)->orderBy('ds_nomecampus', 'asc')->get();
        if($tipoSetor == 'setor'){
            $unidadesHabilitadas = $habilitarUnidadesOrganizacionaisServicos->getHabilitadas();
            view()->share('unidadesHabilitadas', $unidadesHabilitadas);
        }

        $setores = $query->applyFilters($request->all(), $tipoSetor)->paginate(10);

        return view($setorInfo['viewIndex'], compact('setores','campi'));
    }


    public function edit($tipoSetor, string $id)
    {

        
         if($tipoSetor == 'setor' && Gate::denies('setores-editar') || $tipoSetor == 'unidadeOrganizacional' && Gate::denies('unidades-organizacionais-editar')){
             abort(403);
         }
         
        $setorInfo = $this->mapaSetor[$tipoSetor] ?? $this->mapaSetor['unidadeOrganizacional'];

        if(!$setor = Setor::query()->find($id)){
            Notificacao::mensagem('error', 'Setor não encontrado.');
            return redirect()->route($setorInfo['routeIndex'], $tipoSetor);
        }

        $habilitado = $setor->{$setorInfo['relation']} ? $setor->{$setorInfo['relation']}->habilitado : false;

        return view($setorInfo['viewEdit'], compact('setor', 'habilitado'));
    }


    public function update(Request $request, $tipoSetor, string $id)
    {
        
        if($tipoSetor == 'setor' && Gate::denies('setores-editar') || $tipoSetor == 'unidadeOrganizacional' && Gate::denies('unidades-organizacionais-editar')){
            abort(403);
        }
        
        $setorInfo = $this->mapaSetor[$tipoSetor] ?? $this->mapaSetor['unidadeOrganizacional']; 

        if(!$setor = Setor::query()->find($id)){
            Notificacao::mensagem('error', 'Setor não encontrado.');
            return redirect()->route($setorInfo['routeIndex'], $tipoSetor);
        }

        $data = [
            'habilitado' => $request->input('habilitado', false),
        ];
        
       
        try {

            if($tipoSetor == 'unidadeOrganizacional'){
                $request->validate([
                    'imagem' => 'file|image|mimes:jpg,jpeg,png|max:1024',
                    'descricao' => 'required|string',
                ]);
    
                $data['descricao'] = $request->input('descricao');
                

                if($request->hasFile('imagem') && $request->file('imagem')->isValid()){
                    $imagem = $request->imagem;
                    
                    $extensao = $imagem->extension();
                    $nomeArquivo = "logo_{$setor->sg_setor}.{$extensao}";
                    
                    $path = $imagem->storeAs('images/logoSetores', $nomeArquivo, 'public');
                    
                    $data['caminho_logomarca'] = "{$path}";
                    
                }elseif($setor->habilitarUnidadeOrganizacional?->caminho_logomarca == null){
                    
                    $data['caminho_logomarca'] = 'images/logoSetores/logo_default.png';
                }
            }
            
            $setorInfo['model']::updateOrCreate(
                [$tipoSetor == 'setor' ? 'setor_id' : 'unidade_id' => $setor->id_setor],
                $data
            );
    
            Notificacao::mensagem('success', 'Setor atualizado com sucesso.');
    
            return redirect()->route($setorInfo['routeIndex'], $tipoSetor);
            
        } catch (ValidationException $e) {
            return redirect()->back()->withInput()->withErrors($e->errors());
        } catch (Exception $e) {
            
            TratarExcecao::excecao($e);
            return redirect()->back()->withInput();
        }

        
    }

    public function getSetores($campusId){
        if(!request()->ajax()){
            abort(403, 'Acesso não autorizado.');
        }

        $setores = Setor::where('id_campus', $campusId)->orderBy('ds_nomesetor', 'asc')->get(['id_setor', 'ds_nomesetor']);
        return response()->json($setores);
    }

    public function getSetoresHabilitadosByUnidade($unidadeId){
        if(!request()->ajax()){
            abort(403, 'Acesso não autorizado.');
        }
        $setores = $this->setoresServicos->getSetores($unidadeId)->filterBySetorHabilitado('setor')->without('campus', 'setorSuperior', 'habilitarSetor')->orderBy('ds_nomesetor', 'asc')->get(['id_setor', 'ds_nomesetor']);
        return response()->json($setores);
    }

}
