<?php

namespace App\Http\Controllers;

use App\Http\Requests\GrupoSetoresRequest;
use App\Models\GrupoSetor;
use App\Models\HabilitarUnidadeOrganizacional;
use App\Services\HabilitarUnidadesOrganizacionaisServicos;
use App\Util\{Notificacao, TratarExcecao};
use Exception;
use Illuminate\Support\Facades\Gate;
use Illuminate\Http\Request;

class GrupoSetorController extends Controller
{
    public function index(Request $request, HabilitarUnidadesOrganizacionaisServicos $habilitarUnidadesOrganizacionaisServicos)
    {
        if(Gate::denies('grupos-setores-visualizar')){
            abort(403);
        }

        FormSaveInSession($request, 'gruposSetores', ['unidade_responsavel_id' => auth()->user()->unidade_id]);

        $grupos = GrupoSetor::with('membros', 'unidade')->filter(session('filtros_form_gruposSetores'))->orderBy('nome')->paginate(15);

        $unidadesHabilitadas = auth()->user()->perfis()->where('nome', 'Administrador')->exists()
        ? $habilitarUnidadesOrganizacionaisServicos->getHabilitadas()
        : [HabilitarUnidadeOrganizacional::where('unidade_id', auth()->user()->unidade_id)->where('habilitado', true)->with('setor')->firstOrFail()];

        return view('grupo-setor.index', compact('grupos', 'unidadesHabilitadas'));
    }

    public function create(HabilitarUnidadesOrganizacionaisServicos $habilitarUnidadesOrganizacionaisServicos)
    {
        if(Gate::denies('grupos-setores-criar')){
            abort(403);
        }

        $acao = 'Cadastrar';
        $rota = route('grupoSetores.store');
        if(auth()->user()->perfis()->where('nome', 'Administrador')->exists()){
            $unidadesHabilitadas = $habilitarUnidadesOrganizacionaisServicos->getHabilitadas();
        } else {
            $unidadesHabilitadas = [HabilitarUnidadeOrganizacional::where('unidade_id', auth()->user()->unidade_id)->with('setor')->firstOrFail()];
        }
        return view('grupo-setor.manutencao', compact('unidadesHabilitadas', 'rota', 'acao'));
    }

    public function store(GrupoSetoresRequest $request)
    {
        if(Gate::denies('grupos-setores-criar')){
            abort(403);
        }

        try{
            $grupo = GrupoSetor::create($request->validated());
            $grupo->membros()->createMany($request->input('setores'));

            Notificacao::mensagem('success', 'Grupo cadastrado com sucesso.');
        } catch(Exception $e){
            TratarExcecao::excecao($e);
            return redirect()->back()->withInput();
        }

        return redirect()->route('grupoSetores.index');
    }

    public function show(GrupoSetor $grupoSetor)
    {
        if(Gate::denies('grupos-setores-visualizar')){
            abort(403);
        }

        $grupoSetor->load('membros.setor', 'unidade');
        return view('grupo-setor.show', compact('grupoSetor'));
    }

    public function edit(GrupoSetor $grupoSetor)
    {
        if(Gate::denies('grupos-setores-editar')){
            abort(403);
        }

        $acao = 'Editar';
        $rota = route('grupoSetores.update', $grupoSetor->id);
        $grupoSetor->load('membros', 'unidadeHabilitada.setor');
        $setores = $grupoSetor->membros->toArray();
        $unidadesHabilitadas = [$grupoSetor->unidadeHabilitada];
        return view('grupo-setor.manutencao', compact('grupoSetor', 'unidadesHabilitadas', 'setores', 'rota', 'acao'));
    }
    public function update(GrupoSetoresRequest $request, GrupoSetor $grupoSetor)
    {
        if(Gate::denies('grupos-setores-editar')){
            abort(403);
        }

        try{
            $grupoSetor->update($request->validated());
            $grupoSetor->membros()->delete();
            $grupoSetor->membros()->createMany($request->input('setores'));
            Notificacao::mensagem('success', 'Grupo atualizado com sucesso.');

        } catch(Exception $e){
            TratarExcecao::excecao($e);
            return redirect()->back()->withInput();
        }

        return redirect()->route('grupoSetores.index');
    }
    public function destroy(GrupoSetor $grupoSetor)
    {
        if(Gate::denies('grupos-setores-editar')){
            abort(403);
        }

        try{
            $grupoSetor->delete();
            Notificacao::mensagem('success', 'Grupo excluído com sucesso.');
        } catch(Exception $e){
            TratarExcecao::excecao($e);
            return redirect()->back()->withInput();
        }

        return redirect()->route('grupoSetores.index');
    }

    public function getGruposByUnidade($unidadeId)
    {
        if(!request()->ajax()){
            abort(403, 'Acesso não autorizado.');
        }

        $grupos = GrupoSetor::query()->where('unidade_responsavel_id', $unidadeId)->where('habilitado', true)->select('id', 'nome')->orderBy('nome')->get();

        return response()->json($grupos);
    }
}
