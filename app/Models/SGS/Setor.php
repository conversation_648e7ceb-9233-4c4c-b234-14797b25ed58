<?php

namespace App\Models\SGS;

use App\Models\{SGS\Campus, HabilitarSetor, HabilitarUnidadeOrganizacional};
use DB;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;

class Setor extends Model
{
    protected $connection = 'pgsql_sgs';

    protected $table = 'setores';

    protected $primaryKey = 'id_setor';

    #region Relacionamentos

    public function habilitarSetor(): Relation
    {
        return $this->hasOne(HabilitarSetor::class, 'setor_id', 'id_setor');
    }

    public function habilitarUnidadeOrganizacional(): Relation
    {
        return $this->hasOne(HabilitarUnidadeOrganizacional::class, 'unidade_id', 'id_setor');
    }

    public function setorSuperior()
{
    return $this->belongsTo(Setor::class, 'id_setorsuperior', 'id_setor');
}

    public function campus()
    {
        return $this->belongsTo(Campus::class, 'id_campus', 'id_campus');
    }

    #endregion

    #region Scopes

    public function scopeApplyFilters(Builder $query, array $filters, $tipoSetor): Builder
    {
        $map = [
            'sigla' => 'filterBySigla',
            'setor' => 'filterBySetor',
            'setorSuperior' => 'filterBySetorSuperior',
            'campusId' => 'filterByCampus',
        ];

        foreach ($map as $key => $scopeMethod){
            if(!empty($filters[$key])){
                $query->$scopeMethod($filters[$key]);
            }
        }

        if(!empty($filters['habilitado'])){
            $query->filterBySetorHabilitado($tipoSetor);
        }

        $query->orderBy('ds_nomesetor', $filters['sortOrder'] ?? 'asc');

        return $query;
    }

    public function scopeFilterBySetor(Builder $query, $setor): Builder
    {
        return $query->where('ds_nomesetor', 'ILIKE', "%$setor%");
    }

    public function scopeFilterBySigla(Builder $query, $sigla): Builder
    {
        return $query->where('sg_setor', 'ILIKE', "%$sigla%");
    }

    public function scopeFilterBySetorSuperior(Builder $query, $setorSuperior): Builder
    {
        return $query->whereHas('setorSuperior', function($q) use ($setorSuperior){
            $q->where('ds_nomesetor', 'ILIKE', "%$setorSuperior%");
        });
    }

    public function scopeFilterByCampus(Builder $query, $campusId): Builder
    {
        return $query->where('id_campus', $campusId);
    }
    public function scopeFilterBySetorHabilitado(Builder $query, string $tipoSetor): Builder
    {
        $tabela = $tipoSetor == 'setor' ? 'habilitar_setores' : 'habilitar_unidades_organizacionais';
        $coluna = $tipoSetor == 'setor'? 'setor_id' : 'unidade_id';
        $setorIds = DB::connection('pgsql')
            ->table($tabela)
            ->where('habilitado', true)
            ->pluck($coluna);

        return $query->whereIn('id_setor', $setorIds);
    }
    #endregion

    public function getDescricaoHierarquicaAttribute()
    {
        $hierarquia = $this->ds_nomesetor;

        if($this->setorSuperior){
            $hierarquia .= ' | '.$this->setorSuperior->DescricaoHierarquica;
        }   

        return $hierarquia;
    }


}
