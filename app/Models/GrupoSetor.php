<?php

namespace App\Models;

use App\Models\SGS\Setor;
use App\Models\GrupoSetorMembro;
use App\Models\HabilitarUnidadeOrganizacional;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class GrupoSetor extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'grupo_setores';
    protected $fillable = [
        'nome',
        'unidade_responsavel_id',
        'habilitado',
    ];

    #region Relacionamentos

    public function membros(): HasMany
    {
        return $this->hasMany(GrupoSetorMembro::class, 'grupo_setor_id', 'id');
    }

    public function unidade(): BelongsTo
    {
        return $this->belongsTo(Setor::class, 'unidade_responsavel_id', 'id_setor');
    }

    public function unidadeHabilitada(): BelongsTo
    {
        return $this->belongsTo(HabilitarUnidadeOrganizacional::class, 'unidade_responsavel_id', 'unidade_id');
    }

    #endregion

    #region Scopes

    public function scopeFilter(Builder $query, array $filters): Builder
    {
        $user = auth()->user();
        if(!$user->perfis()->where('nome', 'Administrador')->exists()){
            $query->where('unidade_responsavel_id', $user->unidade_id);
        }

        $query->when($filters['nome'] ?? null, function ($query, $nome) {
            return $query->where('nome', 'ILIKE', "%{$nome}%");
        })
        ->when($filters['unidade_responsavel_id'] ?? null, function ($query, $unidade_responsavel_id) {
            return $query->where('unidade_responsavel_id', $unidade_responsavel_id);
        })
        ->when(isset($filters['habilitado']) && $filters['habilitado'] !== '', function ($query) use ($filters) {
            return $query->where('habilitado', $filters['habilitado']);
        });

        return $query;
    }

    #endregion
}
