<?php

namespace App\Models;

use App\Enums\{PessoasCategoriaEnum, ServicoCriticidadeEnum};
use App\Models\SGS\Setor;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\AsEnumCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Servico extends Model
{
    use HasFactory;
    protected $connection = 'pgsql';
    protected $table = 'servicos';
    public $timestamps = true;

    protected $fillable = [
        'id',
        'nome',
        'descricao',
        'setor_responsavel',
        'status',
        'criticidade',
        'unidade_responsavel',
        'categorias_permitidas',
        'grupo_responsavel',
    ];

    protected $casts = [
        'categorias_permitidas' => AsEnumCollection::class . ':' . PessoasCategoriaEnum::class,
        'criticidade' => ServicoCriticidadeEnum::class,
    ];

    #region Relacionamentos

    public function setor(): Relation
    {
        return $this->belongsTo(Setor::class, 'setor_responsavel', 'id_setor');
    }
    public function setorHabilitado(): Relation
    {
        return $this->belongsTo(HabilitarSetor::class, 'setor_responsavel', 'setor_id');
    }
    public function unidade(): Relation{
        return $this->belongsTo(Setor::class, 'unidade_responsavel', 'id_setor');
    }
    public function unidadeHabilitada(): Relation
    {
        return $this->belongsTo(HabilitarUnidadeOrganizacional::class, 'unidade_responsavel', 'unidade_id');
    }
    public function chamados(): Relation
    {
        return $this->hasMany(Chamado::class, 'servico_id', 'id');
    }

    public function grupo()
    {
        return $this->belongsTo(GrupoSetor::class, 'grupo_responsavel', 'id');
    }

    #endRegion

    #region Scopes

    public function scopeServicoSetores(Builder $query, $ids): Builder
    {
        return $query->join('habilitar_setores', 'servicos.setor_responsavel', '=', 'habilitar_setores.setor_id')
                    ->whereIn('servicos.setor_responsavel', $ids)
                    ->habilitados()
                    ->where('habilitar_setores.habilitado', true)
                    ->select('servicos.id', 'servicos.nome');
    }

    public function scopeComCategoriasPermitidas(Builder $query, $categorias): Builder
    {
        return $query->where(function ($query) use ($categorias){
            foreach($categorias as $categoria){
                $query->orWhereJsonContains('servicos.categorias_permitidas', $categoria);
            }
        });
    }

    public function scopeHabilitados(Builder $query): Builder
    {
        return $query->where('servicos.status', true);
    }

    /**
     * Escopo para filtrar serviços relacionados a setores especificos e às categorias permitidas.
     * 
     * Retorna join com a tabela 'habilitar_setores', filtra serviços cujo 'setor_responsavel'
     * esteja entre os IDs fornecidos e que tenham o setor habilitado, além de considerar
     * apenas os que possuem as categorias informadas
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param array<int> $ids Lista de IDs dos setores responsáveis
     * @param array<string> $categorias Lista de categorias permitidas
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeServicoBySetoresAndCategorias(Builder $query, array $ids, array $categorias): Builder
    {
        return $query->join('habilitar_setores', 'servicos.setor_responsavel', '=', 'habilitar_setores.setor_id')
                    ->whereIn('servicos.setor_responsavel', $ids)
                    ->habilitados()
                    ->where('habilitar_setores.habilitado', true)
                    ->comCategoriasPermitidas($categorias);
    }

    /**
     * Escopo para filtrar serviços relacionados ao grupo especifico e às categorias permitidas.
     * 
     * Retorna join com a tabela 'grupo_setores', filtra serviços cujo 'grupo_responsavel'
     * seja igual ao ID fornecido o grupo esteja habilitado, além de considerar 
     * apenas os serviços que tenham as categorias informadas
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $grupoId Id do grupo
     * @param array<string> $categorias Lista de categorias permitidas
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeServicoByGrupoAndCategorias(Builder $query, int $grupoId, array $categorias): Builder
    {
        return $query->join('grupo_setores', 'servicos.grupo_responsavel', '=', 'grupo_setores.id')
                    ->where('servicos.grupo_responsavel', $grupoId)
                    ->habilitados()
                    ->where('grupo_setores.habilitado', true)
                    ->comCategoriasPermitidas($categorias);
    }

    /**
     * Escopo para filtrar serviços relacionados a unidade especifica, incluindo aqueles que tem setor responsável ou grupo responsável habilitados e às categorias permitidas.
     * 
     * Retorna join com a tabela 'habilitar_setores', 'grupo_setores' e 'habilitar_unidades_organizacionais', filtra serviços cujo 'unidade_responsavel'
     * seja igual ao ID fornecido e que tenha a unidade habilitada, incluindo aqueles que tem setor responsável ou grupo responsavel habilitados além de considerar
     * apenas os que possuem as categorias informadas
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $unidadeId Id da unidade
     * @param array<string> $categorias Lista de categorias permitidas
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeServicoAllByUnidadeAndCategorias(Builder $query, int $unidadeId, array $categorias): Builder
    {
        return $query->leftjoin('habilitar_setores', 'servicos.setor_responsavel', '=', 'habilitar_setores.setor_id')
                    ->leftJoin('grupo_setores', 'servicos.grupo_responsavel', '=', 'grupo_setores.id')
                    ->join('habilitar_unidades_organizacionais', 'servicos.unidade_responsavel', '=', 'habilitar_unidades_organizacionais.unidade_id')
                    ->where('servicos.unidade_responsavel', $unidadeId)
                    ->habilitados()
                    ->where('habilitar_unidades_organizacionais.habilitado', true)
                    ->where(function ($query) {
                        $query->whereNull('servicos.setor_responsavel')
                            ->orWhere('habilitar_setores.habilitado', true);
                    })
                    ->where(function ($query) {
                        $query->whereNull('servicos.grupo_responsavel')
                            ->orWhere('grupo_setores.habilitado', true);
                    })
                    ->comCategoriasPermitidas($categorias);
    }

    /**
     * Escopo para filtrar serviços relacionados a unidade especifica, que não tem setor responsável, não tem grupo responsável e às categorias permitidas.
     * 
     * Retorna join com a tabela 'habilitar_unidades_organizacionais', filtra serviços cujo 'unidade_responsavel'
     * seja igual ao ID fornecido e que tenha a unidade habilitada, sem setor responsável, sem grupo responsável e com as categorias permitidas
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $unidadeId Id da unidade
     * @param array<string> $categorias Lista de categorias permitidas
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeServicoOnlyByUnidadeAndCategorias(Builder $query, int $unidadeId, array $categorias): Builder{
        return $query->join('habilitar_unidades_organizacionais', 'servicos.unidade_responsavel', '=', 'habilitar_unidades_organizacionais.unidade_id')
            ->habilitados()
            ->where('servicos.unidade_responsavel', $unidadeId)
            ->where('habilitar_unidades_organizacionais.habilitado', true)
            ->whereNull('servicos.setor_responsavel')
            ->whereNull('servicos.grupo_responsavel')
            ->comCategoriasPermitidas($categorias);
    }

    public function scopeFilter(Builder $query, array $filters): Builder
    {
        //Restrição para o usuario que não é adm ver apenas os servicos da sua unidade
        $user = auth()->user();
        if(!$user->perfis()->where('nome', 'Administrador')->exists()){
            $query->where('unidade_responsavel', $user->unidade_id);
        }

        $query->when($filters['servico_nome'] ?? null, function ($query, $servico_nome) {
            return $query->where('nome', 'ILIKE', "%{$servico_nome}%");
        })
        ->when(isset($filters['servico_status']) && $filters['servico_status'] !== '', function ($query) use ($filters) {
            return $query->where('status', $filters['servico_status']);
        })
        ->when($filters['servico_criticidade'] ?? null, function ($query, $servico_criticidade) {
            return $query->where('criticidade', $servico_criticidade);
        })
        ->when($filters['unidade_responsavel'] ?? null, function ($query, $unidade_responsavel) {
            return $query->where('unidade_responsavel', $unidade_responsavel);
        })
        ->when($filters['setor_responsavel'] ?? null, function ($query, $setor_responsavel) {
            return $query->where('setor_responsavel', $setor_responsavel);
        })
        ->when($filters['grupo_responsavel'] ?? null, function ($query, $grupo_responsavel) {
            return $query->where('grupo_responsavel', $grupo_responsavel);
        });

        return $query;
    }

    #endRegion
}
