<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Chamado;

class Notificacao extends Model
{
    
    protected $connection = 'pgsql';
    protected $table = 'notificacao';
    protected $primaryKey = 'id';

    protected $fillable = [
        'usuario_id',
        'mensagem',
        'id_chamado',
        'tipo',
        'visualizada',
    ];

    #region Relacionamentos
    public function chamado()
    {
        return $this->belongsTo(Chamado::class, 'id_chamado', 'id_chamado');
    }
    #endregion
}
