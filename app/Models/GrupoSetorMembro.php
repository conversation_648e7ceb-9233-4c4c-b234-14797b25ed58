<?php

namespace App\Models;

use App\Models\SGS\Setor;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class GrupoSetorMembro extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'grupo_setor_membros';
    protected $fillable = [
        'grupo_setor_id',
        'setor_id',
        'peso',
    ];

    #region Relacionamentos

    public function grupo(): BelongsTo
    {
        return $this->belongsTo(GrupoSetor::class, 'grupo_setor_id', 'id');
    }

    public function setor(): BelongsTo
    {
        return $this->belongsTo(Setor::class, 'setor_id', 'id_setor');
    }

    public function setorHabilitado(): BelongsTo
    {
        return $this->belongsTo(HabilitarSetor::class, 'setor_id', 'setor_id');
    }


    #endregion
}
