<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChamadoHistorico extends Model
{
    use HasFactory;
    protected $connection = 'pgsql';
    protected $table = 'chamado_historicos';
    protected $primaryKey = 'id';

    protected $fillable = [
        'atividade',
        'chamado_id',
        'servico_id',
        'servico_nome',
        'usuario_solicitante_id',
        'usuario_solicitante_nome',
        'usuario_atendente_id',
        'usuario_atendente_nome',
        'setor_solicitante_id',
        'setor_solicitante_nome',
        'setor_atendente_id',
        'setor_atendente_nome',
        'status',
        'concluded_at',
        'unidade_atendente_id',
        'unidade_atendente_nome',
    ];

    #region Relacionamentos
    public function chamado()
    {
        return $this->belongsTo(Chamado::class, 'chamado_id', 'id_chamado');
    }

    public function servico()
    {
        return $this->belongsTo(Servico::class, 'servico_id', 'id');
    }
}
