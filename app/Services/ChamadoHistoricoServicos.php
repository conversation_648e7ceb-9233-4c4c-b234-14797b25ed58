<?php

namespace App\Services;

use App\Models\ChamadoHistorico;
use App\Models\Servico;
use App\Models\SGS\Pessoa;
use App\Models\SGS\Setor;
use App\Models\SGS\Usuario;

class ChamadoHistoricoServicos
{
    public function __construct(protected ChamadoHistorico $model)
    {}
    
    public function create(array $chamado, array $informacoesAdicionais = [])
    {
        $chamado = array_merge($chamado, $informacoesAdicionais);
        $chamado['chamado_id'] = $chamado['id_chamado'];

        if(isset($chamado['servico_id'])){
            $chamado['servico_nome'] = Servico::where('id', $chamado['servico_id'])->value('nome');
        }
        if(isset($chamado['usuario_solicitante_id'])){
            $cpf = Usuario::where('id_usuario', $chamado['usuario_solicitante_id'])->value('ds_cpf');
            $chamado['usuario_solicitante_nome'] = Pessoa::where('ds_cpf', $cpf)->value('ds_nomepessoa');
        }
        if(isset($chamado['usuario_atendente_id'])){
            $cpf = Usuario::where('id_usuario', $chamado['usuario_atendente_id'])->value('ds_cpf');
            $chamado['usuario_atendente_nome'] = Pessoa::where('ds_cpf', $cpf)->value('ds_nomepessoa');
        }
        if(isset($chamado['setor_solicitante_id'])){
            $chamado['setor_solicitante_nome'] = Setor::where('id_setor', $chamado['setor_solicitante_id'])->value('ds_nomesetor');
        }
        if(isset($chamado['setor_atendente_id'])){
            $chamado['setor_atendente_nome'] = Setor::where('id_setor', $chamado['setor_atendente_id'])->value('ds_nomesetor');
        }
        if(isset($chamado['unidade_atendente_id'])){
            $chamado['unidade_atendente_nome'] = Setor::where('id_setor', $chamado['unidade_atendente_id'])->value('ds_nomesetor');
        }

        return $this->model->create($chamado);
    }
}