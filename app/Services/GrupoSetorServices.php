<?php

namespace App\Services;

use App\Models\Chamado;
use App\Models\GrupoSetor;

class GrupoSetorServices
{
    public function __construct(protected GrupoSetor $model)
    {}

    /**
     * Retorna o id do setor com menos carga de chamados de um grupo
     * 
     * A carga é calculada com base na quantidade de chamados abertos e em andamento, dividido pelo peso do setor
     * caso a carga seja igual vê o que tem o maior peso
     * 
     * @param int $grupoId - Id do grupo
     * @return int|null - Id do setor com menos carga
     */
    public function getSetorBalanceamentoByGrupo(int $grupoId): int|null
    {
        $grupo = $this->model->with('membros')->findOrFail($grupoId);

        // Se o grupo não tiver membros, retorna null, um grupo sempre deve ter ao menos dois menbros, é apenas uma garantia extra
        if($grupo->membros->isEmpty()) {
            return null;
        }

        $pesos = $grupo->membros->pluck('peso', 'setor_id');

        $chamadosPorSetor = Chamado::query()
            ->selectRaw('setor_atendente_id, COUNT(*) as total')
            ->whereIn('setor_atendente_id', $pesos->keys())
            ->whereIn('status', ['Aberto', 'Em andamento'])
            ->groupBy('setor_atendente_id')
            ->pluck('total', 'setor_atendente_id');

        $setoresComCarga = $pesos->map(function ($peso, $setorId) use ($chamadosPorSetor) {
            $totalChamados = $chamadosPorSetor->get($setorId, 0);
            $pesoReal = $peso > 0 ? $peso : 1; // Evita divisão por zero, porém o peso deve ser sempre maior que 0, é apenas uma garantia extra

            return [
                'setor_id' => $setorId,
                'carga' => $totalChamados / $pesoReal,
                'peso' => $pesoReal,
            ];
        });

        $setoresOrdenados = $setoresComCarga->sort(function ($a, $b) {
            /**
             * Primeira parte verifica a carga, se o setor A tiver menos carga, ele vira primeiro na ordenação
             * Se o setor B tiver menos carga, ele vira primeiro na ordenação
             * Se ambos tiverem a mesma carga o 'spaceship operator' ( <=> ) retorna 0
             * Se a primeira parte da expressão for 0, o 'elvis operator' ( ?: ) não retorna a primeira parte, então retorna a segunda parte
             * Na segunda parte verifica o peso, da mesma forma que a primeira, porém dessa vez ordena de forma decrescente, ou seja, o setor com maior peso virá primeiro
             */
            return $a['carga'] <=> $b['carga'] ?: $b['peso'] <=> $a['peso'];
        });

        return $setoresOrdenados->keys()->first() ?? null;
    }
}