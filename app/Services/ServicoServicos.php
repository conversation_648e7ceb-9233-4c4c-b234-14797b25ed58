<?php 

namespace App\Services;

use App\Models\Servico;

class ServicoServicos {
    public function __construct(private Servico $model) {

    }

    /**
     * Retorna o id do setor responsavel por aquele serviço
     * 
     * Caso o serviço não tenha setor responsavel, mas tenha grupo responsavel, retorna o setor com menos carga do grupo
     * Caso o serviço nao tenha setor nem grupo responsavel, retorna null
     * @param int $servicoId - Id do serviço
     * @param mixed $grupoSetorServices - Instancia do GrupoSetorServices, se nenhuma for passada, uma nova instancia será criada
     * @return int|null - Id do setor responsavel ou null
     */
    public function getSetorResponsavelId(?int $servicoId, ?GrupoSetorServices $grupoSetorServices = null): ?int
    {
        if(!$servicoId) return null;
        $servico = $this->model->findOrFail($servicoId);
        
        $grupoSetorServices ??= app(GrupoSetorServices::class);

        if ($servico->setor_responsavel) {
            return $servico->setor_responsavel;
        }

        if ($servico->grupo_responsavel) {
            return $grupoSetorServices->getSetorBalanceamentoByGrupo($servico->grupo_responsavel);
        }

        return null;
    }

}







