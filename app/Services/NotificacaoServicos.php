<?php

namespace App\Services;


use App\Models\Notificacao  ;
use App\Events\{notificacaoChamado, chatChamado};
use App\Models\SGS\Usuario;
use Illuminate\Support\Facades\DB;

class NotificacaoServicos
{
    public function __construct(protected Notificacao $notificacao )
    {
        $this->notificacao = $notificacao;
    }

    public function criaNotificacao(
        int|array|null $usuario_id,
        int|null $id_chamado,
        string $mensagem,
        string $tipo,
        ?int $setor = null,
        ?int $unidade = null,
        ?string $perfil = null
    ) {
        $usuarios = [];

        // 1. Caso já tenha usuario_id enviado
        if ($usuario_id) {
            $usuarios = is_array($usuario_id) ? $usuario_id : [$usuario_id];
        } else {
            // 2. Nenhum usuario_id enviado, montar a consulta
            $query = Usuario::query();

            // Setor tem prioridade
            if ($setor) {
                $query->whereHas('lotacao', function ($q) use ($setor) {
                    $q->where('id_setor_exercicio', $setor);
                });
            }
            // Se não há setor mas há unidade
            elseif ($unidade) {
                $query->whereHas('lotacao', function ($q) use ($unidade) {
                    $q->where('id_setor_lotacao', $unidade);
                });
            }

            // 3. Perfil (precisa cruzar os bancos)
            if ($perfil) {
                // Pega ids de usuários no banco central que têm o perfil
                $usuariosPerfil = DB::connection('pgsql')
                    ->table('perfil_usuario')
                    ->join('perfis', 'perfis.id', '=', 'perfil_usuario.perfil_id')
                    ->where('perfis.nome', $perfil)
                    ->pluck('perfil_usuario.id_usuario')
                    ->toArray();

                if (empty($usuariosPerfil)) {
                    return response()->json(['error' => 'Nenhum usuário encontrado com o perfil informado'], 404);
                }

                // Restringe os usuários da query SGS a esses IDs
                $query->whereIn('id_usuario', $usuariosPerfil);
            }

            $usuarios = $query->pluck('id_usuario')->toArray();

            // Nenhum critério enviado
            if (empty($usuarios)) {
                return response()->json(['error' => 'Nenhum critério válido informado para buscar usuários'], 400);
            }
        }

        // 4. Criar notificações
        $notificacoes = [];

        foreach ($usuarios as $id) {
            $notificacao = $this->notificacao->create([
                'usuario_id' => $id,
                'mensagem'   => $mensagem,
                'id_chamado' => $id_chamado,
                'tipo'       => $tipo,
            ]);

            event(new notificacaoChamado($id, $notificacao->id, $id_chamado));

            $notificacoes[] = $notificacao;
        }

        return $notificacoes;
    }

    public function alteraStatusNotificacao(array $notificacoes)
    {
        
        return $this->notificacao->whereIn('id', $notificacoes)->update(['visualizada' => 1]);
    }


    public function atualizaChat( $payload)
    {
        event(new chatChamado($payload));
    }

}