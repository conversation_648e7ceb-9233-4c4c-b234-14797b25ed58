<?php 

namespace App\Services;

use App\Http\Requests\UpdateRegulacaoRequest;
use App\Models\{Chamado, ChamadoMensagem, SGS\Usuario};
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Services\{ChamadoHistoricoServicos, ChamadoTomboServicos, SGS\SetoresServicos, ServicoServicos};
use App\Services\NotificacaoServicos;

class ChamadoServicos 
{

    public function __construct(private Chamado $chamadoModel, private SetoresServicos $setoresServicos, private NotificacaoServicos $notificacaoServicos) {

    }

    public function contaChamadosRegistrados()
    {
        return $this->chamadoModel->count();
    }

    // Função principal responsável por consultar e listar os chamados na tela home
    public function localizarChamadosPeloFiltro(array $filtro) 
    {

        $filtroValidado = $this->validarFiltrosdeConsulta($filtro);
        
        $query = Chamado::query();
        $this->aplicarFiltrosDeConsulta($query, $filtroValidado);
        return $query->paginate(10);

    }

    // Função auxiliar responsável por validar os filtros de consulta de acordo com o nível de acesso do usuário
    public function validarFiltrosdeConsulta(array $filtro) {


        // Validar o direcionamento de chamado de acordo com o nível de acesso do usuário

        // --- 1. PERMISSÕES DO USUÁRIO ---
        $permissoes = auth()->user()->PermissoesUsuarioLogado('ocultas');
        $permissoesDescricao = array_column($permissoes, 'descricao');


        // --- 2. HIERARQUIA CENTRALIZADA PARA DIRECIONAMENTO (Permissão, Opções Válidas e Padrão) ---
        $configuracaoNiveis = [

            // Nível 3
            3 => [
                'permissao' => 'chamados-listar-todos',
                'opcoes'    => ['direcionado_unidade', 'direcionado_setor', 'direcionado_usuario', 'criado_usuario', 'todos'],
                'padrao'    => 'todos',
            ],
            // Nível 2
            2 => [
                'permissao' => 'chamados-listar-unidade',
                'opcoes'    => ['direcionado_unidade', 'direcionado_setor', 'direcionado_usuario', 'criado_usuario'],
                'padrao'    => 'direcionado_unidade',
            ],
            // Nível 1
            1 => [
                'permissao' => 'chamados-listar-setor',
                'opcoes'    => ['direcionado_setor', 'direcionado_usuario', 'criado_usuario'],
                'padrao'    => 'direcionado_setor',
            ],
        ];

        $dadosNivelMaximo = null;


        // --- 3. DETERMINAR O NÍVEL MÁXIMO DO USUÁRIO ---
        krsort($configuracaoNiveis); // Ordena pela chave (nível) em ordem decrescente (3, 2, 1)

        foreach ($configuracaoNiveis as $nivel => $dados) {
            if (in_array($dados['permissao'], $permissoesDescricao)) {
                $dadosNivelMaximo = $dados;
                break; // Achou o nível máximo, pode sair do loop
            }
        }


        // --- 4. LIMITAR AS OPÇÕES DE DIRECIONAMENTO DE ACORDO COM O NÍVEL DE ACESSO ---

        if ($dadosNivelMaximo) {
            // Se o usuário tem alguma permissão de listagem (nível 1, 2 ou 3)
            $opcoesValidas = $dadosNivelMaximo['opcoes'];
            $filtroPadrao  = $dadosNivelMaximo['padrao'];

            // Se o filtro atual NÃO for uma das opções válidas para o nível máximo, define o padrão
            if (!in_array($filtro['chamado_direcionamento'], $opcoesValidas)) {
                $filtro['chamado_direcionamento'] = $filtroPadrao;
            }
        } else {
            // 0: Sem permissão de listagem (nível padrão)
            $filtro['chamado_direcionamento'] = 'criado_usuario';
        }


        
        //Retorno da função
        return $filtro;
    }

    // Função auxiliar responsável por aplicar os filtros na consulta de chamados (incrementa a query)
     private function aplicarFiltrosDeConsulta($query, array $filtro)
    {
        //Filtro de direcionamento
        $query->when($filtro['chamado_direcionamento'], function ($query, $direcionamento) {

            switch ($direcionamento) {
                case 'todos':
                    break;
                case 'direcionado_unidade':
                    $query->where('unidade_atendente_id', auth()->user()->unidade_exercicio->id_setorsuperior);
                    break;
                case 'direcionado_setor':
                     // Agrupar as condições OR dentro de uma closure para garantir precedência
                        $query->where(function ($grupoDirecionamento) {
                            $grupoDirecionamento->where('setor_atendente_id', auth()->user()->lotacao->id_setor_exercicio)
                                ->orWhere(function ($condicaoUnidade) {
                                    $condicaoUnidade->where('unidade_atendente_id', auth()->user()->unidade_exercicio->id_setorsuperior)
                                        ->whereNull('setor_atendente_id');
                            });
                        });
                    break;
                case 'direcionado_usuario':
                    $query->where('usuario_atendente_id', auth()->user()->id_usuario);
                    break;
                case 'criado_usuario':
                    $query->where('usuario_solicitante_id', auth()->user()->id_usuario);
                    break;
            }
        });

        // Filtro de status
        $query->when($filtro['chamado_status'] && $filtro['chamado_status'] !== 'Todos', function ($query) use ($filtro) {
            $query->where('chamados.status', $filtro['chamado_status'] );
        });

        // Filtro por serviço prestado
        $query->when($filtro['chamado_servico'] && $filtro['chamado_servico'] !== 'todos', function ($query) use ($filtro) {
            $query->where('servico_id', $filtro['chamado_servico']);      
        });

        // Filtro por período de abertura
        $query->when($filtro['chamado_periodo_inicio'], function ($query, $dataInicio) {
            $query->where('created_at', '>=', $dataInicio);
        });
        $query->when($filtro['chamado_periodo_fim'], function ($query, $dataFim) {
            $dataFimFormatada = Carbon::parse($dataFim)->endOfDay();
            $query->where('created_at', '<=', $dataFimFormatada);
        });

        
        // Filtros de texto
        $query->when($filtro['chamado_titulo'], function ($query, $titulo) {
            $query->where('titulo', 'ILIKE', '%' . $titulo . '%');
        });

        $query->when($filtro['chamado_unidade_atendente'], function ($query, $unidade) {
            
        $unidadeBusca = trim($unidade); 
        $unidadeIds = DB::connection('pgsql_sgs')
            ->table('setores')
            ->where(function ($qBusca) use ($unidadeBusca) {
                
                $qBusca->where(DB::raw("TRIM(ds_nomesetor)"), 'ILIKE', '%' . $unidadeBusca . '%')
                    ->orWhere(DB::raw("TRIM(sg_setor)"), 'ILIKE', '%' . $unidadeBusca . '%');
            })
            ->pluck('id_setor')
            ->toArray();


        $query->whereIn('unidade_atendente_id', $unidadeIds); 
    });

        $query->when($filtro['chamado_setor_atendente'], function ($query, $atendente) {
            
            $atendenteBusca = trim($atendente); 
            $setorIds = DB::connection('pgsql_sgs')
                ->table('setores')
                ->where(function ($qBusca) use ($atendenteBusca) {
                    
                    $qBusca->where(DB::raw("TRIM(ds_nomesetor)"), 'ILIKE', '%' . $atendenteBusca . '%')
                        ->orWhere(DB::raw("TRIM(sg_setor)"), 'ILIKE', '%' . $atendenteBusca . '%');
                })
                ->pluck('id_setor') 
                ->toArray(); 
                

            $query->whereIn('setor_atendente_id', $setorIds); 
        });
        $query->when($filtro['chamado_setor_solicitante'], function ($query, $solicitante) {

            $setorIds = DB::connection('pgsql_sgs')
                ->table('setores')
                ->where('ds_nomesetor', 'ILIKE', '%' . $solicitante . '%')
                ->pluck('id_setor')
                ->toArray();
            $query->whereIn('setor_solicitante_id', $setorIds);
        });

        $query->when($filtro['chamado_usuario_solicitante'], function ($query, $usuario) {
                              
            $usuarioIds = DB::connection('pgsql_sgs')
                ->table('pessoas')
                ->join('usuarios', 'pessoas.ds_cpf', '=', 'usuarios.ds_cpf')
                ->where('pessoas.ds_nomepessoa', 'ILIKE', '%' . $usuario . '%')
                ->pluck('usuarios.id_usuario')
                ->toArray();
            $query->whereIn('usuario_solicitante_id', $usuarioIds);
        });

        $query->when($filtro['ordenacao'], function ($query, $ordenacao) {

            if ($ordenacao === 'tempo') {
                // Ordena pela data de criação
                $query->orderBy('created_at', 'asc');

            } elseif ($ordenacao === 'criticidade') {
                
                $query->join('servicos', 'chamados.servico_id', '=', 'servicos.id');
                $query->select('chamados.*');
                $query->orderBy('servicos.criticidade', 'desc');
            }
});

    }

    // /**
    //  * Realiza o upload de imagem para um chamado
    //  * 
    //  * @param \Illuminate\Http\UploadedFile $imagem Arquivo de imagem
    //  * @param string|null $servicoId ID do serviço associado ao chamado
    //  * @param string $siglaSetor Sigla do setor para o diretório de armazenamento
    //  * @param array|null $tombos Array de tombos associados ao chamado
    //  * @return string|null Caminho do arquivo salvo ou null se falhar
    //  */
    public function uploadImagemChamado($imagem, $servicoId, $siglaSetor, $tombos = [])
    {
        if (!$imagem || !$imagem->isValid()) {
            return null;
        }
        $extensao = $imagem->extension();
        // Formata os tombos para o nome do arquivo
        $tombosStr = empty($tombos) ? '' : implode('-', $tombos);
        // Cria o nome do arquivo com o padrão: data_hora_S{servicoId}_T_{tombos}.extensao
        $nomeArquivo = date("Y_m_d_H_i_s_") . "S" . ($servicoId ?? '') . "_T_" . $tombosStr . "." . $extensao;
        // Salva o arquivo no diretório específico do setor
        return $imagem->storeAs("images/chamados/{$siglaSetor}", $nomeArquivo, 'public');
    }

    public function encaminharChamado(Chamado $chamado, UpdateRegulacaoRequest $request,
        ?ChamadoTomboServicos $chamadoTomboServicos = null, 
        ?ChamadoHistoricoServicos $chamadoHistoricoServicos = null,
        ?ServicoServicos $servicoServicos = null)
    {
        $chamadoHistoricoServicos ??= app(ChamadoHistoricoServicos::class);
        $chamadoTomboServicos ??= app(ChamadoTomboServicos::class);
        $servicoServicos ??= app(ServicoServicos::class);

        $setorAtendente = $servicoServicos->getSetorResponsavelId($request->servico_id ?? null);

        $dados = [
            'unidade_atendente_id' => $request->unidadeId,
            'setor_atendente_id' => $setorAtendente,
            'servico_id' => $request->servico_id,
            'usuario_atendente_id' => $request->atendenteId,
            'status' => 'Aberto',
        ];

        $usuario = auth()->user();

        $relacao = !$setorAtendente ? 'setorUnidadeAtendente' : 'setorAtendente';
        $chamado->update($dados);
        $chamado->load($relacao);

        if($request->hasFile('imagemEncaminhamento')){
            $tombos = $chamadoTomboServicos->getTombosByChamadoId($chamado->id_chamado);
            $caminhoArquivo = $this->uploadImagemChamado($request->imagemEncaminhamento, $chamado->servico_id, $chamado->$relacao->sg_setor, $tombos);
        }
        $atividade = "Encaminhado para " . (!$setorAtendente ? 'unidade' : 'setor');

        $infoAdicional = [
                'atividade' => $atividade,
                'usuario_atendente_id' => $usuario->id_usuario,
        ];

        $chamadoHistoricoServicos->create($chamado->toArray(), $infoAdicional);

        if(isset($caminhoArquivo) || isset($request->observacao)){
            $mensagem = ChamadoMensagem::create([
                'chamado_id' => $chamado->id_chamado,
                'mensagem' => $request->observacao,
                'usuario_id' => $usuario->id_usuario,
                'tipo_usuario' => 'Atendente',
                'tipo_mensagem' => 'Informacao adicional',
                'caminho_arquivo' => $caminhoArquivo ?? null,
            ]);

            $chamado->load('mensagens');
            
            $ultimaMensagem = $chamado->mensagens->last();
            $ultimaMensagem->load('usuario.pessoa');

            $payload = [
                'mensagem' => [
                    'id' => $mensagem->id ?? null,
                    'mensagem' => $mensagem->mensagem ?? '',
                    'caminho_arquivo' => $mensagem->caminho_arquivo ?? '',
                    'tipo_usuario' => $mensagem->tipo_usuario ?? '',
                    'created_at' => $mensagem->created_at->format('d/m/Y, H:i'),
                    'usuario' => [
                        'id' => $mensagem->usuario->id_usuario,
                        'nome' => $mensagem->usuario->pessoa->nome ?? '',
                    ],
                ],
                'chamado' => [
                    'id' => $chamado->id_chamado,
                    'status' => $chamado->status,
                    'atendenteNome' => $request->atendenteId != null ? $chamado->usuarioAtendente->pessoa->nome : null ,
                    'unidadeAtendenteNome' => $chamado->setorUnidadeAtendente->ds_nomesetor?? null,
                    'servicoNome' => $chamado->servico->nome ?? '',
                    'ultimaAtualizacao' => $chamado->updated_at->format('d/m/Y, H:i'), 

                ],
                'ultimaSolicitacaoFechamento' => null,
            ];
            $this->notificacaoServicos->atualizaChat($payload);
        }

        $mensagem = ChamadoMensagem::create([
            'chamado_id' => $chamado->id_chamado,
            'mensagem' => "Chamado encaminhado para {$chamado->$relacao->ds_nomesetor} por {$usuario->pessoa->nome}",
            'usuario_id' => $usuario->id_usuario,
            'tipo_usuario' => 'Sistema',
            'tipo_mensagem' => $atividade,
        ]);

        $mensagem->load('usuario.pessoa');

            $payload = [
                'mensagem' => [
                    'id' => $mensagem->id ?? null,
                    'mensagem' => $mensagem->mensagem ?? '',
                    'caminho_arquivo' => $mensagem->caminho_arquivo ?? '',
                    'tipo_usuario' => $mensagem->tipo_usuario ?? '',
                    'created_at' => $mensagem->created_at->format('d/m/Y, H:i'),
                    'usuario' => [
                        'id' => $mensagem->usuario->id_usuario,
                        'nome' => $mensagem->usuario->pessoa->nome ?? '',
                    ],
                ],
                'chamado' => [
                    'id' => $chamado->id_chamado,
                    'status' => $chamado->status,
                    'atendenteNome' => $request->atendenteId != null ? $chamado->usuarioAtendente->pessoa->nome : null ,
                    'unidadeAtendenteNome' => $chamado->setorUnidadeAtendente->ds_nomesetor ?? null,
                    'servicoNome' => $chamado->servico->nome ?? '',
                    'ultimaAtualizacao' => $chamado->updated_at->format('d/m/Y, H:i'), 

                ],
                'ultimaSolicitacaoFechamento' => null,
            ];
            

            $this->notificacaoServicos->atualizaChat($payload);

    }

    /**
     * Verifica se o atendimento do chamado já foi iniciado ou se existem mensagens já trocadas
     * 
     * O atendimento é considerado iniciado se o chamado possui:
     * - Mais de uma informação adicional, pois significa que o chamado já passou pela regulação mais de uma vez
     * - Tem pelo menos mais de uma mensagem sem tipo_mensagem definido (mensagens comuns do bate papo)
     * 
     * @param \App\Models\Chamado $chamado O chamado a ser verificado
     * @return bool Retorna true se o atendimento do chamado ja foi iniciado, false caso contrário.
     */
    public function atendimentoIniciado(Chamado $chamado): bool
    {
        return ($chamado->mensagens()->whereNull('tipo_mensagem')->orWhere('tipo_mensagem', '')->exists()) || 
            ($chamado->mensagens()->where('tipo_mensagem', 'Informacao adicional')->skip(1)->exists());
    }

    public function iniciarAtendimento(Chamado $chamado, $usuarioId, ?ChamadoHistoricoServicos $chamadoHistoricoServicos = null)
    {
        $chamadoHistoricoServicos ??= app(ChamadoHistoricoServicos::class);
        
        $chamado->update([
            'usuario_atendente_id' => $usuarioId,
            'status' => 'Em andamento',
        ]);

        //Cria notificação para o solicitante ao ingressar no chamado
            $this->notificacaoServicos->criaNotificacao( 
                usuario_id: $chamado->usuario_solicitante_id,
                id_chamado: $chamado->id_chamado,
                mensagem: 'Seu chamado foi aceito. O status foi alterado para "Em andamento".',
                tipo: 'Atualização de chamado',
            );
            // Atualiza o chat para o usuário que recebeu o chamado
        
            $usuario = auth()->user();
            $payload = [
                'mensagem' => [
                    'mensagem' => 'Seu chamado foi aceito por '.$usuario->pessoa->nome,
                    'tipo_usuario' => 'Sistema',
                    'created_at' => now()->format('d/m/Y, H:i'),    
                    'usuario' => [ 
                        'nome' =>'Sistema',  
                    ],
                ],
                'chamado' => [
                    'id' => $chamado->id_chamado,
                    'status' => $chamado->status,
                    'ultimaAtualizacao' => $chamado->updated_at->format('d/m/Y, H:i'), // para atualizar a data
                    'atendenteNome' => $chamado->usuarioAtendente->pessoa->nome ?? '',
                    'unidadeAtendenteNome' => $chamado->setorUnidadeAtendente->ds_nomesetor ?? null,
                    'servicoNome' => $chamado->servico->nome ?? '',
                ],
            ];

            $this->notificacaoServicos->atualizaChat($payload);

        $chamadoHistoricoServicos->create($chamado->toArray(), ['atividade' => 'Atendimento iniciado']);
    }
}
