<?php

namespace App\Services;

use App\Models\HabilitarUnidadeOrganizacional ;
use Illuminate\Database\Eloquent\Collection;

class HabilitarUnidadesOrganizacionaisServicos
{
    public function __construct(protected HabilitarUnidadeOrganizacional $model)
    {}

    public function getHabilitadas(): ?Collection
    {
        return $this->model::with('setor')
            ->where('habilitado', true)->get()
            ->sortBy(fn ($setor) => $setor->setor->ds_nomesetor);
    }

    public function getHabilitadaPorCategoria(array $categorias): ?Collection
    {
        return $this->model::with('setor')
            ->where('habilitado', true)
            ->whereHas('servicos', function ($query) use ($categorias) {
                $query->where(function ($query) use ($categorias) {
                    foreach($categorias as $categoria){
                        $query->orWhereJsonContains('categorias_permitidas', $categoria);
                    }
                })->where('status', true);
            })->get()
            ->sortBy(fn($unidadeHabilitada) => $unidadeHabilitada->setor->ds_nomesetor);
    }
}