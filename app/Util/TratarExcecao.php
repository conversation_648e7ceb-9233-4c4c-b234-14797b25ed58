<?php

namespace App\Util;

use Exception;

class TratarExcecao
{
    public static function excecao(Exception $excecao)
    {
        switch ($excecao->getCode()) {
            case 23503:
                $tableNames = [
                    'chamados' => 'Chamado',
                    'servicos' => 'Serviço',
                    'grupo_setores' => 'Grupo de Setores',
                ];

                $errorMessage = $excecao->getMessage();
                $elemento = 'registro';
                $elementoRelacionado = 'outro registro';

                preg_match('/delete.*?on.*?table.*?"([^"]+)"/s', $errorMessage, $matches);
                if(isset($matches[1])) {
                    $tableName = $matches[1];
                    $elemento = $tableNames[$tableName] ?? ucfirst(str_replace('_', ' ', $tableName));
                }
                
                preg_match('/referenced.*?from.*?table.*?"([^"]+)"/s', $errorMessage, $matches);
                if(isset($matches[1])) {
                    $relatedTableName = $matches[1];
                    $elementoRelacionado = $tableNames[$relatedTableName] ?? ucfirst(str_replace('_', ' ', $relatedTableName));
                }

                $friendlyMessage = "Impossível excluir {$elemento} pois existe ao menos um(a) {$elementoRelacionado} relacionado(a) a ele.";
                Notificacao::mensagem('error', $friendlyMessage);
                break;
            case 23505:
                $attributeNames = [
                    'nome' => 'Nome',
                ];

                $errorMessage = $excecao->getMessage();
                $friendlyMessage = 'Ocorreu um erro ao salvar o registro, pois um valor informado já existe no sistema.';

                preg_match('/DETAIL:.*?Key.*?\(([^)]+)\)=\(([^)]+)\)/s', $errorMessage, $matches);
                if(isset($matches[1]) && isset($matches[2])) {
                    $columnName = $matches[1];
                    $value = $matches[2];
                    $friendlyAttributeName = $attributeNames[$columnName] ?? ucfirst(str_replace('_', ' ', $columnName));
                    
                    $friendlyMessage = "O campo '{$friendlyAttributeName}' não pode ser duplicado e o valor '{$value}' já existe no sistema.";
                }

                Notificacao::mensagem('error', $friendlyMessage);
                break;
            default:
                Notificacao::mensagem('error', $excecao->getMessage());
        }
    }
}
