<?php

namespace App\Repositories\Contracts;

use App\Models\HabilitarSetor;
use Illuminate\Database\Eloquent\Collection;

interface HabilitarSetoresIRepositorios
{
    public function ObterTodosPorHabilitacao(bool $habilitado): ?Collection;

    public function ObterPorIdSetor(int $idSetor): ?HabilitarSetor;
    public function obterHabilitadosComServicoporCategoria(array $setoresIds, array $categorias): ?Collection;
}
