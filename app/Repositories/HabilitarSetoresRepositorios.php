<?php

namespace App\Repositories;

use App\Models\HabilitarSetor;
use App\Repositories\Contracts\HabilitarSetoresIRepositorios;
use Illuminate\Database\Eloquent\Collection;

class HabilitarSetoresRepositorios implements HabilitarSetoresIRepositorios
{
    public function ObterTodosPorHabilitacao(bool $habilitado): ?Collection
    {
        return HabilitarSetor::with('setor')->where('habilitado', $habilitado)->get();
    }

    public function ObterPorIdSetor(int $idSetor): ?HabilitarSetor
    {
        return HabilitarSetor::with('setor')->where('setor_id', $idSetor)->first();
    }

    public function obterHabilitadosComServicoPorCategoria(array $setoresIds, array $categorias): ?Collection
    {
        return HabilitarSetor::with('setor')
            ->whereIn('setor_id', $setoresIds)
            ->where('habilitado', true)
            ->whereHas('servicos', function ($query) use ($categorias) {
                $query->where(function ($query) use ($categorias){
                    foreach($categorias as $categoria){
                        $query->orWhereJsonContains('categorias_permitidas', $categoria);
                    }
                })->where('status', true);
            })
            ->get()
            ->sortBy(fn ($setor) => $setor->setor->ds_nomesetor);
    }
}
