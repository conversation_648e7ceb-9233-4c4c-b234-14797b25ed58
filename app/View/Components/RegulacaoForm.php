<?php

namespace App\View\Components;

use App\Models\Chamado;
use App\Models\GrupoSetor;
use App\Models\HabilitarUnidadeOrganizacional;
use App\Services\HabilitarSetoresServicos;
use App\Services\HabilitarUnidadesOrganizacionaisServicos;
use App\Services\SGS\SetoresServicos;
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class RegulacaoForm extends Component
{
    public Chamado $chamado;
    public HabilitarUnidadeOrganizacional $unidade;
    public $unidadesHabilitadas;
    public $setores;
    public $grupos;
    public string $redirectTo;
    public bool $showButtons;
    /**
     * Create a new component instance.
     */
    public function __construct(
        Chamado $chamado,
        HabilitarUnidadesOrganizacionaisServicos $habilitarUnidadesOrganizacionaisServicos,
        HabilitarSetoresServicos $habilitarSetoresServicos,
        SetoresServicos $setoresServicos,
        string $redirectTo,
        HabilitarUnidadeOrganizacional $unidade = null,
        bool $showButtons = true,
    )
    {
        $this->chamado = $chamado;
        $this->unidade = $unidade ?? HabilitarUnidadeOrganizacional::where('unidade_id', $chamado->unidade_atendente_id)->firstOrFail();

        $categorias = $chamado->usuarioSolicitante->categorias->pluck('value')->toArray();

        $this->unidadesHabilitadas = $habilitarUnidadesOrganizacionaisServicos->getHabilitadaPorCategoria($categorias);
        $this->redirectTo = $redirectTo;
        $this->showButtons = $showButtons;

        $setoresFilhosIds = $setoresServicos->getSetoresFilhosIds($chamado->unidade_atendente_id);
        $this->setores = $habilitarSetoresServicos->obterHabilitadosComServicoPorCategoria($setoresFilhosIds, $categorias);
        $this->grupos = GrupoSetor::query()->where('unidade_responsavel_id', $this->unidade->unidade_id)->where('habilitado', true)->get();
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.regulacao-form');
    }
}
