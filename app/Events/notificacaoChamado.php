<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class notificacaoChamado implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $notificacao_id;
    public $id_chamado;

    protected $usuario_id;
     public function __construct($usuario_id, $notificacao_id, $id_chamado)
    {
        $this->usuario_id = $usuario_id;
        $this->notificacao_id = $notificacao_id;
        $this->id_chamado = $id_chamado;

    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn()
    {

        // O canal privado do usuário específico
        return new PrivateChannel('notificacao-chamado.' . $this->usuario_id);
    }

    //Define o nome do evento que será ouvido no front-end
    public function broadcastAs()
    {
        return 'notificacao-chamado';
    }

    //informa que dados serão enviados juntamente com o evento
    public function broadcastWith()
    {
        return [
            'notificacao_id' =>  $this->notificacao_id, 
            'id_chamado' =>  $this->id_chamado,
        ];
    }
}
