<?php

namespace App\Enums;

enum PessoasCategoriaEnum: string
{
    case Aluno = 'A';
    case Docente = 'D';
    case DocenteParcial = 'L';
    case Estagiario = 'E';
    case Prestador = 'P';
    case Tecnico = 'T';
    case TecnicoParcial = 'B';
    case Visitante = 'V';
    case OutroServidor = 'O';
    case OutroServidorParcial = 'R';

    public function getLabel(): string
    {
        return match ($this) {
            self::Aluno => 'Aluno',
            self::Docente => 'Docente',
            self::DocenteParcial => 'Docente (parcial)',
            self::Estagiario => 'Estagiário',
            self::Prestador => 'Prestador',
            self::Tecnico => 'Técnico',
            self::TecnicoParcial => 'Técnico (parcial)',
            self::Visitante => 'Visitante',
            self::OutroServidor => 'Outro Servidor',
            self::OutroServidorParcial => 'Outro Servidor (parcial)',
        };
    }
}
