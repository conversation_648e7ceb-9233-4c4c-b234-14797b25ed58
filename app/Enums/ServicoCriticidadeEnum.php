<?php

namespace App\Enums;

enum ServicoCriticidadeEnum: int
{
    case Baixo = 1;
    case Normal = 2;
    case Importante = 3;
    case Critico = 4;

    public function getLabel(): string
    {
        return match ($this) {
            self::Baixo => 'Baixo',
            self::Normal => 'Normal',
            self::Importante => 'Importante',
            self::Critico => 'Crítico',
        };
    }
    public function getBadgeColor(): string
    {
        return match ($this) {
            self::Baixo => 'text-bg-success',
            self::Normal => 'text-bg-info',
            self::Importante => 'text-bg-warning',
            self::Critico => 'text-bg-danger',
        };
    }
}
