# Product Overview

**Central de Atendimento** is a Laravel-based ticket management system designed for handling service requests and support tickets within an organization.

## Core Features

- **Ticket Management**: Create, track, and manage service requests (chamados)
- **User Authentication**: Integration with SGS (Sistema de Gestão de Serviços) for user authentication
- **Service Catalog**: Organized services by departments and organizational units
- **Workflow Management**: Ticket routing, assignment, and status tracking
- **Messaging System**: Built-in chat functionality for ticket communication
- **Reporting**: Generate reports on tickets and services
- **File Attachments**: Support for image uploads and file attachments
- **Multi-database Support**: Separate connections for main application and SGS integration

## Key Business Entities

- **Chamados** (Tickets): Main service requests with status tracking
- **Serviços** (Services): Catalog of available services
- **Usuários** (Users): System users integrated with SGS
- **Setores** (Departments): Organizational units for ticket routing
- **Perfis** (Profiles): User roles and permissions

## Target Users

- **Solicitantes** (Requesters): Users who create service requests
- **Atendentes** (Agents): Staff who handle and resolve tickets
- **Administradores** (Administrators): System administrators managing users, services, and departments
