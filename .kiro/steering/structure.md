# Project Structure

## Architecture Pattern

This Laravel application follows a **Service-Repository pattern** with clear separation of concerns:

- **Controllers** - Handle HTTP requests and responses
- **Services** - Contain business logic and complex operations
- **Repositories** - Data access layer (where implemented)
- **Models** - Eloquent ORM models with relationships

## Directory Structure

### Core Application (`app/`)

```
app/
├── Console/           # Artisan commands
├── Exceptions/        # Custom exception handlers
├── Helpers/           # Helper functions and utilities
├── Http/
│   ├── Controllers/   # HTTP request handlers
│   ├── Middleware/    # Request middleware
│   └── Requests/      # Form request validation
├── Models/            # Eloquent models
│   └── SGS/          # External SGS system models
├── Providers/         # Service providers
├── Repositories/      # Data access layer
│   └── Contracts/    # Repository interfaces
├── Services/          # Business logic layer
│   ├── Relatorios/   # Report generation services
│   └── SGS/          # SGS integration services
├── Util/             # Utility classes
└── View/
    └── Components/   # Blade components
```

### Database (`database/`)

```
database/
├── factories/        # Model factories for testing
├── migrations/       # Database schema migrations
└── seeders/         # Database seeders
```

### Resources (`resources/`)

```
resources/
├── css/             # Stylesheets
├── js/              # JavaScript files
├── sass/            # SASS source files
└── views/           # Blade templates
    ├── administrador/  # Admin interface views
    ├── auth/          # Authentication views
    ├── chamado/       # Ticket management views
    ├── components/    # Reusable view components
    ├── home/          # Dashboard views
    ├── layouts/       # Layout templates
    ├── regulacao/     # Ticket routing views
    └── relatorios/    # Report views
```

## Naming Conventions

### Models

- Use Portuguese business terms: `Chamado`, `Servico`, `Usuario`
- SGS models in `Models/SGS/` namespace
- Primary keys follow pattern: `id_{table_name}` (e.g., `id_chamado`)

### Controllers

- Suffix with `Controller`: `ChamadoController`
- RESTful resource methods where applicable
- Constructor dependency injection for services

### Services

- Suffix with `Servicos`: `ChamadoServicos`
- Contain complex business logic
- Handle file uploads, data processing, filtering

### Database

- Table names in Portuguese: `chamados`, `servicos`, `usuarios`
- Migration files include descriptive names
- Foreign key relationships clearly defined

### Views

- Organized by feature/module
- Use Blade components for reusable elements
- Portuguese file names matching business domain

## Key Architectural Patterns

### Multi-Database Connections

- `pgsql` - Main application database
- `pgsql_sgs` - External SGS system integration
- Models specify connection via `$connection` property

### Service Layer Pattern

- Controllers delegate business logic to Services
- Services handle complex operations, file uploads, data filtering
- Repository pattern used selectively for data access

### Component-Based Views

- Reusable Blade components in `resources/views/components/`
- Form components for consistent UI patterns
- Message components for different user types

### Permission System

- Role-based access control
- Permissions checked in controllers and services
- Integration with SGS user system

## File Organization Rules

1. **Controllers** should be thin - delegate to Services
2. **Services** contain business logic and complex queries
3. **Models** define relationships and basic accessors
4. **Views** organized by feature, not by type
5. **JavaScript/CSS** organized by functionality
6. **Migrations** use descriptive names with timestamps
