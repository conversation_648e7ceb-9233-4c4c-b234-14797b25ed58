# Technology Stack

## Backend Framework

- **Laravel 12.0** - PHP web application framework
- **PHP 8.2+** - Required PHP version

## Frontend

- **Laravel UI 4.4** - Authentication scaffolding and basic UI components
- **CoreUI 4.0** - Bootstrap-based admin template
- **Bootstrap 5.1** - CSS framework
- **Vite** - Frontend build tool and asset bundling
- **SASS** - CSS preprocessor

## Database

- **PostgreSQL** - Primary database (pgsql connection)
- **PostgreSQL SGS** - External SGS system integration (pgsql_sgs connection)
- **Laravel Migrations** - Database schema management

## Key Dependencies

- **Laravel Sanctum** - API authentication
- **<PERSON><PERSON> Tinker** - REPL for <PERSON>vel
- **DomPDF** - PDF generation for reports
- **<PERSON>vel Toastr** - Flash message notifications
- **Guzzle HTTP** - HTTP client library

## Development Tools

- **Laravel Sail** - Docker development environment
- **<PERSON><PERSON> Pint** - PHP code style fixer
- **PHPUnit** - Testing framework
- **Laravel Debugbar** - Development debugging tool
- **Faker** - Test data generation

## Build System & Commands

### Docker Setup

```bash
# Initial setup
make setup

# Start containers
make up
make start

# Stop containers
make down

# Fresh installation
make freshinstall
```

### Development Commands

```bash
# Inside container
docker exec -it central-atendimento-app bash

# Install dependencies
composer install
npm install

# Build assets
npm run build  # Production build
npm run dev    # Development build

# Laravel commands
php artisan key:generate
php artisan migrate --seed
php artisan storage:link
```

### Testing

```bash
make test
# or
docker compose exec app bash -c "vendor/bin/phpunit"
```

## Environment Configuration

- Copy `.env.example` to `.env` for local development
- Requires PostgreSQL connections for both main app and SGS integration
- Docker Compose handles service orchestration
- Mailpit for local email testing
