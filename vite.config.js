import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import path from 'path';
import { viteStaticCopy } from 'vite-plugin-static-copy'

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/sass/app.scss', 
                'resources/js/app.js', 
                'resources/css/toastr.min.css', 
                'resources/css/bootstrap-duallistbox.min.css',
                'resources/js/smart_search_list.js',
                'resources/js/graficos.js',
                'resources/css/custom.css',
                'resources/js/chamados.js',
                'resources/js/showImagePreview.js',
                'resources/js/regulacao.js',
                'resources/js/opcoesDeRelatorioChamados.js',
                'resources/js/opcoesDeRelatorioServicos.js',
                'resources/js/criterio.js',
                'resources/js/batepapo.js',
                'resources/js/chatUpdate.js',
                'resources/js/grupoSetores.js',
                'resources/js/servicos-index.js',
                'resources/js/servicos-manutencao.js',
            ],
                
            refresh: true,
        }),
        viteStaticCopy({
            targets: [
                {
                    src: path.join(__dirname, '/resources/images'),
                    dest: path.join(__dirname, '/public')
                },
                {
                    src: path.join(__dirname, '/resources/js/toastr.min.js'),
                    dest: path.join(__dirname, '/public/js')
                },
                {
                    src: path.join(__dirname, '/resources/js/jquery.min.js'),
                    dest: path.join(__dirname, '/public/js')
                },
                {
                    src: path.join(__dirname, '/resources/js/jquery.bootstrap-duallistbox.min.js'),
                    dest: path.join(__dirname, '/public/js')
                },
                {
                    src: path.join(__dirname, '/resources/js/app.js'),
                    dest: path.join(__dirname, '/public/js')
                }
            ]
        })
    ],
    resolve: {
        alias: {
            '~bootstrap': path.resolve(__dirname, 'node_modules/bootstrap'),
            '~fontawesome': path.resolve(__dirname, 'node_modules/@fortawesome/fontawesome-free/scss'),
        }
    },
});

