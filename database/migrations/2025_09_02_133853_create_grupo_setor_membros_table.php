<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('grupo_setor_membros', function (Blueprint $table) {
            $table->id();
            $table->foreignId('grupo_setor_id')->constrained('grupo_setores', 'id')->onDelete('cascade');
            $table->foreignId('setor_id')->constrained('habilitar_setores', 'setor_id')->onDelete('cascade');
            $table->integer('peso')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('grupo_setor_membros');
    }
};
