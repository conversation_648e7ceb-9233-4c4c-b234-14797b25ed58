<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('chamado_historicos', function (Blueprint $table) {
            //Torna as colunas setor_atendente_id e setor_atendente_nome nullable pois o setor pode estar vazio
            $table->unsignedBigInteger('setor_atendente_id')->nullable()->change();
            $table->string('setor_atendente_nome')->nullable()->change();
            
            //Adiciona as colunas unidade_atendente_id e unidade_atendente_nome, essas sempre vão estar preenchidas, mas para evitar erros no banco são declaradas como nullable
            $table->unsignedBigInteger('unidade_atendente_id')->nullable();
            $table->string('unidade_atendente_nome')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('chamado_historicos', function (Blueprint $table) {
            //Volta as colunas setor_atendente_id e setor_atendente_nome para serem obrigatórias
            $table->unsignedBigInteger('setor_atendente_id')->change();
            $table->string('setor_atendente_nome')->change();
            
            //Remove as colunas unidade_atendente_id e unidade_atendente_nome
            $table->dropColumn('unidade_atendente_id');
            $table->dropColumn('unidade_atendente_nome');
        });
    }
};
