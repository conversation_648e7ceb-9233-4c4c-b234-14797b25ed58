<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('servicos', function (Blueprint $table) {
            // Setor responsável pode ser vazio
            $table->unsignedBigInteger('setor_responsavel')->nullable()->foreign('setor_responsavel')->references('setor_id')->on('habilitar_setores')->onDelete('cascade')->change();
            
            // Adiciona a coluna unidade_responsavel
            $table->foreignId('unidade_responsavel')->nullable()->constrained('habilitar_unidades_organizacionais', 'unidade_id')->onDelete('cascade');
            
            // Adiciona a coluna categorias_permitidas
            $table->jsonb('categorias_permitidas')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('servicos', function (Blueprint $table) {
            // A coluna setor_responsavel não volta para NOT NULL para evitar erros

            // Remove colunas novas
            $table->dropColumn('unidade_responsavel');
            $table->dropColumn('categorias_permitidas');
        });
    }
};
