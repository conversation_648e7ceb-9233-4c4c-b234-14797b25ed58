<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('chamado_tombos', function (Blueprint $table) {
            // Altera a coluna 'nm_tombo' para o tipo TEXT, que não tem limite de caracteres.
            $table->text('nm_tombo')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
