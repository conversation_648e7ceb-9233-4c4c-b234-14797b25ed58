<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('habilitar_unidades_organizacionais', function (Blueprint $table) {
            $table->unsignedBigInteger('unidade_id')->unique()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('habilitar_unidades_organizacionais', function (Blueprint $table) {
            $table->dropUnique('habilitar_unidades_organizacionais_unidade_id_unique');
            $table->integer('unidade_id')->change();
        });
    }
};
