<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('chamados', function (Blueprint $table) {
            //Torna a coluna setor_atendente_id nullable pois o setor pode estar vazio
            $table->unsignedBigInteger('setor_atendente_id')->nullable()->change();

            //Adiciona a coluna unidade_atendente_id, essa coluna sempre vai estar preenchida, mas para evitar erros no banco ela está sendo construida como nullable
            $table->unsignedBigInteger('unidade_atendente_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('chamados', function (Blueprint $table) {
            //Volta a coluna setor_atendente_id para ser obrigatória
            $table->unsignedBigInteger('setor_atendente_id')->change();

            //Remove a coluna unidade_atendente_id
            $table->dropColumn('unidade_atendente_id');
        });
    }
};
