<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use App\Models\{Permissao, Perfil};

class PermissaoPerfilSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    
    {

        #Pefil de Adminstrador-------------------------------------------
            $permissoes = Permissao::whereIn('codigo',
            [
                'C001', 'C002', 'C003', 
                'R001',
                'U001', 'U002', 
                'SE001', 'SE002', 
                'UO001', 'UO002', 
                'S001', 'S002', 'S003', 'S004', 
                'P001', 'P002', 'P003', 'P004', 
                'H001',
                'GS001', 'GS002', 'GS003', 'GS004',
            ])
            ->pluck('id')->toArray();

            $perfil = Perfil::where('nome', 'Administrador')->first();
            $perfil->permissoes()->syncWithoutDetaching($permissoes);
        
        
        #Perfil de Chefe de Unidade-----------------------------------------

            $permissoes = Permissao::whereIn('codigo', 
            [
                'C002', 'C003', 
                'R001',
                'S001', 'S002', 'S003', 'S004', 
                'H001',
                'GS001', 'GS002', 'GS003', 'GS004',
            ])
            ->pluck('id')->toArray();

            $perfil = Perfil::where('nome','Chefe de Setor')->first();
            $perfil->permissoes()->syncWithoutDetaching($permissoes);

        #Perfil de Atentende---------------------------------------------
            $permissoes = Permissao::whereIn('codigo', 
                [
                    'C003',
                    'H001' 
                ])
                ->pluck('id')->toArray();

            $perfil = Perfil::where('nome', 'Atendente')->first();
            $perfil->permissoes()->syncWithoutDetaching($permissoes);


    }
}
