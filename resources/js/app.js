// import './bootstrap';  
import Chart from 'chart.js/auto';
window.Chart = Chart;

/**
 * Echo exposes an expressive API for subscribing to channels and listening
 * for events that are broadcast by Laravel. Echo and event broadcasting
 * allow your team to quickly build robust real-time web applications.
 */
import './echo';

document.addEventListener('DOMContentLoaded', () => {
    // Configuração do tooltip
    const tooltipTriggerList = Array.from(document.querySelectorAll('[data-coreui-toggle="tooltip"]'));
    tooltipTriggerList.map(tooltipTriggerEl => new coreui.Tooltip(tooltipTriggerEl));

    // Configuração do Toastr
    if(typeof toastr !== 'undefined') {
        toastr.options = {
            "positionClass": "toast-top-center",
            "timeOut": "4000",
        };
    }
});