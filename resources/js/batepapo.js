document.addEventListener('DOMContentLoaded', function() {
    // Verifica se existem erros no modal de encaminhamento para abri-lo automaticamente
    const encaminhamentoModal = document.getElementById('encaminhamentoModal');

    if(encaminhamentoModal?.dataset.encaminhamentoError === 'true') {
        new coreui.Modal(encaminhamentoModal).show();
    }

    // Função para enviar mensagem com Enter
    const textarea = document.getElementById('mensagem');
    const form = textarea?.closest('form');

    // Só executa se o textarea e o form existirem
    if (textarea && form) {
        textarea.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                form.submit();
            }
        });
    }

    // Função para abrir modal de imagem
    function openImageModal(imagePath) {
        document.getElementById('modalImage').src = imagePath;
        new coreui.Modal(document.getElementById('imageModal')).show();
    }
    // expõe para escopo global
    window.openImageModal = openImageModal;

    // Adicionar event listeners para todas as imagens
    const modalImages = document.querySelectorAll('.image-message');
    if (modalImages.length > 0) {
        modalImages.forEach(img => {
            img.addEventListener('click', function() {
                openImageModal(this.src);
            });
        });
    }

    // Torna o campo de observação obrigatório se houver imagem
    const imagemFileInput = document.getElementById('imagemEncaminhamento');
    const observacaoTextarea = document.getElementById('observacao');
    const observacaoLabel = document.getElementById('observacaoLabel');

    imagemFileInput.addEventListener('change', function(){
        const isRequired = this.files.length > 0;

        observacaoLabel.innerHTML = isRequired ?
            'Informação adicional:<span class="text-danger">*</span>' :
            'Informação adicional (Opcional):'
        
        observacaoTextarea.required = isRequired;
    });
});