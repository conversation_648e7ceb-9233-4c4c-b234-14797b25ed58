
document.addEventListener('DOMContentLoaded', function() {

(function () {
  const form = document.getElementById("formEnviarMensagemWrapper");
  if (!form) return;

  // Evita bind duplicado se seu JS for reexecutado (Livewire/Turbo/partial reloads etc.)
  if (form.dataset.ajaxBound === "1") return;
  form.dataset.ajaxBound = "1";

  const textarea = form.querySelector('textarea[name="mensagem"]');
  let sending = false;

  async function enviarMensagem() {
    if (sending) return; // anti-duplo envio
    sending = true;

    const formData = new FormData(form);

    try {
      const response = await fetch(form.action, {
        method: "POST",
        body: formData,
        headers: {
          "X-Requested-With": "XMLHttpRequest",
          "X-CSRF-TOKEN": form.querySelector('input[name="_token"]').value,
          "Accept": "application/json",
        },
      });

      if (!response.ok) {
        // Log para diagnóstico rápido:
        const raw = await response.text().catch(() => "");
        console.error("Resposta não OK:", response.status, raw);
        throw new Error("Erro na resposta do servidor.");
      }

      const data = await response.json();

      if (data.success) {
        // limpa textarea
        if (textarea) textarea.value = "";

        // limpa arquivo
        const fileInput = form.querySelector("input[type='file']");
        if (fileInput) fileInput.value = "";

        // limpa preview
        const preview = form.querySelector("#imagemPreview");
        if (preview) {
          preview.src = "";
          preview.style.display = "none";
        }
      } else {
        alert("Não foi possível enviar a mensagem. Tente novamente.");
      }
    } catch (err) {
      console.error("Erro ao enviar mensagem:", err);
      alert("Ocorreu um erro. Tente novamente.");
    } finally {
      sending = false;
    }
  }

  // 1) Intercepta submit do formulário (botão, Enter, requestSubmit etc.)
  //    Em CAPTURE para vencer outros listeners e impedir fluxo normal.
  form.addEventListener(
    "submit",
    (e) => {
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation?.();
      enviarMensagem();
    },
    true // capture
  );

  // 2) Enter no textarea -> envia via AJAX (Shift+Enter = quebra linha)
  if (textarea) {
    const onEnter = (e) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation?.();
        enviarMensagem();
      }
    };
    // Usar capture em keydown/keypress/keyup para bloquear qualquer outro handler global
    textarea.addEventListener("keydown", onEnter, true);
    textarea.addEventListener("keypress", onEnter, true);
    textarea.addEventListener("keyup", onEnter, true);
  }
})();





    //CONTROLA ENVIO DE REQUISIÇÕES DE CHAMADO (fechamento e continuação)
    document.addEventListener("submit", function(e) {
        const form = e.target;

        // garante que é um form do fechamento
        if (form.matches('form[action*="chamado"][action*="fechamento"]')) {
            e.preventDefault();

            const submitter = e.submitter; // botão clicado
            const formData = new FormData(form);

            // adiciona o botão clicado ao formData
            if (submitter && submitter.name) {
                formData.append(submitter.name, submitter.value);
            }

            fetch(form.action, {
                method: "POST",
                headers: {
                    "X-Requested-With": "XMLHttpRequest",
                    "X-CSRF-TOKEN": form.querySelector('input[name="_token"]').value
                },
                body: formData
            }).catch(error => console.error("Erro ao enviar formulário:", error));
        }
    });

    // ATUALIZA MENSAGENS E INTERFACE DO CHAT

    //variaveis
    const chat_values = document.getElementById('chat-values');
    const usuarioLogadoId =  chat_values.dataset.usuarioLogadoId;
    const chamadoFechamentoRoute = chat_values.dataset.chamadoFechamentoRoute;

    const chatContainer = document.getElementById('chat-messages');
    if (chatContainer) {
    
    const chamadoId = chatContainer.dataset.chamadoId;
    const csrfToken = window.csrfToken;

    // Função para renderizar mensagens
    function renderMessage(msg, chamado) {
        const isSistema = msg.tipo_usuario === 'Sistema';
       
        let html = '';

        const isDoUsuarioLogado = msg.usuario.id === usuarioLogadoId;
        const justifyContent = isDoUsuarioLogado ? 'justify-content-end' : 'justify-content-start';

       const isAtendente = msg.tipo_usuario === 'Atendente';
        const styleBG = isAtendente ? 'background-color: #3C4B64;' : '';
        const textColor = isAtendente ? 'text-white' : 'text-muted';
        const classColor = isAtendente ? 'text-white' : 'bg-light';
        const icon = isAtendente ? 'headset' : 'user';
        if (isSistema) {
           
            html = `
            <div class="d-flex mb-3 justify-content-center">
                <div class="flex-grow-1" style="max-width: 60%;">
                    <div class="d-flex flex-column p-3 rounded" style="background-color: #FFBC2F;">
                        <div class="d-flex align-items-center mb-1 justify-content-center">
                            <strong class="mx-1">Sistema</strong>
                        </div>
                        <div class="text-center">${msg.mensagem.replace(/\n/g, '<br>')}</div>
                        ${ (chamado.status === 'Solicitado fechamento' ) ? `
                        <form action="${chamadoFechamentoRoute}" method="POST" class="acoesFinalizarChamado">
                            <input type="hidden" name="_token" value="${csrfToken}">
                            <div class="d-flex justify-content-center gap-2 my-2">
                            ${(!(msg.usuario.id == usuarioLogadoId))?
                                '<button type="submit" name="acao" value="finalizar" class="btn btn-success">Finalizar chamado</button>'
                                : ''
                            } 
                                <button type="submit" name="acao" value="continuar" class="btn btn-secondary">Continuar chamado</button>
                            </div>
                        </form>` : ''}
                        <small class="text-muted d-flex justify-content-end">${msg.created_at}</small>
                    </div>
                </div>
            </div>`;
        } else {

            const userAvatar = `
            <div class="flex-shrink-0">
                 <div class="avatar ${classColor} rounded-circle p-2" style="${styleBG}">
                     <em class="fas fa-${icon}"></em>
                 </div>
             </div>`

             const avatarSide = ((chamado.atendenteId == usuarioLogadoId && msg.tipo_usuario === 'Atendente') || (chamado.atendenteId != usuarioLogadoId && msg.tipo_usuario === 'Solicitante')) ? 'flex-row-reverse' : '';
           
            const imageHtml = msg.caminho_arquivo ? `
            <div id="imageMessageNew">
                <img src="/storage/${msg.caminho_arquivo}" alt="Imagem enviada"
                     class="img-fluid mt-2 image-message"
                     style="object-fit: contain; max-height: 86px; max-width: 200px; cursor: pointer;">
            </div>` : '';

            html = `
            <div class="d-flex mb-3 ${justifyContent} ">
               <div class="w-100 d-flex ${avatarSide}">
                    ${userAvatar}
                    <div class="flex-grow-1" style="max-width: 80%;">
                        <div class="d-flex align-items-center mb-1 ${justifyContent}">
                            <strong class="mx-1">${msg.usuario.nome}</strong>
                        </div>
                        <div class="d-flex flex-column p-3 ${classColor} rounded" style="${styleBG}">
                            <div>${msg.mensagem.replace(/\n/g, '<br>')}</div>
                            ${imageHtml}
                            <small class="${textColor} d-flex justify-content-end">${msg.created_at}</small>
                        </div>
                    </div>
               </div>
            </div>`;
        }

        return html;
    }


    function atualizarChamadoUI(payload) {

        // ATUALIZA EVENTO DA ULTIMA IMAGEM ENVIADA
        // Adiciona event listener para abrir modal de imagem
        const imageMessage = document.getElementById('imageMessageNew');
        if (imageMessage) {
            const img = imageMessage.querySelector('img');
            if (img) {
                img.addEventListener('click', function() {
                    openImageModal(this.src);
                });
            }
            // Altera o id da última imagem para não repetir
            imageMessage.id = 'imageMessage';
        }


        const chamado = payload.chamado;

        // Função auxiliar para adicionar d-none apenas se não existir
        function esconderElemento(el) {
            if (!el.classList.contains("d-none")) {
                el.classList.add("d-none");
            }
        }

        // status badge //
        const badgeClass = {
            'Aberto' : 'bg-secondary',
            'Em andamento' : 'bg-info',
            'Parado' : 'bg-warning',
            'Concluido' : 'bg-success',
            'Solicitado fechamento' : 'bg-danger',
        };

        const badge = document.getElementById("statusBadge");
        badge.className = `badge ${badgeClass[chamado.status]}`;
        badge.textContent = chamado.status;

        // atendente //
        if(chamado.atendenteNome){
            const atendenteNome = document.getElementById("atendenteNome");
            atendenteNome.textContent = chamado.atendenteNome;
        }else{
            const atendenteNome = document.getElementById("atendenteNome");
            atendenteNome.textContent = '';
        }
        

        // unidade atendente //
        if(chamado.unidadeAtendenteNome != null){
            const labelUnidadeAtendente = document.getElementById("labelUnidadeAtendente");
            labelUnidadeAtendente.textContent = chamado.unidadeAtendenteNome;
        }

        // serviço //
        if(chamado.servicoNome){
            const labelServico = document.getElementById("labelServico");
            labelServico.textContent = chamado.servicoNome;
         }
        // última atualização //
        const ultimaAtualizacao = document.getElementById("ultimaAtualizacao");
        ultimaAtualizacao.textContent = chamado.ultimaAtualizacao; 

        // form finalizar //
        const formFinalizar = document.getElementById("formFinalizar");

        esconderElemento(formFinalizar);
        if (chamado.status === "Em andamento") {
            formFinalizar.classList.remove("d-none"); // mostra
        } else {
            formFinalizar.classList.add("d-none");    // esconde
        }
        // botão encaminhar //
        const btnEncaminhar = document.getElementById("btnEncaminhar");

        esconderElemento(btnEncaminhar);
        if (chamado.status !== "Concluido" && chamado.status !== "Solicitado fechamento" && chamado.mostrarEncaminhar == usuarioLogadoId) {
            btnEncaminhar.classList.remove("d-none"); // mostra
        } else {
            btnEncaminhar.classList.add("d-none");    // esconde
        }

        // formulario de envio de mensagem //
        const alertSolicitado = document.getElementById("alertSolicitadoFechamento");
        const alertConcluido = document.getElementById("alertConcluido");
        const formMensagem   = document.getElementById("formEnviarMensagemWrapper");

        // Esconde todas
        esconderElemento(alertSolicitado);
        esconderElemento(alertConcluido);
        esconderElemento(formMensagem);

        // Mostra apenas a que corresponde ao status
        if (chamado.status === "Solicitado fechamento") {
            alertSolicitado.classList.remove("d-none");
        } else if (chamado.status === "Concluido") {
            alertConcluido.classList.remove("d-none");
        } else if (chamado.status === "Em andamento") {
            formMensagem.classList.remove("d-none");
        }

        // form finalizar (finalizar / continuar)//
        const finalizarOpcoes = document.getElementsByClassName("acoesFinalizarChamado");

        if(chamado.status !== "Solicitado fechamento"){
            //adiciona d-none para todos os formulários, somente se não tiverem
            for (let i = 0; i < finalizarOpcoes.length; i++) {
                if (!finalizarOpcoes[i].classList.contains("d-none")) {
                    finalizarOpcoes[i].classList.add("d-none");
                }
            }
        }

    }


    window.Echo.private('chat-chamado.' + chamadoId)
        .listen('.chat-chamado', (event) => {
            const html = renderMessage(event.mensagem, event.chamado, event.ultimaSolicitacaoFechamento);
            chatContainer.insertAdjacentHTML('afterbegin', html);
            atualizarChamadoUI(event);
        });
    
    }

});


    