document.addEventListener('DOMContentLoaded', function(){
    const unidadeSelect = document.getElementById('unidade_responsavel');
    const setorSelect = document.getElementById('setor_responsavel');
    const grupoSelect = document.getElementById('grupo_responsavel');

    const divSetorResponsavel = document.getElementById('divSetorResponsavel');
    const divGrupoResponsavel = document.getElementById('divGrupoResponsavel');

    const selectedSetorId = setorSelect.dataset.selectedSetorId;
    const selectedGrupoId = grupoSelect.dataset.selectedGrupoId;

    /**
     * Carrega opções para um elemento <select> via ajax
     * @param {object} config - Objeto de configuração
     * @param {HTMLSelectElement} config.selectElement - Elemento <select> a ser populado
     * @param {string} config.url - URL da rota para carregar as opções (ex: '/get/setoresHabilitadosByUnidade/${unidadeId}')
     * @param {string} config.optionTextField - Nome do campo que contém o texto do option
     * @param {string} config.optionValueField - Nome do campo que contém o valor do option
     * @param {string|null} config.selectedId - O ID da opção a ser pré-selecionado
     * @param {string} config.entityName - Nome da entidade para mensagens (ex: 'setor')
     * @param {string} config.promptText - O texto a ser exibido como primeira opção (ex: 'Selecione um Setor') 
     */
    const loadSelectOptions = async (config) => {
        const { selectElement, url, optionTextField, optionValueField, selectedId, entityName, promptText} = config;
        
        selectElement.innerHTML = '<option value="">Carregando...</option>';

        try {
            const response = await fetch(`${url}`, {
                headers: { 'X-Requested-With': 'XMLHttpRequest'}
            });

            if(!response.ok) {
                throw new Error(`HTTP error: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();

            if(data.length == 0){
                selectElement.innerHTML = `<option value="">Nenhum ${entityName} encontrado</option>`;
                return false;
            } else {
                selectElement.innerHTML = `<option value="">${promptText}</option>`;
            }

            data.forEach(item => {
                const option = new Option(item[optionTextField], item[optionValueField]);
                if(selectedId && item[optionValueField] == selectedId){
                    option.selected = true;
                }
                selectElement.add(option);
            });

            return true;
        } catch (error) {
            console.error(`Erro ao carregar ${entityName}:`, error);
            toastr.error(`Ocorreu um erro ao carregar ${entityName}. Tente novamente.`);
            return false;
        }
    }

    const loadDependentData = async (unidadeId, preSelectedSetorId = null, preSelectedGrupoId = null) => {
        if(!unidadeId) {
            divSetorResponsavel.style.display = divGrupoResponsavel.style.display ='none';
            return;
        }

        //Carregar os setores
        const setorPromise = loadSelectOptions({
            selectElement: setorSelect,
            url: `/get/setoresHabilitadosByUnidade/${unidadeId}`,
            optionTextField: 'ds_nomesetor',
            optionValueField: 'id_setor',
            selectedId: preSelectedSetorId,
            entityName: 'setor',
            promptText: 'Selecione um setor',
        });
        //Carregar os grupos
        const grupoPromise = loadSelectOptions({
            selectElement: grupoSelect,
            url: `/get/gruposByUnidade/${unidadeId}`,
            optionTextField: 'nome',
            optionValueField: 'id',
            selectedId: preSelectedGrupoId,
            entityName: 'grupo',
            promptText: 'Selecione um grupo',
        });

        const [setorHasData, grupoHasData] = await Promise.all([setorPromise, grupoPromise]);

        if (!setorHasData) {
            setorSelect.disabled = true;
            divSetorResponsavel.style.display = 'none';
        }

        if (!grupoHasData) {
            grupoSelect.disabled = true;
            divGrupoResponsavel.style.display = 'none';
        }
    }


    loadDependentData(unidadeSelect.value, selectedSetorId, selectedGrupoId);

    unidadeSelect.addEventListener('change', function(){
        loadDependentData(this.value);
    });
})