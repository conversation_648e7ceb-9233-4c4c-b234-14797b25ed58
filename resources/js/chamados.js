document.addEventListener('DOMContentLoaded', function(){
    //Gerenciamento de setores

    const campusSelect = document.getElementById('campusId');
    const setorSelect = document.getElementById('setorSolicitanteId');

    if(campusSelect && setorSelect){
        const loadSetores = (campusId, selectedSetorId) => {
            setorSelect.innerHTML = '<option value="">Selecione um Setor</option>';
            if(campusId){
                fetch(`/get/setores/${campusId}`, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                    }
                })
                    .then(response => response.json())
                    .then(setores => {
                        setores.forEach(setor => {
                            const option = document.createElement('option');
                            option.value = setor.id_setor;
                            option.textContent = setor.ds_nomesetor;
                            if(selectedSetorId && setor.id_setor == selectedSetorId){
                                option.selected = true;
                            }
                            setorSelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('Erro ao carregar setores:', error));
            }
        };

        const defaultCampusId = campusSelect.value;
        const defaultSetorId = setorSelect.dataset.setorUsuario;

        if(defaultCampusId){
            loadSetores(defaultCampusId, defaultSetorId);
        }

        campusSelect.addEventListener('change', function(){
            loadSetores(this.value);
        });

    }

    //Gerenciamento de tombos
    const addTomboButton = document.getElementById('addTomboButton');
    const tombosContainer = document.getElementById('tombosContainer');
    const tomboLabel = document.getElementById('tomboLabel');

    if (tombosContainer) {
        tombosContainer.addEventListener('input', function(event) {
            if (event.target && event.target.matches('input[name="tombos[]"]')) {
                const input = event.target;
                input.value = input.value.replace(/\D/g, '');
            }
        });
    }
    

    const updateTomboLabel = () => {
        const tombosCount = tombosContainer.querySelectorAll('input[name="tombos[]"]').length;
        tomboLabel.innerHTML = tombosCount > 1 
        ? 'Tombos:<span class="text-danger">*</span>' 
        : 'Tombo (Opcional):';

        const fristTomboInput = tombosContainer.querySelector('input[name="tombos[]"]');
        if(fristTomboInput){
            fristTomboInput.required = tombosCount > 1;
        }
    }

    const addTombo = () => {
        const newTomboInput = document.createElement('div');
        newTomboInput.classList.add('d-flex', 'flex-column');
        newTomboInput.style.width = 'auto';
        newTomboInput.innerHTML = `
            <div class="input-group">
                <input type="text" name="tombos[]" class="form-control" style="width: 80px;" required>
                <button type="button" class="btn btn-outline-danger removeTomboButton">Remover</button>
            </div>
        `;
    
        tombosContainer.appendChild(newTomboInput);
        updateTomboLabel();
    
        newTomboInput.querySelector('.removeTomboButton').addEventListener('click', function () {
            tombosContainer.removeChild(newTomboInput);
            updateTomboLabel();
        });

    }

    if(addTomboButton){
        addTomboButton.addEventListener('click', function() {
            addTombo();
        });
    }

    const existingTombos = tombosContainer.querySelectorAll('.d-flex.flex-column');
    existingTombos.forEach(function (element) {
        const removeTomboButton = element.querySelector('.removeTomboButton');
        if (removeTomboButton) {
            removeTomboButton.addEventListener('click', function () {
                tombosContainer.removeChild(element);
                updateTomboLabel();
            });
        }
    });
    
    updateTomboLabel();
});
