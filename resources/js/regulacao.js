document.addEventListener('DOMContentLoaded', function(){
    // Elementos do DOM
    const setorSelect = document.getElementById('setorId');
    const servicoSelect = document.getElementById('servico_id');
    const unidadeSelect = document.getElementById('unidadeId');
    const atendenteSelect = document.getElementById('atendenteId');
    const grupoSelect = document.getElementById('grupoId');
    const imagemFileInput = document.getElementById('imagemEncaminhamento');
    const observacaoTextarea = document.getElementById('observacao');
    const observacaoLabel = document.getElementById('observacaoLabel');
    const divSelecaoSetor = document.getElementById('divSelecaoSetor');
    const divSelecaoServico = document.getElementById('divSelecaoServico');
    const divSelecaoAtendente = document.getElementById('divSelecaoAtendente');
    const divSelecaoGrupo = document.getElementById('divSelecaoGrupo');
    const formRegulacao = document.getElementById('formRegulacao');
    const btnIniciarAtendimento = document.getElementById('btnIniciarAtendimento');
    const btnExcluirSetor = document.getElementById('btnExcluirSetor');
    const btnExcluirGrupo = document.getElementById('btnExcluirGrupo');

    // Dados predefinidos
    const selectedServicoId = servicoSelect.dataset.selectedServico;
    const unidadeOriginalId = unidadeSelect.dataset.unidadeOriginalId;
    const selectedAtendenteId = atendenteSelect.dataset.selectedAtendente;
    const usuarioSolicitanteId = formRegulacao.dataset.usuarioSolicitanteId;

    const updateUIState = () => {
        const unidadeId = unidadeSelect.value;
        const setorId = setorSelect.value;
        const grupoId = grupoSelect.value;
        const atendenteId = atendenteSelect.value;
        const isUnidadeOriginal = unidadeId == unidadeOriginalId;

        const dependentFields = [divSelecaoSetor, divSelecaoGrupo, divSelecaoServico, divSelecaoAtendente];
        const dependentSelects = [setorSelect, grupoSelect, servicoSelect, atendenteSelect];

        // Se não for a unidade original, esconde os campos, desabilita os selects e esconde o botão de iniciar atendimento
        if(!isUnidadeOriginal){
            dependentFields.forEach(field => field.style.display = 'none');
            dependentSelects.forEach(select => select.disabled = true);
            if(btnIniciarAtendimento){
                btnIniciarAtendimento.style.display = 'none';
            }
            return;
        }

        // Se for a unidade original, mostra os campos, habilita os selects e mostra o botão de iniciar atendimento
        dependentFields.forEach(field => field.style.display = 'block');
        dependentSelects.forEach(select => select.disabled = false);
        if(btnIniciarAtendimento){
            // Verifica se tem um atendente selecionado, se tiver esconde o botão de iniciar atendimento
            btnIniciarAtendimento.style.display = atendenteId ? 'none' : 'inline-block';
        }

        // Se um setor está selecionado, desabilita o grupo
        setorSelect.disabled = !!grupoId;
        // Se um grupo está selecionado, desabilita o setor
        grupoSelect.disabled = !!setorId;

        // Se um grupo estiver selecionado, esconde o atendente e o botão de iniciar atendimento
        if(grupoId){
            divSelecaoAtendente.style.display = 'none';
            atendenteSelect.disabled = true;
            if(btnIniciarAtendimento){
                btnIniciarAtendimento.style.display = 'none';
            }
        }

        // Se não houver opções nos selects de grupo/setor esconde eles
        if(setorSelect.options.length <= 1){
            setorSelect.disabled = true;
            divSelecaoSetor.style.display = 'none';
        }
        if(grupoSelect.options.length <= 1){
            grupoSelect.disabled = true;
            divSelecaoGrupo.style.display = 'none';
        }
    };

    /**
     * Carrega opções para um elemento <select> via ajax
     * @param {object} config - Objeto de configuração
     * @param {HTMLSelectElement} config.selectElement - Elemento <select> a ser populado
     * @param {string} config.url - URL da rota para carregar as opções (ex: '/get/atendentes/{setorId}')
     * @param {string} config.optionTextField - Nome do campo que contém o texto do option
     * @param {string} config.optionValueField - Nome do campo que contém o valor do option
     * @param {string|null} config.selectedId - O ID da opção a ser pré-selecionado
     * @param {string} config.entityName - Nome da entidade para mensagens (ex: 'serviços')
     * @param {string} config.promptText - O texto a ser exibido como primeira opção (ex: 'Selecione um Serviço')
     */
    const loadSelectOptions = async (config) => {
        const { selectElement, url, optionTextField, optionValueField, selectedId, entityName, promptText } = config;

        selectElement.innerHTML = '<option value="">Carregando...</option>';

        try {
            const response = await fetch(`${url}`, {
                headers: { 'X-Requested-With': 'XMLHttpRequest'}
            });

            if(!response.ok) {
                throw new Error(`HTTP error: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            
            if(data.length == 0){
                selectElement.innerHTML = `<option value="">Nenhum ${entityName} encontrado</option>`;
            } else {
                selectElement.innerHTML = `<option value="">${promptText}</option>`;
            }

            data.forEach(item => {
                const option = new Option(item[optionTextField], item[optionValueField]);
                if(selectedId && item[optionValueField] == selectedId){
                    option.selected = true;
                }
                selectElement.add(option);
            });
        } catch (error) {
            console.error(`Erro ao carregar ${entityName}:`, error);
            alert(`Ocorreu um erro ao carregar ${entityName}. Tente novamente`);
        }
    };

    /**
     * Carrega tanto os serviços quanto os atendentes de um setor
     * @param {object} config - Objeto de configuração
     * @param {string|null} config.setorId - O ID do setor
     * @param {string|null} config.grupoId - o ID do grupo
     * @param {string|null} config.preSelectedServicoId - O ID do serviço a ser pré-selecionado
     * @param {string|null} config.preSelectedAtendenteId - O ID do atendente a ser pré-selecionado
     */
    const loadDependentData = async (config) => {
        const {setorId = null, grupoId = null, preSelectedServicoId = null, preSelectedAtendenteId = null} = config;
        let servicoUrl, atendenteUrl;
        const labelServico = divSelecaoServico.querySelector('label');
        const labelAtendente = divSelecaoAtendente.querySelector('label');

        if(setorId){
            labelServico.innerHTML = 'Serviço do setor:<span class="text-danger">*</span>';
            labelAtendente.innerHTML = 'Atendente do setor (Opcional):';
            servicoUrl = `/get/servicosBySetor/${setorId}/andUsuario/${usuarioSolicitanteId}`;
            atendenteUrl = `/get/atendentesBySetor/${setorId}`;
        }else if(grupoId){
            labelServico.innerHTML = 'Serviço do grupo:<span class="text-danger">*</span>';
            servicoUrl = `/get/servicosByGrupo/${grupoId}/andUsuario/${usuarioSolicitanteId}`;
            atendenteUrl = null;
        }else{
            labelServico.innerHTML = 'Serviço da unidade:<span class="text-danger">*</span>';
            labelAtendente.innerHTML = 'Atendente da unidade (Opcional):';
            servicoUrl = `/get/servicosOnlyByUnidade/${unidadeOriginalId}/andUsuario/${usuarioSolicitanteId}`;
            atendenteUrl = `/get/atendentesByUnidade/${unidadeOriginalId}`;
        }
        //Carregar os serviços
        const promises = [
            loadSelectOptions({
                selectElement: servicoSelect,
                url: servicoUrl,
                optionTextField: 'nome',
                optionValueField: 'id',
                selectedId: preSelectedServicoId,
                entityName: 'serviço',
                promptText: 'Selecione um serviço',
            })
        ];

        //Carregar os atendentes
        if(atendenteUrl){
            promises.push(
                loadSelectOptions({
                    selectElement: atendenteSelect,
                    url: atendenteUrl,
                    optionTextField: 'ds_nomepessoa',
                    optionValueField: 'id_usuario',
                    selectedId: preSelectedAtendenteId,
                    entityName: 'atendente',
                    promptText: 'Selecione um atendente',
                })
            );
        } else {
            atendenteSelect.innerHTML = '<option value="">Não aplicável</option>';
        }

        await Promise.all(promises);
    };
    
    const handleSelectionChange = async (config) => {
        const {} = config;
        await loadDependentData(config);
        updateUIState();
    }

    // Inicia com valores iniciais
    const init = async () => {
        await loadDependentData({setorId: setorSelect.value, grupoId: grupoSelect.value, preSelectedServicoId: selectedServicoId, preSelectedAtendenteId: selectedAtendenteId});
        updateUIState();
    }
    
    init();

    // Eventos
    setorSelect.addEventListener('change', function(){
        handleSelectionChange({setorId: this.value});
    });

    grupoSelect.addEventListener('change', function(){
        handleSelectionChange({grupoId: this.value});
    })

    unidadeSelect.addEventListener('change', function(){
        updateUIState();
    });

    btnExcluirSetor.addEventListener('click', function(){
        if(!setorSelect.value) return;
        setorSelect.value = '';
        handleSelectionChange({setorId: null});
    })

    btnExcluirGrupo.addEventListener('click', function(){
        if(!grupoSelect.value) return;
        grupoSelect.value = '';
        handleSelectionChange({grupoId: null});
    })

    atendenteSelect.addEventListener('change', function () {
        if (this.value) {
            btnIniciarAtendimento.style.display = 'none';
        } else {
            btnIniciarAtendimento.style.display = 'inline-block';
        }
    });

    // Torna o campo de observação obrigatório se houver imagem
    imagemFileInput.addEventListener('change', function(){
        const isRequired = this.files.length > 0;

        observacaoLabel.innerHTML = isRequired ?
            'Informação adicional:<span class="text-danger">*</span>' :
            'Informação adicional (Opcional):'
        
        observacaoTextarea.required = isRequired;
    });
});