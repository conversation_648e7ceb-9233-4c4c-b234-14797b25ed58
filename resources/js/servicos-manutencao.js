document.addEventListener('DOMContentLoaded', function(){
    const unidadeSelect = document.getElementById('unidade_responsavel');
    const setorSelect = document.getElementById('setor_responsavel_select');
    const grupoSelect = document.getElementById('grupo_responsavel_select');

    const setorInput = document.getElementById('setor_responsavel_hidden');
    const grupoInput = document.getElementById('grupo_responsavel_hidden');

    const btnExcluirSetorResponsavel = document.getElementById('btnExcluirSetorResponsavel');
    const btnExcluirGrupoResponsavel = document.getElementById('btnExcluirGrupoResponsavel');

    const divSetorResponsavel = document.getElementById('divSetorResponsavel');
    const divGrupoResponsavel = document.getElementById('divGrupoResponsavel');

    const selectedSetorId = setorSelect.dataset.selectedSetorId;
    const selectedGrupoId = grupoSelect.dataset.selectedGrupoId;

    /**
     * Carrega opções para um elemento <select> via ajax
     * @param {object} config - Objeto de configuração
     * @param {HTMLSelectElement} config.selectElement - Elemento <select> a ser populado
     * @param {string} config.url - URL da rota para carregar as opções (ex: '/get/setoresHabilitadosByUnidade/${unidadeId}')
     * @param {string} config.optionTextField - Nome do campo que contém o texto do option
     * @param {string} config.optionValueField - Nome do campo que contém o valor do option
     * @param {string|null} config.selectedId - O ID da opção a ser pré-selecionado
     * @param {string} config.entityName - Nome da entidade para mensagens (ex: 'setor')
     * @param {string} config.promptText - O texto a ser exibido como primeira opção (ex: 'Selecione um Setor') 
     */
    const loadSelectOptions = async (config) => {
        const { selectElement, url, optionTextField, optionValueField, selectedId, entityName, promptText} = config;
        
        selectElement.disabled = true;
        selectElement.innerHTML = '<option value="">Carregando...</option>';

        try {
            const response = await fetch(`${url}`, {
                headers: { 'X-Requested-With': 'XMLHttpRequest'}
            });

            if(!response.ok) {
                throw new Error(`HTTP error: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();

            if(data.length == 0){
                selectElement.innerHTML = `<option value="">Nenhum ${entityName} encontrado</option>`;
                return false;
            } else {
                selectElement.innerHTML = `<option value="">${promptText}</option>`;
            }

            data.forEach(item => {
                const option = new Option(item[optionTextField], item[optionValueField]);
                if(selectedId && item[optionValueField] == selectedId){
                    option.selected = true;
                }
                selectElement.add(option);
            });

            return true;
        } catch (error) {
            console.error(`Erro ao carregar ${entityName}:`, error);
            toastr.error(`Ocorreu um erro ao carregar ${entityName}. Tente novamente.`);
            selectElement.innerHTML = `<option value="">Erro ao carregar</option>`;
            return false;
        }
    }

    /**
     * Função auxiliar para atualizar a visibilidade e o estado de um select.
     * @param {HTMLSelectElement} selectElement - O select a ser atualizado
     * @param {HTMLDivElement} divElement - A div do select
     * @param {boolean} hasData - Se o select possui dados
     * @param {boolean} counterpartIsSelected - Se o select oposto estiver selecionado
     */
    const updateSelectState = (selectElement, divElement, hasData, counterpartIsSelected) => {
        // Se o select tem dados, mostra ele, caso contrário o esconde
        divElement.style.display = hasData ? 'block' : 'none';
        // Se o select não tem dados ou se sua contraparte está selecionada desabilita ele, caso contrário habilita
        selectElement.disabled = !hasData || counterpartIsSelected;
    }

    const loadDependentData = async (unidadeId, preSelectedSetorId = null, preSelectedGrupoId = null) => {

        if(preSelectedSetorId) {
            toggleSelect(grupoSelect, grupoInput, true);
        }
        if(preSelectedGrupoId) {
            toggleSelect(setorSelect, setorInput, true);
        }

        //Carregar os setores
        const setorPromise = loadSelectOptions({
            selectElement: setorSelect,
            url: `/get/setoresHabilitadosByUnidade/${unidadeId}`,
            optionTextField: 'ds_nomesetor',
            optionValueField: 'id_setor',
            selectedId: preSelectedSetorId,
            entityName: 'setor',
            promptText: 'Selecione um setor',
        });
        //Carregar os grupos
        const grupoPromise = loadSelectOptions({
            selectElement: grupoSelect,
            url: `/get/gruposByUnidade/${unidadeId}`,
            optionTextField: 'nome',
            optionValueField: 'id',
            selectedId: preSelectedGrupoId,
            entityName: 'grupo',
            promptText: 'Selecione um grupo',
        });

        const [setorHasData, grupoHasData] = await Promise.all([setorPromise, grupoPromise]);

        updateSelectState(setorSelect, divSetorResponsavel, setorHasData, !!preSelectedGrupoId);
        updateSelectState(grupoSelect, divGrupoResponsavel, grupoHasData, !!preSelectedSetorId);
    }

    const toggleSelect = (selectElement, inputElement, disabled, clearValue = true) => {
        selectElement.disabled = disabled;
        if(clearValue) {
            selectElement.value = inputElement.value = '';
        }
    }

    loadDependentData(unidadeSelect.value, selectedSetorId, selectedGrupoId);

    unidadeSelect.addEventListener('change', function(){
        toggleSelect(grupoSelect, grupoInput, false);
        toggleSelect(setorSelect, setorInput, false);
        loadDependentData(this.value);
    });

    setorSelect.addEventListener('change', function(){
        setorInput.value = this.value;
        toggleSelect(grupoSelect, grupoInput, !!this.value);
    });

    grupoSelect.addEventListener('change', function(){
        grupoInput.value = this.value;
        toggleSelect(setorSelect, setorInput, !!this.value);
    })

    btnExcluirSetorResponsavel.addEventListener('click', function(){
        setorSelect.value = setorInput.value = '';
        toggleSelect(grupoSelect, grupoInput, false, false);
    });

    btnExcluirGrupoResponsavel.addEventListener('click', function(){
        grupoSelect.value = grupoInput.value = '';
        toggleSelect(setorSelect, setorInput, false, false);
    });
})