const grupoSetoresManager = {
    /**
     * Referências centralizadas para os elementos da DOM.
     */
    elements: {
        form: null,
        unidadeSelect: null,
        tabelaBody: null,
        addSetorButton: null,
    },

    /**
     * Estado da pagina, como a lista de setores disponíveis.
     */
    state: {
        setoresDisponiveis: [],
        isLoading: false,
    },

    /**
     * Ponto de entrada. Inicia o módulo, busca os elementos e anexa os eventos.
     */
    init() {
        // Mapeia os elementos da DOM para o objeto 'elements'
        this.elements.form = document.getElementById('grupo-setores-form');
        this.elements.unidadeSelect = document.getElementById('unidade_responsavel_id');
        this.elements.tabelaBody = document.getElementById('tabela-setores-membros-body');
        this.elements.addSetorButton = document.getElementById('adicionar-setor');

        // Anexa os listeners de eventos
        this.bindEvents();

        // Se uma unidade já estiver selecionada (vindo de um 'old()'),
        // carrega os setores para as linhas existentes.
        if (this.elements.unidadeSelect.value) {
            this.handleUnidadeChange(this.elements.unidadeSelect.value, false); // false para não limpar a tabela já que a ideia é popular com os dados do old()
        }
    },
    

    /**
     * Centraliza a anotação de todos os eventos.
     */
    bindEvents() {
        // Evento para quando o usuário muda a unidade responsável
        this.elements.unidadeSelect.addEventListener('change', (e) => {
            this.handleUnidadeChange(e.target.value, true); // true para limpar a tabela
        });

        // Evento para o botão de adicionar nova linha de setor
        this.elements.addSetorButton.addEventListener('click', () => this.addNewRow());

        // Delegação de eventos para o corpo da tabela
        // Remoção de linhas
        this.elements.tabelaBody.addEventListener('click', (e) => {
            if (e.target.classList.contains('remover-setor')) {
                this.removeRow(e.target);
            }
        });

        // Atualização dos selects
        this.elements.tabelaBody.addEventListener('change', (e) => {
            if (e.target.classList.contains('setor-select')) {
                this.updateAllSelectsUI();
            }
        });
    },

    /**
     * Controla a mudança da unidade, buscando dados e atualizando a UI.
     * @param {string} unidadeId - O ID da unidade selecionada.
     * @param {boolean} clearTable - Se a tabela deve ser limpa antes de popular.
     */
    async handleUnidadeChange(unidadeId, clearTable = false) {
        if (clearTable) {
            this.elements.tabelaBody.innerHTML = '';
        }

        if (!unidadeId) {
            this.state.setoresDisponiveis = [];
            this.setLoading(false);
            return;
        }

        this.setLoading(true);
        try {
            await this.fetchSetores(unidadeId);
            
            this.updateAllSelectsUI();

        } catch (error) {
            console.error('Falha ao processar mudança de unidade:', error);
            if (typeof toastr !== 'undefined') {
                toastr.error('Ocorreu um erro ao carregar os setores. Tente novamente.');
            }
        } finally {
            this.setLoading(false);
        }
    },

    /**
     * Busca os setores da unidade via ajax.
     * @param {string} unidadeId - O ID da unidade.
     */
    async fetchSetores(unidadeId) {
        try {
            const response = await fetch(`/get/setoresHabilitadosByUnidade/${unidadeId}`, {
                headers: { 'X-Requested-With': 'XMLHttpRequest' }
            });

            if (!response.ok) {
                throw new Error(`HTTP error: ${response.status} ${response.statusText}`);
            }

            this.state.setoresDisponiveis = await response.json();
        } catch (error) {
            console.error('Erro ao carregar setores:', error);
            this.state.setoresDisponiveis = []; // Limpa em caso de erro
            throw error; // Propaga o erro para ser tratado no 'handleUnidadeChange'
        }
    },

    /**
     * Adiciona uma nova linha de setor na tabela.
     */
    addNewRow() {
        if (!this.elements.unidadeSelect.value) {
            if (typeof toastr !== 'undefined') toastr.error('Primeiro, selecione uma unidade responsável.');
            return;
        }

        const rows = this.elements.tabelaBody.querySelectorAll('.setor-row');
        const isIncomplete = [...rows].some(row => !row.querySelector('.setor-select')?.value || !row.querySelector('.peso-input')?.value);

        if (isIncomplete) {
            if (typeof toastr !== 'undefined') toastr.error('Preencha todas as linhas antes de adicionar uma nova.');
            return;
        }

        const newIndex = rows.length;
        const tr = document.createElement('tr');
        tr.className = 'setor-row';
        tr.innerHTML = `
            <td>
                <select name="setores[${newIndex}][setor_id]" class="form-select setor-select" required>
                    <option value="">Selecione um setor</option>
                </select>
            </td>
            <td>
                <input type="number" name="setores[${newIndex}][peso]" class="form-control peso-input" value="1" min="1" required>
            </td>
            <td>
                <button type="button" class="btn btn-outline-danger remover-setor">Remover</button>
            </td>
        `;

        this.elements.tabelaBody.appendChild(tr);

        this.updateAllSelectsUI();
    },

    /**
     * Remove uma linha da tabela e re-organiza os índices.
     * @param {HTMLElement} buttonElement - O botão 'remover' que foi clicado.
     */
    removeRow(buttonElement) {
        buttonElement.closest('.setor-row').remove();
        this.updateRowIndexes();
        this.updateAllSelectsUI();
    },

    /**
     * Re-indexa os nomes dos inputs/selects para garantir que o Laravel os receba como um array contínuo.
     */
    updateRowIndexes() {
        const rows = this.elements.tabelaBody.querySelectorAll('.setor-row');
        rows.forEach((row, index) => {
            const setorSelect = row.querySelector('.setor-select');
            const pesoInput = row.querySelector('.peso-input');

            if (setorSelect) setorSelect.name = `setores[${index}][setor_id]`;
            if (pesoInput) pesoInput.name = `setores[${index}][peso]`;
        });
    },

    /**
     * Atualiza as opções de todos os selects de setor na tabela,
     * garantindo que não haja duplicatas.
     */
    updateAllSelectsUI() {
        const allSelects = this.elements.tabelaBody.querySelectorAll('.setor-select');
        
        // Coleta todos os valores que já estão em uso, exceto os vazios.
        const setoresUsados = [...allSelects]
            .map(select => select.value || select.dataset.selected)
            .filter(Boolean);

        allSelects.forEach(select => {
            // Guarda o valor que estava selecionado antes de limpar
            // Usa 'dataset.selected' como fallback para o valor do old() do Laravel
            const previouslySelectedId = select.value || select.dataset.selected;
            
            // Limpa o dataset para não interferir em futuras atualizações
            if (select.dataset.selected) {
                select.dataset.selected = '';
            }

            // Limpa as opções atuais
            select.innerHTML = '<option value="">Selecione um setor</option>';

            // Popula com as opções disponíveis
            this.state.setoresDisponiveis.forEach(setor => {
                const setorIdStr = String(setor.id_setor);
                
                // A opção deve ser mostrada se:
                // 1. Ela é a que já estava selecionada neste campo.
                // 2. Ela não está sendo usada por nenhum outro campo.
                if (previouslySelectedId === setorIdStr || !setoresUsados.includes(setorIdStr)) {
                    const option = new Option(setor.ds_nomesetor, setor.id_setor);
                    select.appendChild(option);
                }
            });

            // Restaura a seleção anterior
            select.value = previouslySelectedId || '';
            select.disabled = this.state.isLoading;
        });
    },

    /**
     * Controla o estado de carregamento da UI, desabilitando/habilitando campos.
     * @param {boolean} isLoading - True para mostrar o loading, false para esconder.
     */
    setLoading(isLoading) {
        this.state.isLoading = isLoading;
        this.elements.unidadeSelect.disabled = isLoading;
        this.elements.addSetorButton.disabled = isLoading;
        
        this.elements.tabelaBody.querySelectorAll('.setor-select, .peso-input, .remover-setor').forEach(el => {
            el.disabled = isLoading;
        });
        
        if(isLoading && this.elements.tabelaBody.querySelectorAll('.setor-row').length === 0) {
            // Mostrar uma linha de "loading" se a tabela estiver vazia
            this.elements.tabelaBody.innerHTML = `
                <tr class="loading-row">
                    <td colspan="3">Carregando setores...</td>
                </tr>
            `;
        } else if (!isLoading && this.elements.tabelaBody.querySelector('.loading-row')) {
             this.elements.tabelaBody.innerHTML = ''; // Limpa a mensagem de loading
        }
    }
};

// Inicia o gerenciador quando o DOM estiver pronto.
document.addEventListener('DOMContentLoaded', () => grupoSetoresManager.init());