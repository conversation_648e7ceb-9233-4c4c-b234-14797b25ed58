import Echo from 'laravel-echo';

import Pusher from 'pusher-js';
window.Pusher = Pusher;

window.Echo = new Echo({
    broadcaster: "pusher",
    key: import.meta.env.VITE_PUSHER_APP_KEY,
    cluster: import.meta.env.VITE_PUSHER_APP_CLUSTER ?? 'mt1',
    wsHost: import.meta.env.VITE_PUSHER_HOST?? 'localhost',
    wsPort: import.meta.env.VITE_PUSHER_PORT?? 6001,
    forceTLS: false, //Alterer para true em produção
    // wssPort: import.meta.env.VITE_PUSHER_PORT,
    enabledTransports: ["ws"], //alterar para wss na produção
});
