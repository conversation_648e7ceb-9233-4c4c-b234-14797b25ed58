@extends('layouts.app')

@section('content')
<style>
    .avatar-sm {
        width: 32px;
        height: 32px;
    }
</style>


<div class="card mb-4">
    <div class="card-header bg-primary text-white py-3">
        <h4 class="card-title mb-0 fw-semibold">
            <i class="fas fa-chart-bar me-2"></i>
            Relatórios do Sistema
        </h4>
    </div>
    <div class="card-body p-0">
        <!-- Navegação por Abas -->
        <ul class="nav nav-tabs" id="relatorioTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="chamados-tab" data-bs-toggle="tab" data-bs-target="#chamados"
                        type="button" role="tab" aria-controls="chamados" aria-selected="true">
                    <i class="fas fa-ticket-alt me-2"></i>
                    Relatório de Chamados
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="atividades-tab" data-bs-toggle="tab" data-bs-target="#atividades"
                        type="button" role="tab" aria-controls="atividades" aria-selected="false">
                    <i class="fas fa-tasks me-2"></i>
                    Relatório de Atividades
                </button>
            </li>
        </ul>

        <!-- Conteúdo das Abas -->
        <div class="tab-content border border-top-0 rounded-bottom bg-white p-4" id="relatorioTabContent">
            <!-- Aba Chamados -->
            <div class="tab-pane fade show active" id="chamados" role="tabpanel" aria-labelledby="chamados-tab">
                <div class="mb-3">
                    <h5 class="text-primary mb-2">
                        <i class="fas fa-info-circle me-2"></i>
                        Informações e relatórios sobre os chamados do sistema
                    </h5>
                    <p class="text-muted small mb-4">Consulte níveis de acesso para visualizar informações detalhadas</p>
                </div>

                <form action="{{ route('relatorios.index') }}" method="GET" id="formChamados">
                    <div class="row g-3">
                        <!-- Período -->
                        <div class="col-md-6">
                            <div class="row">
                                <div class="col-6">
                                    <label for="data_inicio" class="form-label fw-medium">Data Inicial</label>
                                    <input type="date" class="form-control" id="data_inicio" name="data_inicio"
                                           value="{{ request('data_inicio') }}">
                                </div>
                                <div class="col-6">
                                    <label for="data_fim" class="form-label fw-medium">Data Final</label>
                                    <input type="date" class="form-control" id="data_fim" name="data_fim"
                                           value="{{ request('data_fim') }}">
                                </div>
                            </div>
                        </div>

                        <!-- Palavra-chave -->
                        <div class="col-md-6">
                            <label for="palavra_chave" class="form-label fw-medium">Palavra-chave</label>
                            <input type="text" class="form-control" id="palavra_chave" name="palavra_chave"
                                   placeholder="Digite a palavra-chave" value="{{ request('palavra_chave') }}">
                        </div>

                        <!-- Tipo de busca -->
                        <div class="col-md-6">
                            <label class="form-label fw-medium">Buscar por</label>
                            <div class="d-flex gap-2">
                                <input type="radio" class="btn-check" name="buscar_por" id="buscar_titulo" value="titulo"
                                       {{ request('buscar_por', 'titulo') == 'titulo' ? 'checked' : '' }}>
                                <label class="btn btn-outline-primary btn-sm me-2" for="buscar_titulo">Título</label>

                                <input type="radio" class="btn-check" name="buscar_por" id="buscar_descricao" value="descricao"
                                       {{ request('buscar_por') == 'descricao' ? 'checked' : '' }}>
                                <label class="btn btn-outline-primary btn-sm" for="buscar_descricao">Descrição</label>
                            </div>
                        </div>

                        <!-- Tipo de correspondência -->
                        <div class="col-md-6">
                            <label for="tipo_correspondencia" class="form-label fw-medium">Tipo de correspondência</label>
                            <select class="form-select" id="tipo_correspondencia" name="tipo_correspondencia">
                                <option value="todas" {{ request('tipo_correspondencia', 'todas') == 'todas' ? 'selected' : '' }}>
                                    Todas as palavras
                                </option>
                                <option value="qualquer" {{ request('tipo_correspondencia') == 'qualquer' ? 'selected' : '' }}>
                                    Qualquer uma das palavras
                                </option>
                                <option value="expressao" {{ request('tipo_correspondencia') == 'expressao' ? 'selected' : '' }}>
                                    Expressão exata
                                </option>
                            </select>
                        </div>

                        <!-- Campus -->
                        <div class="col-md-4">
                            <label for="campus" class="form-label fw-medium">Campus</label>
                            <select class="form-select" id="campus" name="campus">
                                <option value="">Todos os Campus</option>
                                <option value="campus1" {{ request('campus') == 'campus1' ? 'selected' : '' }}>Campus Principal</option>
                                <option value="campus2" {{ request('campus') == 'campus2' ? 'selected' : '' }}>Campus Norte</option>
                                <option value="campus3" {{ request('campus') == 'campus3' ? 'selected' : '' }}>Campus Sul</option>
                            </select>
                        </div>

                        <!-- Último usuário de destino -->
                        <div class="col-md-4">
                            <label for="ultimo_usuario_destino" class="form-label fw-medium">Último Usuário de Destino</label>
                            <select class="form-select" id="ultimo_usuario_destino" name="ultimo_usuario_destino">
                                <option value="">Todos os usuários</option>
                                <option value="atendente1" {{ request('ultimo_usuario_destino') == 'atendente1' ? 'selected' : '' }}>João Silva</option>
                                <option value="atendente2" {{ request('ultimo_usuario_destino') == 'atendente2' ? 'selected' : '' }}>Maria Santos</option>
                            </select>
                        </div>

                        <!-- Tombo -->
                        <div class="col-md-4">
                            <label for="tombo" class="form-label fw-medium">Tombo</label>
                            <input type="text" class="form-control" id="tombo" name="tombo"
                                   placeholder="Ex: 123456" value="{{ request('tombo') }}">
                        </div>

                        <!-- Número do chamado -->
                        <div class="col-md-4">
                            <label for="numero_chamado" class="form-label fw-medium">Nº do Chamado</label>
                            <input type="number" class="form-control" id="numero_chamado" name="numero_chamado"
                                   placeholder="Ex: 12345" value="{{ request('numero_chamado') }}">
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Filtrar
                            </button>
                            <a href="{{ route('relatorios.index') }}" class="btn btn-secondary">
                                <i class="fas fa-eraser me-2"></i>Limpar Filtros
                            </a>
                        </div>
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#relatorioModal">
                            <i class="fas fa-chart-line me-2"></i>Gerar Relatório
                        </button>
                    </div>
                </form>

                <!-- Exemplos de resultados para Chamados -->
                <div class="mt-4">
                    <h6 class="text-secondary mb-3">
                        <i class="fas fa-list me-2"></i>Exemplos de Resultados
                    </h6>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Nº Chamado</th>
                                    <th>Título</th>
                                    <th>Status</th>
                                    <th>Campus</th>
                                    <th>Último Destino</th>
                                    <th>Data Abertura</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="badge bg-primary">#12345</span></td>
                                    <td>Problema na impressora do laboratório</td>
                                    <td><span class="badge bg-warning">Em Andamento</span></td>
                                    <td>Campus Principal</td>
                                    <td>João Silva</td>
                                    <td>15/10/2024 09:30</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-primary">#12346</span></td>
                                    <td>Solicitação de acesso ao sistema</td>
                                    <td><span class="badge bg-success">Concluído</span></td>
                                    <td>Campus Norte</td>
                                    <td>Maria Santos</td>
                                    <td>14/10/2024 14:15</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Aba Atividades -->
            <div class="tab-pane fade" id="atividades" role="tabpanel" aria-labelledby="atividades-tab">
                <div class="mb-3">
                    <h5 class="text-success mb-2">
                        <i class="fas fa-info-circle me-2"></i>
                        Informações e relatórios de usuários do sistema
                    </h5>
                    <p class="text-muted small mb-4">Relatórios de atividades e produtividade dos atendentes</p>
                </div>

                <form action="{{ route('relatorios.index') }}" method="GET" id="formAtividades">
                    <div class="row g-3">
                        <!-- Período -->
                        <div class="col-md-6">
                            <div class="row">
                                <div class="col-6">
                                    <label for="data_inicio_ativ" class="form-label fw-medium">Data de Início</label>
                                    <input type="date" class="form-control" id="data_inicio_ativ" name="data_inicio"
                                           value="{{ request('data_inicio') }}">
                                </div>
                                <div class="col-6">
                                    <label for="data_fim_ativ" class="form-label fw-medium">Data de Fim</label>
                                    <input type="date" class="form-control" id="data_fim_ativ" name="data_fim"
                                           value="{{ request('data_fim') }}">
                                </div>
                            </div>
                        </div>

                        <!-- Atendente -->
                        <div class="col-md-6">
                            <label for="atendente" class="form-label fw-medium">
                                Atendente
                                <small class="text-muted">(somente para administrador/chefe de unidade)</small>
                            </label>
                            <select class="form-select" id="atendente" name="atendente">
                                <option value="">Todos os atendentes</option>
                                <option value="atendente1" {{ request('atendente') == 'atendente1' ? 'selected' : '' }}>João Silva</option>
                                <option value="atendente2" {{ request('atendente') == 'atendente2' ? 'selected' : '' }}>Maria Santos</option>
                                <option value="atendente3" {{ request('atendente') == 'atendente3' ? 'selected' : '' }}>Pedro Oliveira</option>
                                <option value="atendente4" {{ request('atendente') == 'atendente4' ? 'selected' : '' }}>Ana Costa</option>
                            </select>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-search me-2"></i>Filtrar
                            </button>
                            <a href="{{ route('relatorios.index') }}" class="btn btn-secondary">
                                <i class="fas fa-eraser me-2"></i>Limpar Filtros
                            </a>
                        </div>
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#relatorioAtividadesModal">
                            <i class="fas fa-chart-line me-2"></i>Gerar Relatório
                        </button>
                    </div>
                </form>

                <!-- Exemplos de resultados para Atividades -->
                <div class="mt-4">
                    <h6 class="text-secondary mb-3">
                        <i class="fas fa-list me-2"></i>Exemplos de Resultados
                    </h6>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Atendente</th>
                                    <th>Chamados Atendidos</th>
                                    <th>Tempo Médio</th>
                                    <th>Taxa de Resolução</th>
                                    <th>Período</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <i class="fas fa-user text-white small"></i>
                                            </div>
                                            João Silva
                                        </div>
                                    </td>
                                    <td><span class="badge bg-info">45 chamados</span></td>
                                    <td>2h 30min</td>
                                    <td><span class="badge bg-success">95%</span></td>
                                    <td>01/10 - 15/10/2024</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-success rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <i class="fas fa-user text-white small"></i>
                                            </div>
                                            Maria Santos
                                        </div>
                                    </td>
                                    <td><span class="badge bg-info">38 chamados</span></td>
                                    <td>1h 45min</td>
                                    <td><span class="badge bg-success">98%</span></td>
                                    <td>01/10 - 15/10/2024</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Relatório de Chamados -->
<div class="modal fade" id="relatorioModal" tabindex="-1" aria-labelledby="relatorioModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="relatorioModalLabel">
                    <i class="fas fa-chart-line me-2"></i>
                    Opções do Relatório de Chamados
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fechar"></button>
            </div>
            <form action="{{ route('relatorios.index') }}" method="GET" target="_blank">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Ordenar relatório por:</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="ordenar_por" id="ordenar_por_status" value="status" checked>
                                <label class="form-check-label" for="ordenar_por_status">Status</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="ordenar_por" id="ordenar_por_data" value="data">
                                <label class="form-check-label" for="ordenar_por_data">Data de Abertura</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="ordenar_por" id="ordenar_por_campus" value="campus">
                                <label class="form-check-label" for="ordenar_por_campus">Campus</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Formato do relatório:</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="formato" id="formato_pdf" value="pdf" checked>
                                <label class="form-check-label" for="formato_pdf">PDF</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="formato" id="formato_excel" value="excel">
                                <label class="form-check-label" for="formato_excel">Excel</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Fechar
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-download me-2"></i>Gerar Relatório
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para Relatório de Atividades -->
<div class="modal fade" id="relatorioAtividadesModal" tabindex="-1" aria-labelledby="relatorioAtividadesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="relatorioAtividadesModalLabel">
                    <i class="fas fa-chart-bar me-2"></i>
                    Opções do Relatório de Atividades
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fechar"></button>
            </div>
            <form action="{{ route('relatorios.index') }}" method="GET" target="_blank">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Ordenar relatório por:</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="ordenar_por" id="ordenar_por_atendente" value="atendente" checked>
                                <label class="form-check-label" for="ordenar_por_atendente">Nome do Atendente</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="ordenar_por" id="ordenar_por_quantidade" value="quantidade">
                                <label class="form-check-label" for="ordenar_por_quantidade">Quantidade de Chamados</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="ordenar_por" id="ordenar_por_periodo" value="periodo">
                                <label class="form-check-label" for="ordenar_por_periodo">Período</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Formato do relatório:</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="formato" id="formato_pdf_ativ" value="pdf" checked>
                                <label class="form-check-label" for="formato_pdf_ativ">PDF</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="formato" id="formato_excel_ativ" value="excel">
                                <label class="form-check-label" for="formato_excel_ativ">Excel</label>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <label class="form-label fw-bold">Incluir no relatório:</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="incluir_graficos" id="incluir_graficos" checked>
                            <label class="form-check-label" for="incluir_graficos">Gráficos de produtividade</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="incluir_detalhes" id="incluir_detalhes">
                            <label class="form-check-label" for="incluir_detalhes">Detalhes dos chamados</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Fechar
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-download me-2"></i>Gerar Relatório
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar tooltips se necessário
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Funcionalidade para os radio buttons customizados
    const radioButtons = document.querySelectorAll('input[type="radio"][name="buscar_por"]');
    radioButtons.forEach(function(radio) {
        radio.addEventListener('change', function() {
            // Remover classe active de todos os labels
            document.querySelectorAll('label[for^="buscar_"]').forEach(function(label) {
                label.classList.remove('active');
            });
            // Adicionar classe active ao label selecionado
            if (this.checked) {
                document.querySelector('label[for="' + this.id + '"]').classList.add('active');
            }
        });
    });

    // Definir estado inicial dos radio buttons
    const checkedRadio = document.querySelector('input[type="radio"][name="buscar_por"]:checked');
    if (checkedRadio) {
        document.querySelector('label[for="' + checkedRadio.id + '"]').classList.add('active');
    }

    // Validação de datas
    const dataInicio = document.getElementById('data_inicio');
    const dataFim = document.getElementById('data_fim');
    const dataInicioAtiv = document.getElementById('data_inicio_ativ');
    const dataFimAtiv = document.getElementById('data_fim_ativ');

    function validarDatas(inicio, fim) {
        if (inicio.value && fim.value) {
            if (new Date(inicio.value) > new Date(fim.value)) {
                fim.setCustomValidity('A data final deve ser posterior à data inicial');
                fim.reportValidity();
                return false;
            } else {
                fim.setCustomValidity('');
                return true;
            }
        }
        return true;
    }

    if (dataInicio && dataFim) {
        dataInicio.addEventListener('change', () => validarDatas(dataInicio, dataFim));
        dataFim.addEventListener('change', () => validarDatas(dataInicio, dataFim));
    }

    if (dataInicioAtiv && dataFimAtiv) {
        dataInicioAtiv.addEventListener('change', () => validarDatas(dataInicioAtiv, dataFimAtiv));
        dataFimAtiv.addEventListener('change', () => validarDatas(dataInicioAtiv, dataFimAtiv));
    }
});
</script>

@endsection