@extends('layouts.app')

@section('content')
        <div class="card mb-4">
            <div class="card-header py-3">
                <h5 class="card-title">Relatório da Pesquisa</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('relatorio' , 'chamados') }}" method="GET">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="status">Status do Chamado</label>
                            <select name="status" id="status" class="form-control">
                                <option value="">Todos os Chamados</option>
                                <option value="aberto" {{ $request->status == 'aberto' ? 'selected' : '' }}>Aberto</option>
                                <option value="fechado" {{ $request->status == 'concluidos' ? 'selected' : '' }}>Concluido</option>
                                <option value="em_andamento" {{ $request->status == 'em_andamento' ? 'selected' : '' }}>Em Andamento</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="usuario_id">Usuário Solicitante</label>
                            <select name="usuario_id" id="usuario_id" class="form-control">
                                <option value="">Todos</option>
                            </select>
                        </div>
                        <div class="col-md-3 col-lg-6">
                            <label for="setor_id">Setor Solicitante</label>
                            <select name="setor_id" id="setor_id" class="form-control">
                                <option value="">Todos os Setores</option>                                                                                                         
                                    <option value="116">COORDENAÇÃO DE DESENVOLVIMENTO DE SISTEMAS</option>
                                    <option value="113">COORDENAÇÃO DE SUPORTE E MANUTENÇÃO</option>
                                    <option value="273">DEPARTAMENTO DE SISTEMAS DE INFORMAÇÕES</option>
                                    <option value="13">DIVISÃO DE SUPORTE A TELEFONIA</option>
                                    <option value="10">SEÇÃO DE INFRAESTRUTURA DE REDES</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="atendente_id">Atendente</label>
                            <select name="atendente_id" id="atendente_id" class="form-control">
                                <option value="">Todos</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="status">Serviço Solicitado</label>
                            <select name="servicos" id="servicos" class="form-control">
                                <option value="">Serviço Prestado</option>
                                <option value="">Relatorio de estudantes</option>
                            </select>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                        <button type="submit" class="btn btn-primary">Filtrar</button>
                        <a href="{{ route('relatorio' , 'chamados') }}" class="btn btn-secondary">Limpar Filtros</a>
                        </div>
                        <a href="#" class="btn btn-info btn-md" data-coreui-toggle="modal" data-coreui-target="#relatorioModal"><em class="fa-solid fa-chart-line"></em> Relatório da pesquisa</a>
                    </div>
                </form>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold">Chamados Filtrados</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Status</th>
                                <th>Serviço</th>
                                <th>Usuario</th>
                                <th>Setor</th>
                                <th>Atendente</th>
                                <th>Data Abertura</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($chamados as $chamado)
                                <tr>
                                    <td>{{ $chamado->id_chamado }}</td>
                                    <td>{{ $chamado->status }}</td>
                                    <td>{{ $chamado->servico->nome ?? 'N/A' }}</td>
                                    <td>{{ $chamado->usuarioSolicitante->pessoa->ds_nomepessoa ?? 'N/A' }}</td>
                                    <td>{{ $chamado->setorSolicitante->ds_nomesetor ?? 'N/A' }}</td>
                                    <td>{{ $chamado->usuarioAtendente->pessoa->ds_nomepessoa ?? 'N/A' }}</td>
                                    <td>{{ $chamado->created_at->format('d/m/Y H:i') }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="7">Nenhum chamado encontrado com os filtros aplicados.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                    <div class="d-flex justify-content-left">
                        {{ $chamados->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="relatorioModal" tabindex="-1" aria-labelledby="relatorioModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="relatorioModalLabel">Opções do Relatório</h5>
                    <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <style>
                    #data_container {
                        display: none;
                    }
                </style>
                <form action="{{ route('relatorio','chamados') }}" method="GET" target="_blank">
                    <div class="modal-body">
                        <div class="mb-4">
                            <label class="form-label fw-bold">Ordenar relatório por:</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="ordenar_por" id="ordenar_por_status" value="status" checked>
                                <label class="form-check-label" for="ordenar_por_status">
                                    Status
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="ordenar_por" id="ordenar_por_data" value="data">
                                <label class="form-check-label" for="ordenar_por_data">
                                    Data
                                </label>
                            </div>
                        </div>

                        <div class="mb-3" id="status_container">
                            <label class="form-label fw-bold">Status Selecionado</label>
                            <div class="alert alert-info">
                                {{ $filtro['chamado_status'] ?? 'Todos' }}
                            </div>
                        </div>

                        <div class="mb-3" id="data_container">
                            <label for="data_abertura" class="form-label fw-bold">Data de Abertura</label>
                            <input type="date" class="form-control" id="data_abertura" name="data_inicio" value="{{ $filtro['chamado_abertura'] ?? '' }}">
                        </div>

                        <input type="hidden" name="chamado_status" value="{{ $filtro['chamado_status'] ?? 'Todos' }}">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Fechar</button>
                        <button type="submit" class="btn btn-primary">Gerar Relatório</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection