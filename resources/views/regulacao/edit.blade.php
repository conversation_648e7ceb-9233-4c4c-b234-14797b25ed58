@extends('layouts.app')

@section('content')
    
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div class="col-lg-11 col-md-10 text-center">
                <h3 class="card-title"><PERSON>camin<PERSON></h3>
            </div>
            <div class="col-lg-1 col-md-2 ms-auto d-flex align-items-center justify-content-center">
                <img src="{{ asset("storage/{$unidade->caminho_logomarca}") }}" class="card-img-right img-fluid" alt="logo da unidade organizacional">
            </div>
        </div>
        <div class="card-body">
            {{-- Informações do usuario solicitante --}}
            <div class="row ">
                <div class="form-group col-lg-5">
                    <label for="solicitante" class="fw-semibold">Solicitante:</label>
                    <p class="form-control bg-light">{{$chamado->usuarioSolicitante->pessoa->ds_nomepessoa}}</p>
                </div>
                <div class="form-group col-lg-4">
                    <label for="email" class="fw-semibold">E-mail:</label>
                    <p class="form-control bg-light">{{$chamado->usuarioSolicitante->pessoa->ds_emailprincipal}}</p>
                </div>
                <div class="form-group col-lg-3">
                    <label for="telefone" class="fw-semibold">Telefone para contato:</label>
                    <p class="form-control bg-light">{{$chamado->telefone_contato}}</p>
                </div>
            </div>
            {{-- Informações da localização do chamado --}}

            <div class="row ">
                <div class="form-group col-lg-5">
                    <label for="setor" class="fw-semibold">Setor:</label>
                    <p class="form-control bg-light">{{$chamado->setorSolicitante->ds_nomesetor}}</p>
                </div>
                <div class="form-group col-lg-3">
                    <label for="campus" class="fw-semibold">Campus:</label>
                    <p class="form-control bg-light">{{$chamado->setorSolicitante->campus->ds_nomecampus}}</p>
                </div>
            </div>

            {{-- Informações do chamado --}}
            <div class="row">
                <div class="col-lg-8 col-md-9">
                    <div class="form-group">
                        <label for="titulo" class="fw-semibold">Título:</label>
                        <p class="form-control bg-light">{{$chamado->titulo}}</p>
                    </div>
                    <div class="form-group">
                        <label for="descricao" class="fw-semibold">Descrição:</label>
                        <p class="form-control bg-light" style="white-space: pre-line;">{{$chamado->descricao}}</p>
                    </div>
                </div>
                @if($chamado->caminho_arquivo)
                    <div class="col-lg-2 col-md-3 d-flex flex-column align-items-center justify-content-start text-center mb-3">
                        <label for="imagem" class="fw-semibold ">Imagem:</label>
                        <div class="w-100 form-control bg-light ">
                            <a href="" data-coreui-toggle="modal" data-coreui-target="#imageModal">
                                <img src="{{ asset("storage/{$chamado->caminho_arquivo}") }}" class="img-fluid" style="object-fit: contain; max-height: 100px; max-width: 100%;" alt="imagem anexada ao chamado">
                            </a>
                        </div>
                    </div>
                    <x-image-modal :src="asset('storage/' . $chamado->caminho_arquivo)" alt="imagem anexada ao chamado"/>
                @endif
            </div>
            @if ($chamado->temMensagens)
                <div class="row d-flex justify-content-center">
                    <div class="col-lg-6 col-md-8 col-10 alert alert-info d-flex align-items-center justify-content-center gap-2 shadow-sm rounded">
                        <em class="fas fa-comments me-2" aria-hidden="true"></em>
                        <div class="text-center">
                            Este chamado já possui mensagens trocadas.
                            <a href="{{route('chamado.show', $chamado->id_chamado)}}" class="fw-semibold">Clique aqui</a> para visualizar.
                        </div>
                    </div>
                </div>
            @endif

            <x-regulacao-form :$chamado :$unidade redirect_to="regulacao.index"/>
        </div>

    </div>
@endsection