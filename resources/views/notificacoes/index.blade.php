@extends('layouts.app')

@section('content')
    <div class="card">
        <div class="card-body">
            <div class="d-flex justify-content-start bg-info p-2 gap-3 rounded-top">
                <div class=" d-flex align-items-center justify-content-center bg-white rounded-circle col-1" style="max-width: 70px!important;">
                    <i class="fa-solid fa-bell text-dark display-6"></i>
                </div>
                <h5 class="card-title text-white display-6">Notificações</h5>
            </div>
    
            <form  action=" {{route('notificacoes.index')}}" method="get" class="w-100 d-flex row">
                    
                <div class="row my-4 px-4 gap-3 ">             

                    {{-- Botão para vizualizar todas as notificações --}}
                    <div class="col-12 col-lg-3">
                        <button type="submit" name="notificacao_status" value="1" class="btn btn-primary w-100">Todas as notificações</button>
                    </div>
                   
                </div>
            </form>

            <table class="table caption-top" aria-label="Lista de notificações">
                <thead>
                    <tr>
                        <th scope="col" class="text-left">Data</th>
                        <th scope="col" class="text-left">Mensagem</th>
                        <th scope="col" class="text-left">Chamado</th>
                        <th scope="col" class="text-left">Tipo</th>
                        <th scope="col" class="text-center">Status</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($notificacoes as $notificacao)
                        <tr >
                            <td class="text-left py-3">{{ $notificacao->created_at->format('d/m/y') }}<p class="text-muted">{{ $notificacao->created_at->format('H:i') }}</p></td>
                            <td class="text-left py-3">{{ $notificacao->mensagem }}</td>
                            <td class="text-left py-3"><a href="{{ $notificacao->chamado? route( $notificacao->chamado->servico ? 'chamado.show' : 'regulacao.edit', $notificacao->chamado?->id_chamado) : '#' }}" class="text-decoration-none">{{ $notificacao->chamado?->titulo }}</a></td>
                            <td class="text-left py-3">{{ $notificacao->tipo }}</td>
                            <td class="text-center"><span class= "badge text-bg-{{ $notificacao->visualizada ? 'info' : 'success' }}">{{ $notificacao->visualizada ? 'Visualizada' : 'Novo' }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            <div class="mt-3">
                {{ $notificacoes->links() }}
            </div>
        </div>
    </div>
@endsection