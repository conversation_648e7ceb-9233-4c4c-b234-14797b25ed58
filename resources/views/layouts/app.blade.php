<!DOCTYPE html>
<html lang="pt-br">

<head>
    <base href="./">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title>{{ config('app.name', 'Laravel') }}</title>
    <meta name="theme-color" content="#ffffff">
    @vite('resources/sass/app.scss')
    @vite('resources/css/toastr.min.css')
    @vite('resources/css/bootstrap-duallistbox.min.css')
    

    @yield('styles')

</head>

<body>
    @include('layouts.partials.sidebar')
    

    <div class="wrapper d-flex flex-column min-vh-100 bg-light">
        @include('layouts.partials.header')
        <div class="body flex-grow-1 px-3">
            <div class="container-lg">
                @yield('content')
            </div>
        </div>

        <footer class="footer">

            <small class="bg-light bg-opacity-50">
                © {{ date('Y') }} <a href="https://www.portais.univasf.edu.br/" target="_blank"> Universidade
                    Federal do
                    Vale do São Francisco </a> - STI

            </small>

            @if (env('APP_ENV') != 'production')
                @include('layouts.database')
            @endif

        </footer>

    </div>

    @vite('resources/js/app.js')
    <script src="{{ asset('js/jquery.min.js') }}"></script>
    <script src="{{ asset('js/toastr.min.js') }}"></script>
    <script src="{{ asset('js/coreui.bundle.min.js') }}"></script>
    <script src="{{ asset('js/jquery.bootstrap-duallistbox.min.js') }}"></script>

    {!! Toastr::message() !!}

    @stack('scripts')
</body>

</html>