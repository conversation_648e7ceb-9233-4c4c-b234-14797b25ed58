<ul class="navbar-nav">

        <li class="nav-item text-center p-3 d-grid">
            <a href="{{ route('chamado.selecionarUnidade')}}" class="btn btn-block btn-warning ">
                <i aria-hidden="true" class="fa fa-file-pen btn-icon me-2"></i>
                Registrar
            </a>
        </li>
        
        <li class="nav-item">
            <a class="nav-link" href="{{ route('home.index') }}">
                <i aria-hidden="true" class="nav-icon fa-solid fa-house"></i>
                Início
            </a>
        </li>

        @php
            $setoresRepositorios = app(\App\Repositories\SetoresRepositorios::class);
            $unidadeHabilitada = $setoresRepositorios->verificarUnidadeOrganizacionalHabilitada(auth()->user()->setor?->id_setor);

            $permissoes = collect(auth()->user()->PermissoesUsuarioLogado('descritas'))
                            ->filter(function($permissao) use ($unidadeHabilitada) {
                $menusRestritos = ['Setores', 'Regulação'];
                return in_array($permissao->descricao_menu, $menusRestritos)
                    ? $unidadeHabilitada
                    : true;
            });
        @endphp

        @foreach ($permissoes as $permissao)
            <li class="nav-item">
                <a class="nav-link" 
                    href="{{$permissao->rota}}"> 
                    <i aria-hidden="true" class="nav-icon fa-solid {{$permissao->classe_menu}}"></i>
                    {{ $permissao->descricao_menu }}
                </a>
            </li>
        @endforeach

        <li class="nav-item">
            <a class="nav-link" href="{{ route('relatorios.index')  }}">
                <i aria-hidden="true" class="nav-icon fa-solid fa-chart-line"></i> {{-- Ícone de gráfico para relatórios --}}
                Relatórios
            </a>
        </li>

        <!--    
        @can('historico-listar')
            <li class="nav-item">
                <a class="nav-link" href="#">
                    <i aria-hidden="true" class="nav-icon fa-solid fa-list"></i>
                    Histórico
                </a>
            </li>
        @endcan
    
        @can('servicos-listar')
            <li class="nav-item">
                <a class="nav-link" href=" {{route('servicos.index') }} ">
                    <i aria-hidden="true" class="nav-icon fa-solid fa-laptop"></i>
                    Serviços
                </a>
            </li>
        @endcan    
            
        @can('perfis-listar')
            <li class="nav-item">
                <a class="nav-link" href="{{ route('perfil.index') }}">
                    <i aria-hidden="true" class="nav-icon fa-solid fa-id-badge"></i>
                    Perfil
                </a>
            </li>
        @endcan

    
        @can('usuarios-listar')
            <li class="nav-item">
                <a class="nav-link" href="{{ route('usuarios.index') }}">
                    <i aria-hidden="true"class="nav-icon fa-solid fa-users"></i>
                    Usuários
                </a>
            </li>
        @endcan

            
        @can('setores-listar')
            <li class="nav-item">
                <a class="nav-link" href="{{ route('setores.index', 'setor') }}">
                    <i aria-hidden="true" class="nav-icon fa-solid fa-university"></i>
                    Setores
                </a>
            </li>
        @endcan
    
            
        @can('unidades-organizacionais-listar')
            <li class="nav-item">
                <a class="nav-link" href="{{ route('setores.index', 'externo') }}">
                    <i aria-hidden="true" class="nav-icon fa-solid fa-university"></i>
                    Setores Externos
                </a>
            </li>
        @endcan
        -->
</ul>
