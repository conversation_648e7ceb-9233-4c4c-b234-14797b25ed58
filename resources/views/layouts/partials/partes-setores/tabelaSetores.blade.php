<div class="table-responsive">
    <table class="table caption-top" aria-label="Lista de setores e suas informações">
        <thead>
            <tr>
                <th scope="col" class="text-left col-1">Sigla</th>
                <th scope="col" class="text-left col-4">
                    <a href="{{route($routeIndex,
                    ['tipoSetor' => $tipoSetor, 
                    'sortOrder'=> request('sortOrder', 'asc') =='asc' ? 'desc' : 'asc'] 
                    + request()->query())}}">
                        Nome
                        <i aria-hidden="true" class="fas fa-sort-{{request('sortOrder', 'asc') == 'asc' ? 'up' : 'down'}}"></i>
                    </a>
                </th>
                <th scope="col" class="text-left col-1">Campus</th>
                <th scope="col" class="text-left col-4">Setor superior</th>
                <th scope="col" class="col-1">Habilitado</th>
                <th scope="col" class="col-1"></th>
            </tr>
        </thead>
        <tbody>
            @php
            if($tipoSetor == 'setor' && Gate::denies('setores-editar') || $tipoSetor == 'unidadeOrganizacional' && Gate::denies('unidades-organizacionais-editar'))
            {
                $btn_editar = false;
            }else{
                $btn_editar = true;
            }
            @endphp
            @foreach ($setores as $setor)
            <tr>
                <td class="text-left">{{ $setor->sg_setor }}</td>
                <td class="text-left">{{ $setor->ds_nomesetor }}</td>
                <td class="text-left">{{ $setor->campus->ds_nomecampus }}</td>
                <td class="text-left">{{ $setor->setorSuperior->ds_nomesetor ?? '' }}</td>
                <td class="text-center">
                    
                    @if ($setor->$relation?->habilitado == true)
                        <span class="badge text-bg-success">Sim</span>
                    @else
                        <span class="badge text-bg-danger">Não</span>
                    @endif
                </td>
                <td>
                    
                    <div class="d-flex justify-content-end">
                        @if ($btn_editar)
                            <a class="btn btn-warning btn-sm me-2" title="Editar um setor"
                            href="{{ route('setor.edit', [$tipoSetor, $setor->id_setor]) }}">
                            <i aria-hidden="true" class="fas fa-pencil-alt"></i>
                            </a>
                        @endif
                    </div>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
    {{ $setores->links() }}
</div>