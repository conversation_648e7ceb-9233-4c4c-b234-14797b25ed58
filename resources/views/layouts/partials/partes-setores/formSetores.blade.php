<form method="GET" action="{{route($routeIndex, $tipoSetor)}}">
    <div class= "row">
        <div class="col-lg-2 col-md-4 mb-3">
            <input type="text" name="sigla" class="form-control" placeholder="Buscar por sigla" value="{{ session('filtros_form_'.$tipoSetor.'.sigla')}}">
        </div>
      
        <div class="col-lg-4 col-md-8 mb-3">
            <input type="text" name="setor" class="form-control" placeholder="Buscar por nome" value="{{ session('filtros_form_'.$tipoSetor.'.setor')}}">
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <select name="campusId" class="form-select" onchange="this.form.submit()">
                <option value="">Todos os campi</option>
                @foreach ($campi as $campus)
                    <option value="{{ $campus->id_campus}}" {{session('filtros_form_'.$tipoSetor.'.campusId') == $campus->id_campus ? 'selected' : '' }}>{{ $campus->ds_nomecampus }}</option>
                @endforeach
            </select>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <input type="text" name="setorSuperior" class="form-control" placeholder="Buscar por setor superior" value="{{ session('filtros_form_'.$tipoSetor.'.setorSuperior') }}">
        </div>
        @if ($tipoSetor == 'setor')
            <div class="col-lg-4 mb-2">
                <select name="unidadeId" class="form-select" id="unidadeId" onchange="this.form.submit()">
                    <option value="" disabled>Selecione uma unidade</option>
                    @foreach ($unidadesHabilitadas as $unidade)
                        <option value="{{$unidade->unidade_id}}" @selected(session("filtros_form_{$tipoSetor}.unidadeId") == $unidade->unidade_id)>{{$unidade->setor->ds_nomesetor}}</option>
                    @endforeach
                </select>
            </div>  
        @endif
       
        <div class="row">
            <div class="col-lg-6 col-md-12 mb-2">
                <div class="form-check">
                    <input type="checkbox" name="habilitado" id="habilitado" {{ session('filtros_form_'.$tipoSetor.'.habilitado') ? 'checked' : '' }} class="form-check-input">
                    <label class="form-check-label" for="habilitado">Somente {{ $tipoSetor =='setor'? 'setores habilitados' : 'unidades organizacionais habilitadas'}}  para atendimento</label>
                </div>
            </div>
        </div>
        
        <div class="row">

            <div class="col-lg-2 col-md-6 mb-2">
                <button type="submit" class="btn btn-primary">Buscar</button>
                <button type="submit" name= "limparFiltros" value="limpar"  class="btn btn-secondary">Limpar</button>
            </div>
        </div>

    </div>
</form>