
<form action="{{ route($routeUpdate, [$tipoSetor, $setor->id_setor]) }}" method="POST" enctype="multipart/form-data">
    @csrf
    @method('PUT')
    {{-- informações do setor --}}
    <fieldset disabled>
        <legend class="visually-hidden">Campos sem permissão de edição</legend>

        <div class="row mb-3">
            <div class="form-group col-lg-6 col-md-12 ">
                <label for="ds_nomesetor" class="fw-semibold">Nome do Setor</label>
                <input type="text" id="ds_nomesetor" class="form-control bg-light"
                value="{{ $setor->ds_nomesetor }}">
            </div>
            <div class="form-group col-lg-3 col-md-6">
                <label for="sg_setor" class="fw-semibold">Sigla do Setor</label>
                <input type="text" id="sg_setor" class="form-control bg-light" value="{{ $setor->sg_setor }}">
            </div>
            <div class="form-group col-lg-3 col-md-6">
                <label for="ds_nomecampus" class="fw-semibold">Campus</label>
                <input type="text" id="ds_nomecampus" class="form-control bg-light"
                value="{{ $setor->campus->ds_nomecampus }}">
            </div>
        </div>
        {{-- informações do setor superior   --}}
        <div class="row mb-3">
            <div class="form-group col-lg-6 col-md-12 ">
                <label for="setorSuperior" class="fw-semibold">Setor superior</label>
                <input type="text" id="setorSuperior" class="form-control bg-light"
                    value="{{ $setor->setorSuperior->ds_nomesetor ?? '' }}">

            </div>
            <div class="form-group col-lg-3 col-md-6">
                <label for="sg_setor" class="fw-semibold">Sigla do setor superior</label>
                <input type="text" id="sg_setor" class="form-control bg-light"
                    value="{{ $setor->setorSuperior->sg_setor ?? '' }}">

            </div>
            <div class="form-group col-lg-3 col-md-6">
                <label for="ds_nomecampus" class="fw-semibold">Campus do setor superior</label>
                <input type="text" id="ds_nomecampus" class="form-control bg-light"
                    value="{{ $setor->setorSuperior->campus->ds_nomecampus ?? '' }}">
                    
            </div>
        </div>
        {{-- descrição hierarquica do setor --}}
        <div class="row mb-3">
            <div class="form-group col-12">
                <label for="ds_descricao" class="fw-semibold">Descrição Hierarquica</label>
                <textarea name="ds_descricao" id="ds_descricao" cols="30" rows="5" class="form-control bg-light">{{ $setor->DescricaoHierarquica }}</textarea>
            </div>
        </div>
    </fieldset>
    {{-- habilitação de setor/unidade --}}
    <div class="form-group col-lg-4 col-md-6 mb-3">
        <label for="habilitado" class="fw-semibold">Habilitar {{ucfirst($tipoSetor)}} para Atendimento </label>
        <div class="form-check border py-3 rounded shadow-sm bg-light">
            <input type="checkbox" name="habilitado" id="habilitado" {{old( 'habilitado',$habilitado) ? 'checked' : '' }}>
            <label for="habilitado" class="ml-2">Habilitado</label>
        </div>
    </div>

    @if ($tipoSetor == 'unidadeOrganizacional')
    <div class="form-group">
        <label for="imagem" class="fw-semibold">Selecione a Logo:</label>
        <div class="d-flex align-items-center">

            <img id="imagemPreview" class="img-fluid rounded border p-1 me-2" alt="Preview" style="object-fit: contain; max-width: 200px; max-height: 110px; display: none;" data-default-image="{{ asset('storage/'.($setor->habilitarUnidadeOrganizacional->caminho_logomarca ?? 'images/logoSetores/logo_default.png'))}}">
           
            <div class="col-lg-4 col-md-8">
                <input class="form-control @error('imagem') is-invalid @enderror" type="file" name="imagem" id="imagem">
                @error('imagem')
                    <div class="invalid-feedback">
                        {{ $errors->first('imagem') }} <br>Tamanho max: 1mb. <br>Formatos aceitos: .jpg, .jpeg, .png
                    </div>
                @enderror
            </div>
        </div>
    </div>

    <div class="form-group mb-3">
        <label for="descricao" class="col-lg-3 col-form-label fw-semibold">Descrição do Setor:<span class="text-danger">*</span></label>
        <div class="col-lg-6">
            <textarea name="descricao" id="descricao" class="form-control @error('descricao') is-invalid
            @enderror" rows="4" required>{{old('descricao', $setor->habilitarUnidadeOrganizacional?->descricao) ?? ''}}</textarea>
            @error('descricao')
                <div class="invalid-feedback">
                    {{ $errors->first('descricao') }}
                </div>
            @enderror
        </div>
    </div>
    @endif

    <div class="mx-auto">
        <button type="submit" class="btn btn-primary">Salvar Alterações</button>
        <a href="{{ url()->previous() }}" class="btn btn-secondary">Cancelar</a>
    </div>

</form>