<header class="header header-sticky mb-4">
    <div class="container-fluid">
        <button class="header-toggler px-md-0 me-md-3" type="button"
            onclick="coreui.Sidebar.getInstance(document.querySelector('#sidebar')).toggle()">
            <i aria-hidden="true" class="fa fa-bars"></i>
        </button>
        <a class="header-brand d-none" href="#">

        </a>
        <ul class="header-nav d-none d-md-flex">
            <li class="nav-item">
                <div class="fs-5 fw-bold">{{ auth()->user()->pessoa?->lotacao?->setor->ds_nomesetor }}</div>
                <div class="fs-6 fw-bold text-muted">{{ env('APP_NAME', 'Central de Atendimento') }}</div>
            </li>
        </ul>

        <ul class="header-nav ms-auto">

        </ul>
        <ul class="header-nav ms-3">
            <li class="nav-item me-4">
                <a class="nav-link" href="{{ route('notificacoes.index') }}">
                    <span class="badge bg-danger {{ $notificacoes == 0 ? 'd-none' : '' }}" id='notificacaoCount' > {{ $notificacoes }}</span>
                    <i aria-hidden="true"  class="fa fa-bell"></i>
                </a>
            </li>

            <li class="nav-item dropdown">
                <a class="nav-link show bg-light rounded" data-coreui-toggle="dropdown" href="#" role="button"
                    aria-haspopup="true" aria-expanded="true">
                    <i aria-hidden="true" class="fas fa-user"></i>
                    <span class="m-2 fw-semibold">{{ Auth::user()?->pessoa?->nome }}</span>
                    <i aria-hidden="true"  class="fas fa-chevron-down"></i>
                </a>

                <div class="dropdown-menu dropdown-menu-end pt-0">
                    <a href="{{ route('usuario.perfil') }}" class="dropdown-item">
                        <i aria-hidden="true" class="fa-solid fa-user me-2"></i>
                        Perfil
                    </a>
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <a href="#" class="dropdown-item text-danger"
                            onclick="event.preventDefault(); this.closest('form').submit();">
                            <i aria-hidden="true" class="fa-solid fa-power-off me-2"></i>
                            <span>Sair</span>
                        </a>
                    </form>
                </div>

            </li>
        </ul>
    </div>
</header>

<script>
    
//Adiciona +1 ao contador de notificações ao receber o evento via websocket
document.addEventListener('DOMContentLoaded', function () {

    // Armazena o token CSRF em uma variável global
    window.csrfToken = "{{ csrf_token() }}";

    window.Echo.private(`notificacao-chamado.{{ $usuarioId }}`)
        .listen('.notificacao-chamado', (e) => {
            
            // Verifica se a URL atual é a view de chat (/chamado/{id})
            const path = window.location.pathname;
            // const regex = /^\/chamado\/\d+$/; // corresponde a /chamado/123
            const match = path.match(/^\/chamado\/(\d+)$/); // captura o id da URL

            // Verifica se o usuário está dentro do chat. Se estiver, todas as notificações criadas para ele serão marcadas como lidas
            if (match && parseInt(match[1]) === e.id_chamado) {
                fetch(`/notificacoes/marcar-como-lida/${e.notificacao_id}`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': window.csrfToken,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });
                return;
            }
            // Atualiza contador da barra de navegação
            let notificacaoCount = document.getElementById('notificacaoCount');
            let count = parseInt(notificacaoCount.innerText) || 0;
            count++;
            notificacaoCount.innerText = count;
            
            if (count > 0) {
                notificacaoCount.classList.remove('d-none');
            }

    });
});

</script>
