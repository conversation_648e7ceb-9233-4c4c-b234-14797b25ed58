@extends('layouts.app')
@section('content')

    <div class="card">
        <div class="card-body">
            <div class="d-flex justify-content-between">
                <h5 class="card-title"> Lista de Serviços</h5>
                <div>
                    <a href="#" id="btnRelatorioServicos" class="btn btn-info btn-md me-2" data-coreui-toggle="modal" data-coreui-target="#relatorioServicosModal">
                        <em class="fa-solid fa-chart-line"></em> Relatório de Serviços
                    </a>
                    @can('servicos-criar')
                        <a href="{{route('servicos.create')}}" class="btn btn-primary">Novo</a>
                    @endcan
                </div>
            </div>

            <hr>

            <form id="formularioApresentar" action=" {{route('servicos.index')}}" method="get">
                <div class="row mb-3">
                    <div class="col-12 col-lg-8">
                        <select name="unidade_responsavel" id="unidade_responsavel" class="form-select" onchange="this.form.submit()">
                            <option value="" disabled>Selecione uma unidade</option>
                            @if (auth()->user()->perfis()->where('nome', 'Administrador')->exists())
                                <option value="">Todas as unidades</option>
                            @endif
                            @foreach ($unidadesHabilitadas as $unidadeHabilitada)
                                <option value="{{$unidadeHabilitada->unidade_id}}" @selected(session("filtros_form_servicos.unidade_responsavel") == $unidadeHabilitada->unidade_id)>{{$unidadeHabilitada->setor->ds_nomesetor}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="row gy-3 mb-3">
                    <div class="col-12 col-lg-6" id="divSetorResponsavel">
                        <select name="setor_responsavel" id="setor_responsavel" class="form-select" data-selected-setor-id="{{session('filtros_form_servicos.setor_responsavel')}}" onchange="this.form.submit()">
                            <option value="" disabled>Selecione um setor</option>
                        </select>
                    </div>
                    <div class="col-12 col-lg-6" id="divGrupoResponsavel">
                        <select name="grupo_responsavel" id="grupo_responsavel" class="form-select" data-selected-grupo-id="{{session('filtros_form_servicos.grupo_responsavel')}}" onchange="this.form.submit()">
                            <option value="" disabled>Selecione um grupo</option>
                        </select>
                    </div>
                </div>
                <div class="d-flex flex-wrap gap-3 mb-3">
                    <div class="d-flex flex-grow-1" style="max-width: 300px;">
                        <input type="text" name="servico_nome" id="servico_nome" class="form-control" placeholder="Buscar por nome" value="{{ session('filtros_form_servicos.servico_nome')}}">
                    </div>
                    <div class="d-flex flex-grow-1" style="max-width: 250px;">
                        <select name="servico_status" id="servico_status" class="form-select" onchange="this.form.submit()">
                            <option value="">Selecione um status</option>
                            <option value="0" @selected(session("filtros_form_servicos.servico_status") === "0")>Desabilitado</option>
                            <option value="1" @selected(session("filtros_form_servicos.servico_status") === "1")>Habilitado</option>
                        </select>
                    </div>
                    <div class="d-flex flex-grow-1" style="max-width: 250px;">
                        <select name="servico_criticidade" id="servico_criticidade" class="form-select" onchange="this.form.submit()">
                            <option value="">Selecione uma criticidade</option>
                            @foreach ($criticidadeList as $criticidade)
                                <option value="{{$criticidade->value}}" @selected(session("filtros_form_servicos.servico_criticidade") == $criticidade->value)>{{$criticidade->getLabel()}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div>
                    <button type="submit" class="btn btn-primary">Buscar</button>
                    <button type="submit" name= "limparFiltros" value="limpar"  class="btn btn-secondary">Limpar</button>
                </div>
            </form>
        
            <hr>

            <div class="table-responsive">
                <table class="table caption-top" aria-label="Layout de organização de elementos">
                    <thead>
                        <tr>
                            <th id="servico_nome" class="text-left col-3">Nome</th>
                            <th id="servico_descricao" class="text-left col-3">Descrição</th>
                            <th id="unidade_responsavel" class="text-left col-3">Unidade responsável</th>
                            <th id="setor_responsavel" class="text-left col-3">Setor/Grupo responsável</th>
                            <th id="servico_criticidade" class="text-left col-1">Criticidade</th>
                            <th id="servico_status" class="text-center col-1">Status</th>
                            <th  scope ="col" class ="text-center col-1">Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                    
                    
                            
                
                        @foreach ($servicos as $servico)
                            <tr>
                                <td class="text-left"> {{$servico->nome}} </td>
                                <td class="text-left"> {{$servico->descricao}} </td>
                                <td class="text-left"> {{$servico->unidade?->ds_nomesetor}} </td>
                                <td class="text-left"> {{$servico->setor?->ds_nomesetor ?? $servico->grupo?->nome}} </td>
                                <td class="text-center"> {{$servico->criticidade->getLabel()}}</td>
                                <td class="text-center"> 
                                    <span class="badge text-bg-{{$servico->status == '1' ? 'success' : 'danger'}}">{{$servico->status == '1' ? 'Habilitado' : 'Desabilitado'}}</span>
                                </td>
                                <td>
                                    <div class="d-flex justify-content-end">

                                        <a class="btn btn-primary btn-sm me-2" title="Visualizar um Serviço"
                                        href= {{route('servicos.show',$servico->id)}}>
                                        <em class="fas fa-eye"></em>
                                        </a>

                                        <a class="btn btn-warning btn-sm me-2" title="Editar um Serviço"
                                        href= {{ route('servicos.edit',$servico->id)}}>
                                        <em class="fas fa-pencil-alt"></em>
                                        </a>

                                    </div>
                                </td>
                            </tr>
                        @endforeach
                        
                    </tbody>
                </table>
            </div>
            {{ $servicos->links() }}
        </div>
    </div>

    <!-- Modal de Relatório de Serviços -->
    <div class="modal fade" id="relatorioServicosModal" tabindex="-1" aria-labelledby="relatorioServicosModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="relatorioServicosModalLabel">Opções de Relatório de Serviços</h5>
                    <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <form action="{{ route('relatorio.servicos') }}" method="GET">
                    @csrf
                    <div class="modal-body">
                        @if(session('filtros_form_servicos.servico_status') == 'todos')
                        <div class="mb-3" id="opcaoStatus">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" id="ordenarPorStatus" name="ordenacao" value="status" onchange="verificarSelecaoOrdenacao()">
                                <label class="form-check-label" for="ordenarPorStatus">
                                    Ordenar por status
                                </label>
                            </div>
                        </div>
                        @endif

                        <div class="mb-3" id="opcaoCriticidade">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" id="ordenarPorCriticidade" name="ordenacao" value="criticidade" onchange="verificarSelecaoOrdenacao()">
                                <label class="form-check-label" for="ordenarPorCriticidade">
                                    Ordenar por criticidade
                                </label>
                            </div>
                        </div>
                        
                        <div id="erroOrdenacao" class="text-danger" style="display: block;">
                            Selecione uma opção de ordenação para gerar o relatório.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Cancelar</button>
                        <button type="submit" id="btnGerarRelatorio" class="btn btn-primary" disabled>Gerar Relatório</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @vite('resources/js/servicos-index.js')
    @vite('resources/js/opcoesDeRelatorioServicos.js')
@endsection
