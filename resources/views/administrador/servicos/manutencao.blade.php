@extends('layouts.app')
@section('content')
    <div class="card">
        <form method="POST" action=" {{ $rota }}">
            @csrf
           
            @if ($atividade == 'Editar')
                @method('PUT')
            @endif

            @php
                $defaultCategorias = (isset($servico) && $servico->exists) 
                    ? ($servico->categorias_permitidas->pluck('value')->all() ?? []) 
                    : collect($categorias)->pluck('value')->all();

                $disabled = $atividade == 'Visualizar' ? 'disabled' : '';
                $btnText = $atividade == 'Visualizar' ? 'Voltar' : 'Cancelar';
                $asterisco = $atividade == 'Visualizar' ? '' : '<span class="text-danger">*</span>';
            @endphp
            
            <div class="card-body">
                <h5 class="card-title">{{ $atividade }} Serviço</h5>
                <div class="card p-3 mt-3 gap-3">
                    <div>
                        <label for="nome" class="fw-semibold">Nome do serviço:{!!$asterisco!!}</label>
                        <input type="text" required name="nome" class="form-control @error('nome') is-invalid @enderror" id="nome" value="{{ old('nome', $servico->nome ?? '') }}" {{$disabled}}>
                             
                        @error('nome')
                            <div class="invalid-feedback">
                               {{$message}}
                            </div>
                        @enderror
                        
                    </div>

                    <div>
                        <label for="descricao" class="fw-semibold">Atividade realizada:{!!$asterisco!!}</label>
                        <textarea class="form-control @error('descricao') is-invalid @enderror" style="resize: none" name="descricao"
                            id="descricao" rows="3" {{$disabled}} required>{{ old('descricao', $servico->descricao ?? '') }}</textarea>
                         
                        @error('descricao')
                            <div class="invalid-feedback">
                                {{$message}}
                            </div>
                        @enderror
                    </div>
                    <div class="row">
                        <div class="col-12 col-lg-8">
                            <label for="unidade_responsavel" class="fw-semibold">Unidade responsável:{!!$asterisco!!}</label>
                            <select name="unidade_responsavel" id="unidade_responsavel" class="form-select @error('unidade_responsavel') is-invalid @enderror" {{$disabled}} required>
                                @foreach ($unidadesHabilitadas as $unidadeHabilitada)
                                    <option value="{{$unidadeHabilitada->unidade_id}}" @selected(old('unidade_responsavel', $servico->unidade_responsavel ?? auth()->user()->unidade_id) == $unidadeHabilitada->unidade_id)>{{$unidadeHabilitada->setor->ds_nomesetor}} </option>
                                @endforeach
                            </select>
                            @error('unidade_responsavel')
                                <div class="invalid-feedback">
                                    {{$message}}
                                </div>
                            @enderror
                        </div>
                    </div>
                    <div class="row gy-3">
                        <div class="col-12 col-lg-6" id="divSetorResponsavel">
                            <label for="setor_responsavel_select" class="fw-semibold">
                                Setor responsável:
                                <em class="fas fa-info-circle" tabindex="0" data-coreui-toggle="tooltip" data-coreui-placement="top" data-coreui-container="body" title="O setor responsável é opcional, porém só pode ser selecionado um setor ou um grupo por vez."></em>
                            </label>
                            <div class="d-flex align-items-center gap-2">
                                <select id="setor_responsavel_select" class="form-select @error('setor_responsavel') is-invalid @enderror" data-selected-setor-id="{{old('setor_responsavel', $servico->setor_responsavel ?? '')}}" disabled>
                                    <option value="">{{$servico->setor->ds_nomesetor ?? ''}}</option>
                                </select>
                                <input type="hidden" name="setor_responsavel" id="setor_responsavel_hidden" value="{{old('setor_responsavel', $servico->setor_responsavel ?? '')}}">
                                @if ($atividade != 'Visualizar')
                                    <em class="fas fa-solid fa-trash text-danger" id="btnExcluirSetorResponsavel" title="Limpar setor responsável" style="cursor: pointer;"></em>
                                @endif
                            </div>
                            @error('setor_responsavel')
                                <div class="invalid-feedback d-block">
                                    {{$message}}
                                </div>
                            @enderror
                        </div>
                        <div class="col-12 col-lg-6" id="divGrupoResponsavel">
                            <label for="grupo_responsavel_select" class="fw-semibold">
                                Grupo responsável:
                                <em class="fas fa-info-circle" tabindex="0" data-coreui-toggle="tooltip" data-coreui-placement="top" data-coreui-container="body" title="O grupo responsável é opcional, porém só pode ser selecionado um setor ou um grupo por vez."></em>
                            </label>
                            <div class="d-flex align-items-center gap-2">
                                <select id="grupo_responsavel_select" class="form-select @error('grupo_responsavel') is-invalid @enderror" data-selected-grupo-id="{{old('grupo_responsavel', $servico->grupo_responsavel ?? '')}}" disabled>
                                    <option value="">{{$servico->grupo->nome ?? ''}}</option>
                                </select>
                                <input type="hidden" name="grupo_responsavel" id="grupo_responsavel_hidden" value="{{old('grupo_responsavel', $servico->grupo_responsavel ?? '')}}">
                                @if ($atividade != 'Visualizar')
                                    <em class="fas fa-solid fa-trash text-danger" id="btnExcluirGrupoResponsavel" title="Limpar grupo responsável" style="cursor: pointer;"></em>
                                @endif
                            </div>
                            @error('grupo_responsavel')
                                <div class="invalid-feedback d-block">
                                    {{$message}}
                                </div>
                            @enderror
                        </div>
                    </div>
                    <div class="d-flex flex-wrap gap-3">
                        <div class="flex-grow-1" style="max-width: 200px;">
                            <label for="criticidade" class="fw-semibold">Criticidade:{!!$asterisco!!}</label>
                            <select name="criticidade" class="form-select @error('criticidade') is-invalid @enderror" {{$disabled}} required>
                                <option value="1" @selected(old('criticidade', $servico->criticidade ?? 1) == 1) >Baixa</option>
                                <option value="2" @selected(old('criticidade', $servico->criticidade ?? 1) == 2) >Normal</option>
                                <option value="3" @selected(old('criticidade', $servico->criticidade ?? 1) == 3) >Importante</option>
                                <option value="4" @selected(old('criticidade', $servico->criticidade ?? 1) == 4) >Crítico</option>
                            </select>
                            @error('criticidade')
                                <div class="invalid-feedback d-flex justify-content-start">
                                    {{$message}}
                                </div>
                            @enderror
                        </div>
                        <div class="flex-grow-1" style="max-width: 200px;">
                            <label for="status" class="fw-semibold">Estado do serviço:{!!$asterisco!!}</label>
                            <select name="status" class="form-select @error('status') is-invalid @enderror" {{$disabled}} required>
                                <option value="0" @selected(old('status', $servico?->status ?? 1) == 0)>Desabilitado</option>
                                <option value="1" @selected(old('status', $servico?->status ?? 1) == 1)>Habilitado</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback d-flex justify-content-start">
                                    {{$message}}
                                </div>
                            @enderror
                        </div>
                    </div>

                    <div>
                        <label for="categorias_permitidas" class="fw-semibold @error('categorias_permitidas.*') text-danger @enderror">Categorias permitidas:{!!$asterisco!!}</label>
                        <div class="border rounded shadow-sm bg-light p-3">
                            <div class="row gy-2 ms-2">
                                @foreach ($categorias as $categoria)
                                    <div class="form-check col-12 col-lg-3 col-md-6">
                                        <input type="checkbox" 
                                            class="form-check-input"
                                            id="categoria-{{ $categoria->value}}"
                                            name="categorias_permitidas[]"
                                            value="{{ $categoria->value}}"
                                            @checked(in_array($categoria->value, old('categorias_permitidas', $defaultCategorias)))
                                            {{$disabled}}
                                        >
                                        <label for="categoria-{{ $categoria->value}}" class="form-check-label">
                                            {{ $categoria->getLabel()}}
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                        @error('categorias_permitidas.*')
                            <div class="invalid-feedback d-flex justify-content-start">
                                {{$message}}
                            </div>
                        @enderror

                    </div>
                    <hr class="mb-0">
                    <div class="mx-auto">
                        <a href="{{ url()->previous() }}" class="btn btn-secondary">{{ $btnText }}</a>
                         
                        @if ($atividade == 'Editar')

                            <button type="submit" name='opcao' value="delete" class="btn btn-danger" form = "formularioDelete">
                                Excluir
                            </button>
                            
                        @endif
                         
                        @if ($atividade != 'Visualizar')
                            <button type="submit" name='opcao' value="save" class="btn btn-primary">Salvar</button>
                        @endif 
                
                    </div>

                </div>
            </div>
        </form>

        @if ($atividade == 'Editar')
            <form id="formularioDelete" action=" {{route('servicos.destroy',$servico->id)}}" method="post">
                @csrf
                @method('DELETE')
            </form>
        @endif
    
    </div>
@endsection
@if ($atividade != 'Visualizar')
    @push('scripts')
        @vite('resources/js/servicos-manutencao.js')
    @endpush
@endif
