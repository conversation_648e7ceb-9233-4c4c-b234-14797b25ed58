@extends('layouts.app')

@section('content')
    <div class="card">
        <div class="card-header d-flex align-items-center justify-content-center">
            <h3 class="card-title">{{$acao}} Grupo de Setores</h3>
        </div>
        <div class="card-body d-flex flex-column" style="min-height: 75vh">
            <form action="{{$rota}}" method="POST" id="grupo-setores-form">
                @csrf
                @method($acao == 'Editar' ? 'PUT' : '')
                <div class="d-flex flex-wrap gap-3">
                    <div style="flex:1; min-width:250px; max-width:500px;">
                        <label for="nome" class="fw-semibold">Nome do grupo:</label>
                        <input type="text" name="nome" id="nome" 
                            class="form-control @error('nome') is-invalid @enderror" 
                            value="{{ old('nome', $grupoSetor->nome ?? '') }}" required>
                        @error('nome')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div style="flex:2; min-width:300px;">
                        <label for="unidade_responsavel_id" class="fw-semibold">Unidade responsável:</label>
                        <select name="unidade_responsavel_id" id="unidade_responsavel_id" 
                                class="form-select @error('unidade_responsavel_id') is-invalid @enderror" required>
                            <option value="">Selecione uma unidade</option>
                            @foreach ($unidadesHabilitadas as $unidade)
                                <option value="{{$unidade->unidade_id}}" 
                                        @selected(old('unidade_responsavel_id', $grupoSetor->unidade_responsavel_id ?? auth()->user()->unidade_id) == $unidade->unidade_id)>
                                    {{$unidade->setor->ds_nomesetor}}
                                </option>
                            @endforeach
                        </select>
                        @error('unidade_responsavel_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div style="flex:0 0 auto; min-width:180px;">
                        <label class="fw-semibold d-block">Status do grupo:</label>
                        <div class="form-control d-flex align-items-center gap-2">
                            <div class="form-check form-switch m-0">
                                <input type="hidden" name="habilitado" value="0">
                                <input type="checkbox" class="form-check-input" id="habilitado" name="habilitado"
                                    value="1" {{ old('habilitado', $grupoSetor->habilitado ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="habilitado">Habilitado</label>
                            </div>
                        </div>
                        @error('habilitado')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="table-responsive" style="overflow-x: auto;">
                    <table class="table" id="tabela-setores-membros">
                        <thead>
                            <tr>
                                <th scope="col" class="text-left" style="min-width: 400px;">Setor</th>
                                <th scope="col" class="text-left" style="min-width: 120px; width: 120px;">Peso</th>
                                <th scope="col" class="text-left" style="min-width: 100px; width: 100px;">Ações</th>
                            </tr>
                        </thead>
                        <tbody id="tabela-setores-membros-body">
                            @forelse (old('setores', $setores ?? []) as $i => $setor)
                            <tr class="setor-row">
                                    <td>
                                        <select name="setores[{{$i}}][setor_id]" id="setores[{{$i}}][setor_id]" class="form-select setor-select @error("setores.$i.setor_id") is invalid @enderror" data-selected="{{$setor['setor_id'] ?? ''}}" required>
                                            <option value="">Selecione um setor</option>
                                        </select>
                                        @error("setores.$i.setor_id")
                                            <div class="invalid-feedback">
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </td>
                                    <td>
                                        <input type="number" name="setores[{{$i}}][peso]" id="setores[{{$i}}][peso]" class="form-control peso-input @error("setores.$i.peso") is-invalid @enderror" value="{{$setor['peso'] ?? 1}}" min="1" required>
                                        @error("setores.$i.peso")
                                            <div class="invalid-feedback">
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-outline-danger remover-setor">Remover</button>
                                    </td>
                                </tr>
                            @empty
                            @endforelse
                        </tbody>
                    </table>
                </div>
                @error('setores')
                    <div class="invalid-feedback d-block mb-3">
                        {{ $message }}
                    </div>
                @enderror
                <button type="button" class="btn btn-success" id="adicionar-setor">Adicionar setor</button>
            </form>
            <div class="mt-auto">
                <hr>
                <div class="d-flex gap-2 align-items-center justify-content-center mt-3">
                    <a href="{{ url()->previous()}}" class="btn btn-secondary">Voltar</a>
                    <button type="submit" form="grupo-setores-form" class="btn btn-primary">Salvar</button>
                    @if ($acao == 'Editar')
                        <form action="{{ route('grupoSetores.destroy', $grupoSetor->id) }}" method="POST" id="grupo-setores-form-delete">
                            @csrf
                            @method('DELETE')
                            <button type="submit" form="grupo-setores-form-delete" class="btn btn-danger">Excluir</button>
                        </form>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    @vite('resources/js/grupoSetores.js')
@endpush