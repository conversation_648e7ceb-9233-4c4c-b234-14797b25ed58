@extends('layouts.app')

@section('content')
    <div class="card">
        <div class="card-body">
            <div class="d-flex justify-content-between">
                <h5 class="card-title">Grupos de Setores</h5>
                @can('grupos-setores-criar')
                    <a href="{{ route('grupoSetores.create') }}" class="btn btn-primary">Novo</a>
                @endcan
            </div>
            <hr>
            <form action="{{ route('grupoSetores.index') }}" method="GET">
                @csrf
                <div class="d-flex flex-wrap gap-3">
                    <div style="flex:1; min-width: 250px; max-width: 300px;">
                        <input type="text" name="nome" id="nome" class="form-control" placeholder="Buscar por nome" value="{{ session('filtros_form_gruposSetores.nome') }}">
                    </div>
                    <div style="flex:1; min-width: 300px; max-width: 600px;">
                        <select name="unidade_responsavel_id" id="unidade_responsavel_id" class="form-select" onchange="this.form.submit()">
                            @if (auth()->user()->perfis()->where('nome', 'Administrador')->exists())
                                <option value="">Todas as unidades</option>
                            @endif
                            @foreach ($unidadesHabilitadas as $unidade)
                                <option value="{{$unidade->unidade_id}}" @selected(session("filtros_form_gruposSetores.unidade_responsavel_id") == $unidade->unidade_id)>{{$unidade->setor->ds_nomesetor}}</option>
                            @endforeach
                        </select>
                    </div>
                    <div style="flex:1; min-width: 250px; max-width: 300px;">
                        <select name="habilitado" id="habilitado" class="form-select" onchange="this.form.submit()">
                            <option value="">Selecione um status</option>
                            <option value="0" @selected(session("filtros_form_gruposSetores.habilitado") == "0")>Desabilitado</option>
                            <option value="1" @selected(session("filtros_form_gruposSetores.habilitado") == "1")>Habilitado</option>
                        </select>
                    </div>
                </div>
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary">Buscar</button>
                    <button type="submit" name="limparFiltros" value="limpar" class="btn btn-secondary">Limpar</button>
                </div>
            </form>
            <hr>
            <div class="table-responsive">
                <table class="table caption-top" aria-label="Lista de grupos de setores">
                    <thead>
                        <tr>
                            <th scope="col" class="text-left col-3">Nome</th>
                            <th scope="col" class="text-left col-3">Unidade responsável</th>
                            <th scope="col" class="text-center col-1">Membros</th>
                            <th scope="col" class="text-center col-1">Habilitado</th>
                            <th scope="col" class="text-center col-1">Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($grupos as $grupo)
                            <tr>
                                <td class="text-left">{{$grupo->nome}}</td>
                                <td class="text-left">{{$grupo->unidade->ds_nomesetor}}</td>
                                <td class="text-center"> <span class="badge text-bg-light"> {{$grupo->membros->count()}}</span></td>
                                <td class="text-center">
                                    @if ($grupo->habilitado == true)
                                        <span class="badge text-bg-success">Sim</span>
                                    @else
                                        <span class="badge text-bg-danger">Não</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="d-flex justify-content-end">
                                        <a class="btn btn-primary btn-sm me-2" title="Visualizar um Grupo"
                                            href={{ route('grupoSetores.show', $grupo->id) }}>
                                            <i aria-hidden="true" class="fas fa-eye"></i>
                                        </a>
                                        <a class="btn btn-warning btn-sm me-2" title="Editar um Grupo"
                                            href={{ route('grupoSetores.edit', $grupo->id) }}>
                                            <i aria-hidden="true" class="fas fa-pencil-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            {{ $grupos->links() }}
        </div>
    </div>
@endsection