@extends('layouts.app')

@section('content')
    <div class="card">
        <div class="card-header d-flex align-items-center justify-content-center">
            <h3 class="card-title">Visualizar Grupo de Setores</h3>
        </div>
        <div class="card-body d-flex flex-column" style="min-height: 75vh">
            <div class="d-flex flex-wrap gap-3">
                <div style="flex:1; min-width:250px; max-width:500px;">
                    <label for="nome" class="fw-semibold">Nome do grupo:</label>
                    <input type="text" id="nome" class="form-control" value="{{ $grupoSetor->nome }}" disabled>
                </div>

                <div style="flex:2; min-width:300px;">
                    <label for="unidade_responsavel" class="fw-semibold">Unidade responsável:</label>
                    <input type="text" id="unidade_responsavel" class="form-control" value="{{ $grupoSetor->unidade->ds_nomesetor }}" disabled>
                </div>

                <div style="flex:0 0 auto; min-width:180px;">
                    <label class="fw-semibold d-block">Status do grupo:</label>
                    <div class="form-control d-flex align-items-center gap-2" style="background-color: #D8DBE0;">
                        <div class="form-check form-switch m-0">
                            <input type="checkbox" class="form-check-input" id="habilitado" {{ $grupoSetor->habilitado ? 'checked' : '' }} disabled>
                            <label class="form-check-label text-black" for="habilitado">Habilitado</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive" style="overflow-x: auto;">
                <table class="table" id="tabela-setores-membros">
                    <thead>
                        <tr>
                            <th scope="col" class="text-left" style="min-width: 400px;">Setor</th>
                            <th scope="col" class="text-left" style="min-width: 120px; width: 120px;">Peso</th>
                        </tr>
                    </thead>
                    <tbody id="tabela-setores-membros-body">
                        @foreach ($grupoSetor->membros as $membro)
                            <tr>
                                <td>
                                    <input type="text" class="form-control" value="{{ $membro->setor->ds_nomesetor }}" disabled>
                                </td>
                                <td>
                                    <input type="number" class="form-control" value="{{ $membro->peso }}" disabled>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <div class="mt-auto">
                <hr>
                <div class="d-flex gap-2 align-items-center justify-content-center mt-3">
                    <a href="{{ url()->previous() }}" class="btn btn-secondary">Voltar</a>
                </div>
            </div>
        </div>
    </div>
@endsection

