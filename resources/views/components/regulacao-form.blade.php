<form action="{{route('regulacao.update', $chamado->id_chamado)}}" id="formRegulacao" method="post" enctype="multipart/form-data" data-usuario-solicitante-id="{{$chamado->usuario_solicitante_id}}">
    @csrf
    @method('PUT')
    <input type="hidden" name="redirect_to" id="redirect_to" value={{$redirectTo}}>
    <div class="row">
        <div class="mb-3" style="width: auto;">
            <label for="unidadeId" class="fw-semibold">Unidade:<span class="text-danger">*</span></label>
            <select name="unidadeId" id="unidadeId" class="form-select @error('unidadeId') is-invalid @enderror" data-unidade-original-id="{{$unidade->unidade_id}}">
                <option value="">Selecione uma unidade</option>
                @foreach ($unidadesHabilitadas as $unidadeHabilitada)
                    <option value="{{$unidadeHabilitada->unidade_id}}"
                        @selected(old('unidadeId', $unidade->unidade_id) == $unidadeHabilitada->unidade_id)
                        >{{$unidadeHabilitada->setor->ds_nomesetor}}</option>
                @endforeach
            </select>
            @error('unidadeId')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    <div class="row">
        <div id="divSelecaoSetor" class="mb-3" style="width: auto;">
            <label for="setorId" class="fw-semibold">
                Setor (Opcional):
                <em class="fas fa-info-circle" tabindex="0" data-coreui-toggle="tooltip" data-coreui-placement="top" data-coreui-container="body" title="O setor é opcional, porém só pode ser selecionado um setor ou um grupo por vez."></em>
            </label>
            <div class="d-flex align-items-center gap-2">
                <select name="setorId" id="setorId" class="form-select @error('setorId') is-invalid @enderror">
                    <option value="" >Selecione um setor</option>
                    @foreach ($setores as $setor)
                        <option value="{{$setor->setor_id}}" @selected(old('setorId') == $setor->setor_id)>{{$setor->setor->ds_nomesetor}}</option>
                    @endforeach
                </select>
                <em class="fas fa-solid fa-trash text-danger" id="btnExcluirSetor" tittle="Limpar setor" style="cursor: pointer;"></em>
            </div>
            @error('setorId')
                <div class="invalid-feedback" style="display: block;">
                    {{ $message }}
                </div>
            @enderror
        </div>
        <div id="divSelecaoGrupo" class="mb-3" style="width: auto;">
            <label for="grupoId" class="fw-semibold">
                Grupo (Opcional):
                <em class="fas fa-info-circle" tabindex="0" data-coreui-toggle="tooltip" data-coreui-placement="top" data-coreui-container="body" title="O grupo é opcional, porém só pode ser selecionado um setor ou um grupo por vez. Além disso, não é possivel selecionar um atendente especifico para os serviços do grupo."></em>
            </label>
            <div class="d-flex align-items-center gap-2">
                <select name="grupoId" id="grupoId" class="form-select @error('grupoId') is-invalid @enderror">
                    <option value="">Selecione um grupo</option>
                    @foreach ($grupos as $grupo)
                        <option value="{{$grupo->id}}" @selected(old('grupoId') == $grupo->id)>{{$grupo->nome}}</option>
                    @endforeach
                </select>
                <em class="fas fa-solid fa-trash text-danger" id="btnExcluirGrupo" tittle="Limpar grupo" style="cursor: pointer;"></em>
            </div>
            @error('grupoId')
                <div class="invalid-feedback" style="display: block;">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    <div class="row">
        <div id="divSelecaoServico" class="mb-3" style="width: auto;">
            <label for="servico_id" class="fw-semibold">Serviço da unidade:<span class="text-danger">*</span></label>
            <select name="servico_id" id="servico_id" class="form-select @error('servico_id') is-invalid
            @enderror" data-selected-servico="{{old('servico_id')}}" required>
                <option value="">Selecione um setor</option>
            </select>
            @error('servico_id')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
        <div id="divSelecaoAtendente" class="mb-3" style="width: auto;">
            <label for="atendenteId" class="fw-semibold">Atendente da Unidade(Opcional):</label>
            <select name="atendenteId" id="atendenteId" class="form-select @error('atendenteId') is-invalid @enderror" data-selected-atendente="{{old('atendenteId')}}">
                <option value="">Selecione um setor</option>
            </select>
            @error('atendenteId')
                <div class="invalid-feedback">
                    {{ $message}}
                </div>
            @enderror
        </div>
    </div>
    <div class="form-group row mb-3">
        <label for="observacao" id="observacaoLabel" class="fw-semibold">Informação adicional (Opcional):</label>
        <div class="col-lg-8">
            <textarea name="observacao" id="observacao" class="form-control @error('observacao') is-invalid
            @enderror" rows="5">{{old('observacao')}}</textarea>
            @error('observacao')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    <div class="form-group mb-3">
        <label for="imagemEncaminhamento" class="col-lg-3 col-form-label fw-semibold">Imagem (Opcional):</label>
        <div class="col-lg-8">
            <input class="form-control @error('imagemEncaminhamento') is-invalid
            @enderror" type="file" name="imagemEncaminhamento" id="imagemEncaminhamento">
            @error('imagemEncaminhamento')
                <div class="invalid-feedback">
                    {{ $message }} <br>Tamanho max: 1mb. <br>Formatos aceitos: .jpg, .jpeg, .png
                </div>
            @enderror
        </div>
    </div>
    @if ($showButtons)
        <div class="mx-auto">
            <button type="submit" name="tipoEncaminhamento" value="encaminharEIniciar" form="formRegulacao" id="btnIniciarAtendimento" class="btn btn-info">
                <em class="fas fa-sign-in-alt me-2"></em>Iniciar atendimento
            </button>
            <button type="submit" name="tipoEncaminhamento" value="encaminhar" form="formRegulacao" class="btn btn-primary">
                <em class="fas fa-share me-2"></em>Encaminhar chamado
            </button>
            <a href="{{ url()->previous()}}" class="btn btn-secondary">Cancelar</a>
        </div>
    @endif
</form>
@push('scripts')
    @vite('resources/js/regulacao.js')
@endpush