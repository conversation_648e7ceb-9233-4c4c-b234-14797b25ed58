@extends('layouts.app')

@section('content')
    <div class="card">
        <div class="card-header d-flex align-items-center bg-light">
            <div>
                <h2 class="mb-0">{{$chamado->titulo}}</h2>
                <small class="text-muted">Chamado #{{$chamado->id_chamado}}</small>
            </div>
        </div>
  
        <div class="card-body">
            {{-- Status Banner --}}
            <div class="alert {{ $chamado->statusClass }} d-flex align-items-center">
                <em class="fas {{ $chamado->statusIcon }} me-2"></em>
                <div>
                    Status: <strong>{{$chamado->status}}</strong>
                </div>
            </div>

            @if ($chamado->atendimentoIniciado)
                <div class="alert alert-info d-flex align-items-center">
                    <em class="fas fa-comments me-2" aria-hidden="true"></em>
                    <div class="text-center">
                        <PERSON>ste chamado já possui mensagens trocadas.
                        <a href="{{route('chamado.show', $chamado->id_chamado)}}" class="fw-semibold">Clique aqui</a> para visualizar.
                    </div>
                </div>
            @endif

            <div class="row g-4">
                {{-- Informações do Solicitante --}}
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                Informações do Solicitante
                            </h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <strong>Nome:</strong> {{$chamado->usuarioSolicitante->pessoa->nome}}
                                </li>
                                <li class="list-group-item">
                                    <strong>Telefone:</strong> {{$chamado->telefone_contato}}
                                </li>
                                <li class="list-group-item">
                                    <strong>Email:</strong> {{$chamado->usuarioSolicitante->pessoa->ds_emailprincipal}}
                                </li>
                                <li class="list-group-item">
                                    <strong>Setor:</strong> {{$chamado->setorSolicitante->ds_nomesetor}}
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                {{-- Informações do Chamado --}}
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                Informações do Chamado
                            </h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <strong>Serviço:</strong> {{$chamado->servico?->nome}}
                                </li>
                                <li class="list-group-item">
                                    <strong>Criticidade:</strong>
                                    <span class="badge {{ $chamado->servico?->criticidade->getBadgeColor()}}">
                                        {{ $chamado->servico?->criticidade->getlabel()}}

                                    </span>
                                </li>
                                <li class="list-group-item">
                                    <strong>Criado em:</strong> {{$chamado->created_at->format('d/m/Y H:i')}}
                                </li>
                                <li class="list-group-item">
                                    <strong>Última atualização:</strong> {{$chamado->updated_at->format('d/m/Y H:i')}}
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                {{-- Descrição --}}
                <div class="{{$chamado->infoAdicional ? 'col-lg-6' : 'col-lg-12'}}">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                Descrição
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="bg-light p-3 rounded">
                                {!! nl2br(e($chamado->descricao)) !!}
                                @if ($chamado->caminho_arquivo)
                                <div class="mt-3">
                                    <a href="" data-coreui-toggle="modal" data-coreui-target="#imageModalDescricao">
                                        <img src="{{ asset("storage/{$chamado->caminho_arquivo}") }}" class="img-fluid" style="object-fit: contain; max-height: 100px; max-width: 100%;" alt="imagem anexada ao chamado">
                                    </a>
                                </div>
                                <x-image-modal id="imageModalDescricao" :src="asset('storage/' . $chamado->caminho_arquivo)" alt="imagem anexada a descrição do chamado"/>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                {{-- Informações adicionais --}}
                @if ($chamado->infoAdicional)
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    Informações Adicionais
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="bg-light p-3 rounded">
                                    {!! nl2br(e($chamado->infoAdicional->mensagem)) !!}
                                    @if ($chamado->infoAdicional->caminho_arquivo)
                                    <div class="mt-3">
                                        <a href="" data-coreui-toggle="modal" data-coreui-target="#imageModalInfoAdicional">
                                            <img src="{{ asset("storage/{$chamado->infoAdicional->caminho_arquivo}") }}" class="img-fluid" style="object-fit: contain; max-height: 100px; max-width: 100%;" alt="imagem anexada ao chamado">
                                        </a>
                                    </div>
                                    <x-image-modal id="imageModalInfoAdicional" :src="asset('storage/' . $chamado->infoAdicional->caminho_arquivo)" alt="imagem anexada a descrição do chamado"/>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            {{-- Ações --}}
            <div class="d-flex align-items-center mt-4">
                @if ($chamado->status == 'Aberto')
                    <form action="{{route('chamado.atender', $chamado->id_chamado)}}" method="POST">
                        @csrf
                        <button type="submit" class="btn btn-info me-2">
                            <em class="fas fa-sign-in-alt me-2"></em>Atender
                        </button>
                    </form>
                @endif
                {{-- Navegação --}}
                <nav class="ms-auto" aria-label="Navegação entre chamados">
                    <ul class="pagination">
                        <a href="{{route('home.index')}}" class="btn btn-secondary ms-auto">
                            Voltar
                        </a>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
@endsection