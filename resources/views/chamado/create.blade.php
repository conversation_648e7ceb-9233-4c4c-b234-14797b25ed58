@extends('layouts.app')

@section('content')
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div class="col-lg-11 text-center">
                <h3 class="card-title">{{$unidadeAtendente->setor->ds_nomesetor}}</h3>
                <h4>Abrir chamado</h4>

            </div>
            <div class="col-lg-1 ms-auto d-flex align-items-center justify-content-center">
                <img src="{{ asset("storage/{$unidadeAtendente->caminho_logomarca}") }}" class="card-img-right img-fluid" alt="logo do setor {{$unidadeAtendente->setor->ds_nomesetor}}" >
            </div>
        </div>
        <div class="card-body">

            <form action="{{route('chamado.store')}}" method="POST" enctype="multipart/form-data">
                @csrf
                <input type="hidden" name="unidadeAtendenteId" value="{{$unidadeAtendente->setor->id_setor}}">
                <input type="hidden" name="siglaSetor" value="{{$unidadeAtendente->setor->sg_setor}}">
                <input type="hidden" name="usuarioId" value="{{$usuario->id_usuario}}">
                
                <div class="form-group mb-3 row">
                    <label for="campusId" class="col-lg-3 col-form-label fw-semibold" >Campus:<span class="text-danger">*</span></label>
                    <div style="width: auto;">

                        <select name="campusId" id="campusId" class="form-select" required>
                            @foreach ($campi as $campus)
                                <option value="{{ $campus->id_campus}}"
                                    @if(old('campusId', $usuario->setor?->campus->id_campus) == $campus->id_campus) selected @endif>
                                    {{ $campus->ds_nomecampus }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="form-group mb-3 row">
                    <label for="setorSolicitanteId" class="col-lg-3 col-form-label fw-semibold">Local do Atendimento:<span class="text-danger">*</span></label>
                    <div style="width: auto;">

                        <select name="setorSolicitanteId" id="setorSolicitanteId" class="form-select @error('setorSolicitanteId') is-invalid @enderror" data-setor-usuario="{{old( 'setorSolicitanteId', $usuario->setor?->id_setor)}}" required>
                            <option value="">Selecione um Setor</option>
                        </select>
                        @error('setorSolicitanteId')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                </div>

                <div class="form-group mb-3 row">
                    <label for="telefone" class="col-lg-3 col-form-label fw-semibold">Telefone para Contato:<span class="text-danger">*</span></label>
                    <div class="col-lg-3">
                        <input type="text" name="telefone" id="telefone" class="form-control @error('telefone') is-invalid @enderror" 
                        value="{{old('telefone', $usuario->pessoa->ds_telefones)}}" required>
                        @error('telefone')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                </div>

                <div class="form-group mb-3 row">
                    <label for="tombo" class="col-lg-3 col-form-label fw-semibold" id="tomboLabel">Tombo:</label>
                    <div class="col-lg-9">
                        <div id="tombosContainer" class="d-flex flex-wrap gap-2 align-items-start">

                            <div class="d-flex flex-column" style="width: auto;">
                                <div class="input-group">
                                    <input type="text" name="tombos[]" class="form-control 
                                        @error('tombos.0') is-invalid @enderror" 
                                        style="width: {{$errors->has('tombos.0') ? '100px' : '80px'}};" value="{{old('tombos.0')}}">     
                                    <button type="button" class="btn btn-outline-secondary" id="addTomboButton">Adicionar</button>
                                </div>
                                @error('tombos.0')
                                        <div class="invalid-feedback d-block">
                                            Formato inválido - permitido apenas números
                                        </div>
                                @enderror
                            </div>

                            @foreach (old('tombos', []) as $index =>$tombo)
                                @if ($index > 0)
                                    <div class="d-flex flex-column" style="width:auto;">
                                        <div class="input-group">
                                            <input type="text" name="tombos[]" class="form-control @error("tombos.{$index}") is-invalid @enderror" 
                                                value="{{$tombo}}" style="width: {{$errors->has("tombos.{$index}") ? '100px' : '80px'}};" required>
                                            <button type="button" class="btn btn-outline-danger removeTomboButton">Remover</button>
                                        </div>
                                        @error("tombos.{$index}")
                                            <div class="invalid-feedback d-block">
                                                Formato inválido - permitido apenas números
                                            </div>
                                        @enderror
                                    </div>
                                @endif
                            @endforeach

                        </div>
                    </div>
                </div>

                <div class="form-group mb-3 row">
                    <label for="servico" class="col-lg-3 col-form-label fw-semibold">Serviço (Opcional):</label>
                    <div class="col-lg-6">

                        <select name="servico" id="servico" class="form-select @error('servico') is-invalid @enderror">
                            <option value="">Selecione um Serviço</option>
                            @foreach ($servicos as $servico)
                                <option value="{{ $servico->id}}"
                                    @if(old('servico') == $servico->id) selected @endif>
                                    {{ $servico->nome }}
                                </option>
                            @endforeach
                        </select>
                        @error('servico')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                </div>

                <div class="form-group mb-3 row">
                    <label for="titulo" class="col-lg-3 col-form-label fw-semibold">Titulo do Chamado:<span class="text-danger">*</span></label>
                    <div class="col-lg-6">
                        <input type="text" name="titulo" id="titulo" class="form-control @error('titulo') is-invalid @enderror" value="{{old('titulo')}}" required>
                        @error('titulo')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                    @enderror
                    </div>
                </div>

                <div class="form-group mb-3 row">
                    <label for="descricao" class="col-lg-3 col-form-label fw-semibold">Descrição do Chamado:<span class="text-danger">*</span></label>
                    <div class="col-lg-6">
                        <textarea name="descricao" id="descricao" class="form-control @error('descricao') is-invalid @enderror" rows="5" required>{{old('descricao')}}</textarea>
                        @error('descricao')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                </div>

                <div class="form-group mb-3 row">
                    <label for="imagem" class="col-lg-3 col-form-label fw-semibold">Imagem (Opcional):</label>
                    <div class="col-lg-6">
                        <input class="form-control @error('imagem') is-invalid @enderror" type="file" name="imagem" id="imagem">
                        @error('imagem')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">Criar Chamado</button>
            </form>
        </div>

    </div>

@endsection

@push('scripts')
    @vite('resources/js/chamados.js')
@endpush