@extends('layouts.app')

@section('content')
    <div class="container-fluid">
        <div class="row">
            {{-- Coluna do Chat --}}
            <div class="col-lg-9 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title mb-0">{{ $chamado->titulo }}</h5>
                            <small class="text-muted">Aberto em {{ $chamado->created_at->format('d/m/Y, H:i') }}</small>
                        </div>
                        <a href="{{ url()->previous() }}" class="btn btn-secondary btn-sm">Voltar</a>
                    </div>

                    <div class="card-body">
                        {{-- Área de mensagens --}}
                        <div id="chat-messages" data-chamado-id="{{ $chamado->id_chamado }}" class="d-flex flex-column-reverse mb-4" style="height: 500px; overflow-y: auto;">
                            @foreach ($mensagens->sortByDesc('created_at') as $mensagem)
                                @if($mensagem->tipo_usuario == 'Sistema')
                                    <x-mensagens.sistema :$mensagem :$chamado :$ultimaSolicitacaoFechamento />
                                @else
                                    <x-mensagens.usuario :$mensagem :$chamado /> 
                                @endif
                            @endforeach
                        </div>

                        {{-- Área de input --}}
                        <div>
                            
                                    <div id="alertSolicitadoFechamento" class="{{ $chamado->status == 'Solicitado fechamento' ? '' : 'd-none' }} alert alert-danger mb-0 d-flex align-items-start">
                                        <em class="fas fa-exclamation-triangle me-2 mt-1"></em>
                                        <p class="mb-0">Não é possível enviar mensagens enquanto o chamado estiver com o fechamento solicitado, por favor responda o fechamento para continuar.</p>
                                    </div>
                                    
                                
                                    <div id="alertConcluido" class="{{ $chamado->status == 'Concluido' ? '' : 'd-none' }} alert alert-success mb-0 d-flex align-items-start">
                                        <em class="fas fa-check-circle me-2 mt-1"></em>
                                        <p class="mb-0">Este chamado foi concluído. Não é mais possível enviar mensagens.</p>
                                    </div>
                                    
                               
                                    <form id="formEnviarMensagemWrapper" action="{{ route('chamado.storeMessage', $chamado->id_chamado) }}" method="POST" data-chamado-id="{{ $chamado->id_chamado }}" enctype="multipart/form-data" class="d-flex gap-2 {{ $chamado->status == 'Em andamento' ? '' : 'd-none' }}">
                                        @csrf
                                        <div class="d-flex flex-grow-1 align-items-start">
                                            <textarea name="mensagem" id="mensagem" class="form-control me-2" rows="3" placeholder="Digite sua mensagem..." required>{{ old('mensagem') }}</textarea>
                                            <img id="imagemPreview" alt="Preview" class="img-fluid rounded border p-1"
                                                style="object-fit: contain; max-height: 86px; max-width: 200px; display: none;">
                                        </div>
                                        <div class="d-flex flex-column gap-2">
                                            <input type="file" name="imagem" id="imagem" class="d-none">
                                            <label for="imagem" class="btn btn-outline-secondary" style="cursor: pointer;">
                                                <em class="fas fa-paperclip"></em>
                                            </label>
                                            <button type="submit" class="btn btn-primary">
                                                <em class="fas fa-paper-plane"></em>
                                            </button>
                                        </div>
                                    </form>

                                    <div id="chat-values" class="d-none"
                                        data-chamado-fechamento-route="{{ route('chamado.fechamento', $chamado->id_chamado) }}"
                                        data-usuario-logado-id="{{ auth()->id() }}">
                                    </div>
                                    @error('mensagem')
                                    <div class="invalid-feedback d-block">
                                        {{ $message }}
                                    </div>
                                    @enderror
                                    @error('imagem')
                                        <div class="invalid-feedback d-block">
                                            {{ $message }} <br>Tamanho max: 1mb. <br>Formatos aceitos: .jpg, .jpeg, .png
                                        </div>
                                    @enderror 
                              
                        </div>

                    </div>
                </div>
            </div>
            {{-- Coluna de Detalhes --}}
            <div class="col-lg-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Detalhes do Chamado</h5>
                    </div>
                    <div class="card-body">
                        <div>
                            <label class="fw-semibold">Número do chamado</label>
                            <p class="mb-2">{{ $chamado->id_chamado }}</p>
                        </div>

                        <div>
                            <label class="fw-semibold">Solicitante</label>
                            <p class="mb-2">{{ $chamado->usuarioSolicitante->pessoa->nome }}</p>
                        </div>

                        <div>
                            <label class="fw-semibold">Setor Solicitante</label>
                            <p class="mb-2">{{ $chamado->setorSolicitante->ds_nomesetor }}</p>
                        </div>

                        <div>
                            <label class="fw-semibold">Atendente</label>
                            <p class="mb-2" id="atendenteNome">{{ $chamado->usuarioAtendente?->pessoa->nome?? '' }}</p>
                        </div>

                        <div>
                            <label class="fw-semibold" >Unidade Atendente</label>
                            <p class="mb-2" id="labelUnidadeAtendente">{{ $chamado->setorUnidadeAtendente->ds_nomesetor }}</p>
                        </div>

                        <div>
                            <label class="fw-semibold">Status</label>
                            <div class="mb-1">
                                <span id="statusBadge" class="badge {{ $chamado->statusClass }}">{{ $chamado->status }}</span>
                            </div>
                        </div>

                        <div>
                            <label class="fw-semibold" >Serviço</label>
                            <p class="mb-2" id="labelServico">{{ $chamado->servico?->nome ?? '' }}</p>
                        </div>

                        <div>
                            <label class="fw-semibold">Criado em</label>
                            <p class="mb-2">{{ $chamado->created_at->format('d/m/Y, H:i') }}</p>
                        </div>

                        <div>
                            <label class="fw-semibold">Última atualização</label>
                            <p id="ultimaAtualizacao" class="mb-2">{{ $chamado->updated_at->format('d/m/Y, H:i') }}</p>
                        </div>
                            <div id="btnEncaminhar" class="{{ ($chamado->usuario_atendente_id == auth()->id() && !($chamado->status == 'Solicitado fechamento' || $chamado->status == 'Concluido')) ? '' : 'd-none' }}">
                                <button  class="btn btn-secondary w-100 mb-2" data-coreui-toggle="modal" data-coreui-target="#encaminhamentoModal">Encaminhar chamado</button>
                            </div>
        
                            <div >
                                <form id="formFinalizar" method="POST" class="{{ $chamado->status == 'Em andamento' ? '' : 'd-none' }}" action="{{ route('chamado.fechamento', $chamado->id_chamado) }}">
                                    @csrf
                                    <button id="btnFinalizar" type="submit" class="btn btn-primary w-100" name="acao" value="{{ $chamado->usuarioAtual == 'Solicitante' ? 'finalizar' : 'solicitar' }}">
                                        Finalizar chamado
                                    </button>
                                </form>
                            </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Modal de Imagem --}}
    <x-image-modal alt="Imagem enviada"/>

    {{-- Modal de encaminhamento --}}
    <div class="modal fade" id="encaminhamentoModal" tabindex="-1"
        data-encaminhamento-error="{{ $errors->hasAny(['unidadeId', 'setorId', 'servico_id', 'atendenteId', 'observacao', 'imagemEncaminhamento']) ? 'true' : 'false' }}">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title fw-semibold">Encaminhar chamado</h5>
                    <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <x-regulacao-form :$chamado redirectTo="home.index" :showButtons="false"/> 
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Cancelar</button>
                    <button type="submit" form="formRegulacao" class="btn btn-primary" name="tipoEncaminhamento" value="encaminhar">Encaminhar</button>
                </div>
            </div>

        </div>
    </div>

@endsection





@push('scripts')
    @vite('resources/js/showImagePreview.js')
    @vite('resources/js/batepapo.js')
    {{-- Adicionando script para atualizar o chat --}}
    @vite('resources/js/chatUpdate.js')
@endpush
