# Produto

- Propósito: Gestão de chamados/tickets com integração ao SGS, do registro à conclusão.
- Público-alvo: Comunidade interna (servidores, setores, unidades) e equipes de atendimento.
- Principais fluxos/valor:
  - Abertura, acompanhamento e encerramento de chamados com histórico e anexos.
  - Regulação/encaminhamento por setor/unidade, com chat nas interações.
  - Criação e manutenção de serviços pelo chefe de setor.
  - Regra "setor sem setor interno": o próprio setor atua como atendente.
  - Regulação automática para SIC da SRCA por campus (SIC/Petrolina, SIC/Ciências Agrárias, SIC/Juazeiro, SIC/Paulo Afonso, SIC/Senhor do Bonfim, SIC/São Raimundo Nonato, SIC/Salgueiro).
  - Relatórios em PDF e painel gerencial dos chamados.
- Diferenciais:
  - Integração nativa à base SGS (usuários, setores, perfis) e autorização por perfil/permissão.
  - Operação multi-campus e visão gerencial consolidada.
- Limitações e escopo atual:
  - Autenticação legada (SHA1) sem SSO; CORS amplo; ausência de CSP.
  - CI/CD ausente; cobertura de testes inicial.
  - Sem design system próprio (usa CoreUI/Bootstrap).
