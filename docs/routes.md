## Rotas e Páginas

Fonte: `routes/web.php` e templates em `resources/views`.

### Público

- GET `/` → `LoginController@index` (view: `auth/login.blade.php`)
- GET `/login` → `LoginController@index`
- POST `/login` → `LoginController@login`
- POST `/logout` → `LoginController@logout`

### Autenticado (middleware `auth`)

- Resource `home` → `HomeController` (principal: `home/index.blade.php`)
- Resource `servicos` → `ServicoController`
- GET `/usuario/perfil` → `UsuarioController@perfil` (view: `home/perfil.blade.php`)
- GET `/usuario/listagem` → `UsuarioController@index`
- GET `/usuario/{id}/editar` → `UsuarioController@edit`; PUT `/usuario/{id}` → `update`
- GET `/{tipoSetor}/listagem` → `SetorController@index` (setor|unidadeOrganizacional)
- GET `/setor/{tipoSetor}/{id}/editar` → `SetorController@edit`; PUT `/setor/{tipoSetor}/{id}` → `update`

#### Chamados

- GET `/chamado/selecionar-unidade` → lista unidades habilitadas (view: `chamado/selecionar-unidade.blade.php`)
- GET `/chamado/novo/{setorAtendenteId}` → formulário de criação (view: `chamado/create.blade.php`)
- POST `/chamado/novo` → `ChamadoController@store`
- GET `/chamado/preview/{id_chamado}` → preview (view: `chamado/preview.blade.php`)
- GET `/chamado/{id_chamado}` → atendimento/chat (view: `chamado/show.blade.php`)
- POST `/chamado/{id_chamado}/mensagem` → criar mensagem
- POST `/chamado/{id_chamado}/atender` → iniciar atendimento
- POST `/chamado/{id_chamado}/fechamento` → solicitar/finalizar/continuar

#### Regulação

- GET `/regulacao` → index (view: `regulacao/index.blade.php`)
- GET `/regulacao/{id_chamado}/editar` → formulário (view: `regulacao/edit.blade.php`)
- PUT `/regulacao/{id_chamado}` → update/encaminhar

#### Relatórios

- GET `/relatorio/chamados` → PDF (template `relatorios/relatorio-chamados.blade.php`)
- GET `/relatorio/servicos` → PDF (template `relatorios/relatorio-servicos.blade.php`)

#### Perfil

- Resource `perfil` → `PerfilController`

#### AJAX

- GET `/get/setores/{campusId}` → setores por campus
- GET `/get/servicos/{setorId}` → serviços por setor
- GET `/get/atendentes/{setorId}` → atendentes por setor

### Páginas possivelmente órfãs

- `resources/views/login/index.blade.php` (não referenciada por rotas)
- `resources/views/layouts/navigation.blade.php` (não incluída pelo layout)
- Scaffolds de auth (passwords/register/verify) — sem rotas ativas

### Observações

- Usar sempre nomes de rota (`home.index`, `servicos.index`, etc.) nas views
- Ajustar links que usam `route('home')` → `route('home.index')`
