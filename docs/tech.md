# Tech

- Stack e versões (runtimes, frameworks, libs-chave):
  - PHP 8.3 (imagem), <PERSON><PERSON> 12, Postgres 15, Node 21, Vite 4, CoreUI 4, Bootstrap 5.1, Axios, Chart.js.
- Build/Test/Lint/Dev/Deploy (comandos e ferramentas):
  - Dev/build: npm run dev, npm run build (Vite).
  - Testes: PHPUnit (suites Unit/Feature); make test.
  - Docker: containers app (php-fpm), nginx, db (Postgres); compose dev e prod.
  - Deploy: SSH + rsync + docker-compose up -d em produção.
- Convenções (ESLint/Prettier/Commits/Branches):
  - PHP Pint; sem ESLint/Prettier/EditorConfig configurados.
- Arquitetura (monorepo, camadas, aliases):
  - App única (Laravel); Service-Repository; Gates dinâmicos; aliases Vite para assets.
- Notas de segurança (CORS/CSP/authz) e performance:
  - Trocar SHA1 por Hash::check/Argon2 ou SSO/OIDC; parametrizar queries de autorização.
  - Restringir CORS por ambiente e adicionar CSP/headers seguros no Nginx.
  - Remover composer update do build; usar somente composer install para reprodutibilidade.
- Integrações (Supabase/IA/APIs):
  - SGS (pgsql_sgs) para auth/usuários/setores; sem IA em runtime atualmente.
