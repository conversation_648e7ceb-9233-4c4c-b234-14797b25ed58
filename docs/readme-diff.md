## Diferenças e Complementos ao README

O `README.md` atual cobre visão geral, setup e estrutura. Complementos e ajustes sugeridos:

### Stack/Versões

- Adicionar versões resolvidas (PHP 8.3 no Docker, Laravel 12, Vite 4, Bootstrap 5.1, CoreUI 4, Chart.js 4)
- Registrar Node LTS recomendado para build (18/20) em vez de 21

### Deploy

- Documentar pipeline real do `deploy/deploy.sh` e `docker-entrypoint.sh`
- Corrigir diretório de trabalho no entrypoint para `/var/www`

### Convenções

- Explicitar camadas (Controller→Service→Repository→Model) e uso de `Gate`
- Padronizar uso de `route()` com nomes de rota, ex.: `home.index`

### Frontend/Assets

- Padronizar uso do Vite e evitar cópias manuais para `public/js`
- Listar entradas do Vite por página e como referenciá-las com `@vite`

### Itens obsoletos

- Remover referência a arquivos não usados (ex.: `bootstrap.js`, `axios`)
