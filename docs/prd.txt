<context>
# Overview

A Central de Atendimento é um sistema de gestão de chamados/tickets para a comunidade interna, cobrindo o ciclo completo de abertura ao encerramento, com integração nativa ao SGS para autenticação, perfis e setores. O produto entrega visão multi-campus, regulação (manual e automática) e relatórios/painéis gerenciais para acompanhamento de eficiência e atendimento.

Público-alvo principal: solicitantes (comunidade), atendentes (setores), reguladores, chefes de setor/unidade e administradores. Diferenciais incluem integração direta à base SGS (usuários, perfis, setores), operação multi-campus e visão consolidada. Limitações atuais: autenticação legada (SHA1), CORS amplo, ausência de CSP, CI/CD ausente e cobertura de testes inicial (ver Appendix C e F).

# Core Features

- Chamados end-to-end [R-002, R-012, R-013]
  - O que: Abertura, pré-visualização, atendimento/atribuição, conversa (mensagens com imagem), tombos, histórico e encerramento (solicitar/confirmar/continuar). 
  - Por quê: Rastreabilidade e comunicação efetiva entre solicitantes e atendentes; conformidade e auditoria via histórico.
  - Como (alto nível): Controllers orquestram, Services encapsulam regras, Models/Eloquent persistem; upload de imagens em storage público; histórico/atribuições versionam estado (ver Appendix E.2, C.3).

- Regulação (manual e automática SIC/SRCA por campus) [R-003]
  - O que: Encaminhamento de chamados por unidade/setor/serviço/atendente; regra automática para SIC de cada campus (Petrolina, Ciências Agrárias, Juazeiro, Paulo Afonso, Senhor do Bonfim, São Raimundo Nonato, Salgueiro).
  - Por quê: Direcionamento correto e rápido, padronização do fluxo intersetorial.
  - Como: Fila de regulação e regras baseadas em unidade/campus e serviço; registros em histórico; validações em Services.

- Catálogo de Serviços com público-alvo (categoria) [R-001, R-010, R-011]
  - O que: CRUD de serviços com criticidade/status e público-alvo (aluno, servidor, técnico, docente, visitante, etc.). Na experiência do usuário, exibir serviços filtrados pela categoria no login.
  - Por quê: Reduce atrito e melhora descobribilidade; direciona demanda ao atendimento adequado.
  - Como: Models/Controllers/Services de serviço, associação de categorias, filtros no carregamento (ver Appendix D.3 e E.3).

- Perfis/Permissões com Gates dinâmicos [R-014]
  - O que: Controle de acesso por perfil/permissão com criação dinâmica de Gates a partir de dados em `permissoes`.
  - Por quê: Segurança e governança, minimizando lógica de autorização espalhada.
  - Como: `AuthServiceProvider` cria Gates com base no repositório de permissões; Services/Controllers consultam Gates.

- Relatórios e Painéis Gerenciais [R-004, R-005]
  - O que: PDFs de chamados/serviços e painéis para chefes de unidade e administradores (visão consolidada multi-campus).
  - Por quê: Tomada de decisão com indicadores; prestação de contas.
  - Como: Services de relatório (DomPDF), endpoints/views dedicados; agregações via Query Builder/índices (ver Appendix E.3 e F.1).

- Autenticação integrada ao SGS
  - O que: Login customizado com `App\Services\SGS\UsuariosServicos`, uso de perfis/setores do SGS.
  - Por quê: Unifica identidade e autorização corporativas; reduz duplicidade.
  - Como: Conexão `pgsql_sgs`, mapeamento de perfis/setores, migração gradativa para hashing seguro (ver Appendix C.1).

# User Experience

- Personas
  - Solicitante: abre e acompanha chamados; conversa com atendente; envia imagens.
  - Atendente: assume chamado, interage no chat, registra tombos e finaliza.
  - Regulador: gerencia fila e direciona chamados.
  - Chefe de Unidade/Setor: acompanha indicadores e relatórios; visão consolidada.
  - Administrador: governa usuários/perfis/serviços/setores.

- Jornadas principais
  - Abertura → Pré-visualização → Regulação/Encaminhamento → Atendimento/Chat → Tombo (opcional) → Fechamento/Confirmação.
  - Criação/gestão de serviços pelo Chefe de Setor (com público-alvo) → Catálogo filtrado no login [R-001, R-010, R-011].
  - Painéis/Relatórios para chefes/admin [R-004, R-005].

- Considerações de UI/UX
  - Mobile-first, acessibilidade (WCAG), performance de interface e consistência visual (CoreUI/Bootstrap). 
  - Padronização de modais em CoreUI; evitar mix `data-coreui-*` com `data-bs-*` (ver riscos e Appendix E.3). 
  - Ajustes de navegação/links (e.g., `route('home.index')`), e limpeza de páginas órfãs (ver Appendix E.1).
</context>

<PRD>
# Technical Architecture

- Componentes do sistema
  - Laravel 12 (PHP 8.3), MVC + Services + Repositories; Blade (CoreUI 4/Bootstrap 5.1); Vite; Chart.js; jQuery. 
  - Banco: PostgreSQL principal (`pgsql`) e integração SGS via `pgsql_sgs`.
  - Autenticação: login custom `App\Services\SGS\UsuariosServicos`; autorização por Gates dinâmicos.

- Modelos de dados (alto nível)
  - Chamado, ChamadoHistorico, ChamadoMensagem, ChamadoTombo, Atribuicao.
  - Servico, Perfil, Permissao, HabilitarSetor, HabilitarUnidadeOrganizacional.
  - Entidades SGS: Usuario, Setor, Perfil, Campus, etc. (ver Appendix E.2).

- Integrações e APIs
  - Base SGS via conexão `pgsql_sgs` para identidade/perfis/setores.
  - Geração de PDF via DomPDF.

- Infraestrutura e build
  - Docker (php-fpm + nginx + postgres); Vite para assets; Makefile para automação; storage público com symlink.
  - Configurações de Nginx por ambiente esperadas em `.docker/nginx/${APP_ENV}.conf` (ver Appendix B.2).

- Segurança e performance (diretrizes)
  - Migrar SHA1 → `Hash::check`/Argon2; CORS restrito por ambiente; CSP/headers no Nginx.
  - Evitar N+1; Query Builder para consultas complexas; parametrização e cache.

# Development Roadmap

- Fase MVP (atingir produto utilizável rapidamente)
  - Autenticação segura (hashing moderno) e login SGS operativo [R-014].
  - Chamados end-to-end com chat e imagens; tombos e histórico funcionais.
  - Regulação manual funcional; regulação automática SIC/SRCA por campus [R-003].
  - Catálogo de serviços com público-alvo e filtro por categoria no login [R-001, R-010, R-011].
  - Relatórios PDF essenciais; painéis básicos (chefes/admin) [R-004, R-005].

- Fase 2 (robustez, governança e produtividade)
  - Padronizar modais em CoreUI; corrigir navegação/links; remover páginas órfãs.
  - Índices/otimização de queries; cache por chave funcional; mitigação de latência [R-012, R-013, R-015].
  - Gates dinâmicos parametrizados e com cache por usuário/perfil [R-014].
  - Testes unitários e de feature abrangentes; performance/security testing básico [R-006–R-009].

- Fase 3 (segurança/ops/UX avançada)
  - CSP completo; CORS por ambiente; endurecimento de headers (Nginx). 
  - CI/CD; métricas de UX/performance; modo escuro e melhorias de acessibilidade [R-016–R-020].
  - Limpeza de resíduos; versionamento de Nginx; guidelines de assets via Vite.

Critérios de pronto de cada item: definidos no Appendix F (quality gates) e G (validação por requisito).

# Logical Dependency Chain

1) Fundação
   - Migrations/modelos essenciais; autenticação segura; Gates dinâmicos; CRUD de serviços com categorias.
2) Fluxos principais
   - Abertura→Atendimento→Chat→Fechamento (com histórico/tombos) e regulação manual.
3) Regulação automática
   - Implementar regras SIC/SRCA por campus e auditoria.
4) Visões e relatórios
   - Painéis chefes/admin e PDFs.
5) Não-funcionais e operação
   - Índices/cache/latência; testes; CORS/CSP; deploy Nginx.

# Risks and Mitigations

- Autenticação legada (SHA1)
  - Mitigação: migrar para `Hash::check`/Argon2; plano de migração; testes.
- CORS amplo e ausência de CSP
  - Mitigação: CORS por ambiente; CSP/headers no Nginx; checklist de segurança.
- Performance/latência (volume significativo, ~10k alunos)
  - Mitigação: índices, Query Builder, cache; observabilidade; metas de latência (Appendix F).
- Mistura CoreUI/Bootstrap JS (modais)
  - Mitigação: padronização CoreUI; remoção de dependências duplicadas.
- Rotas/links inconsistentes e páginas órfãs
  - Mitigação: correção de rotas; limpeza de arquivos; testes de navegação.
- Build não reprodutível
  - Mitigação: `composer install` (sem `update`) em build; travar versões; CI.

# Appendix

Este Appendix concentra detalhes granulares e rastreabilidade sem alterar o template do PRD. Use as referências internas (A–K) a partir do corpo do PRD.

## A. Baseline e versões (stack)

- Backend: PHP 8.3; Laravel 12; Postgres 15
- Frontend: Node 21; Vite 4; CoreUI 4; Bootstrap 5.1; Chart.js 4; jQuery
- Bibliotecas chave (PHP): `barryvdh/laravel-dompdf`, `brian2694/laravel-toastr`, `guzzlehttp/guzzle`, `laravel/sanctum`, `laravel/ui`, Debugbar, Pint, PHPUnit
- Conexões de banco: `pgsql` (app), `pgsql_sgs` (SGS)
- Origem canônica: `composer.json`, `package.json`, `Dockerfile` (ver B.2)

## B. Dev/Build/Test/Deploy

- Comandos Dev/Build/Test
  - `npm run dev`, `npm run build`; `make test`; `vendor/bin/phpunit`
  - `php artisan`: `key:generate`, `migrate --seed`, `storage:link`
- Docker/Deploy
  - Containers: app (php-fpm), nginx, db (Postgres)
  - Compose dev/prod; deploy via SSH+rsync+`docker compose up -d`
  - Nginx esperado: `.docker/nginx/${APP_ENV}.conf` (versionar)

## C. Segurança e performance

- Autenticação e autorização
  - Migrar SHA1 → `Hash::check`/Argon2; login SGS via `App\Services\SGS\UsuariosServicos`
  - Gates dinâmicos por permissões com parametrização e cache
- Front door
  - CORS restrito por ambiente; CSP/headers seguros no Nginx
- Consultas e caching
  - Evitar N+1; Query Builder para consultas complexas; parametrizar queries; cache funcional por usuário/perfil

## D. Convenções operacionais

- Lint/format: PHP Pint
- Branch/Commits: padronização do time (adotar convenção e registrar)
- Build: `composer install` (evitar `composer update` no build)
- Dados: Eloquent para CRUD simples; Query Builder para consultas complexas (evitar SQL cru salvo necessidade justificada) [R-014]

## E. Inventários “lossless”

### E.1 Rotas e páginas

- Web principais (conforme diagnóstico):
  - `GET /` → `auth.login`
  - `GET /login` → formulário de login
  - `POST /login` → autenticação
  - `POST /logout` → sair
  - `Route::resource('home', HomeController)` → `GET /home` (dashboard/lista)
  - `Route::resource('servicos', ServicoController)` → CRUD de serviços
  - `GET /usuario/perfil` → perfil
  - `GET /usuario/listagem` → listagem de usuários
  - `GET /usuario/{id}/editar`, `PUT /usuario/{id}` → editar/atualizar
  - `GET /{tipoSetor}/listagem` (setor|unidadeOrganizacional)
  - `GET /setor/{tipoSetor}/{id}/editar`, `PUT /setor/{tipoSetor}/{id}`
  - `GET /chamado/selecionar-unidade`
  - `GET /chamado/novo/{setorAtendenteId}`, `POST /chamado/novo`
  - `GET /chamado/preview/{id}`, `GET /chamado/{id}`
  - `POST /chamado/{id}/mensagem`
  - `POST /chamado/{id}/atender`
  - `POST /chamado/{id}/fechamento`
  - `GET /regulacao`
  - `GET /regulacao/{id}/editar`, `PUT /regulacao/{id}`
  - Relatórios: `GET /relatorio/chamados`, `GET /relatorio/servicos`
  - AJAX: `GET /get/setores/{campusId}`, `GET /get/servicos/{setorId}`, `GET /get/atendentes/{setorId}`

- Órfãs/erros conhecidos
  - `resources/views/login/index.blade.php` (órfã)
  - `resources/views/layouts/navigation.blade.php` (referencia `route('home')` errado; não utilizada)
  - Views padrão `resources/views/auth/*` sem rotas ativas

### E.2 Modelos/Tabelas/Migrations (catálogo)

- App Models: `Chamado`, `ChamadoHistorico`, `ChamadoMensagem`, `ChamadoTombo`, `Atribuicao`, `HabilitarSetor`, `HabilitarUnidadeOrganizacional`, `Perfil`, `Permissao`, `Servico`
- SGS Models: `Usuario`, `Setor`, `Perfil`, `Atividade`, `Campus`, `Lotacao`, `Pessoa`, `PessoaCategoria`
- Propósito (alto nível):
  - Chamado e derivados: estado, histórico, mensagens, tombos e atribuições
  - Serviço: catálogo com criticidade/status e categorias de público-alvo
  - Perfis/Permissões: autorização e Gates dinâmicos
  - Habilitações: vínculo de setores/unidades para atendimento/regulação

### E.3 UI/JS e componentes

- Blade Components: `components/image-modal`, `components/mensagens/{usuario,sistema}`, `components/regulacao-form`
- Partials: `layouts/partials/{header,sidebar}`, parciais de setores/unidades
- JS módulos: `resources/js/{chamados.js,regulacao.js,graficos.js,criterio.js,batepapo.js}`
- Padrão de modais: CoreUI (evitar `data-bs-*`); migrar referências mistas

## F. Não-funcionais e quality gates

- Performance
  - Latência: metas por operação crítica; monitorar e otimizar [R-012]
  - Cache: chaves por usuário/perfil/serviço; invalidação clara [R-013]
  - Índices: selecionar por consultas mais frequentes [R-015]
- Testes e qualidade
  - Cobertura alvo por camada (Unit/Feature/E2E) [R-006–R-009]
  - Security testing: autenticação, autorização, headers e CSP
  - Performance testing: cenários de carga representativos

## G. Rastreabilidade (IDs estáveis)

| ID   | Requisito (fonte: [requisitos.md](mdc:docs/requisitos.md)) | Seções do PRD | Validação/Aceitação |
| ---- | ---------------------------------------------------------- | ------------- | ------------------- |
| R-001 | Chefe de setor criar serviços | Core Features; Roadmap | CRUD operante; RBAC; testes de fluxo |
| R-002 | Setor sem setor interno atua como atendente | Core Features; Architecture | Regras aplicadas; testes de atribuição |
| R-003 | Regulação automática para SIC/SRCA por campus | Core Features; Dependency Chain; Roadmap | Casos por campus; histórico/auditoria |
| R-004 | Painel gerencial para chefes de unidade | Core Features; Roadmap | KPIs exibidos; filtros; autorização |
| R-005 | Painel gerencial para admin (todas unidades) | Core Features; Roadmap | Visão consolidada; autorização |
| R-006 | Cobertura completa de testes | Roadmap; F | Relatórios de cobertura por suite |
| R-007 | Testes automatizados (unit/integração/E2E) | Roadmap; F | Pipelines executando suites |
| R-008 | Security testing | Roadmap; C; F | Verificações de headers/CSP/AuthZ |
| R-009 | Performance testing | Roadmap; F | Testes de carga e metas |
| R-010 | Serviços com público-alvo (categoria) | Core Features; UX | Campos/relacionamentos; filtros |
| R-011 | Exibir serviços filtrados por categoria no login | Core Features; UX | Cenários de login; asserts de UI |
| R-012 | Verificar latência das consultas | Core Features; F | Métricas coletadas e analisadas |
| R-013 | Implementar cache | Architecture; F | Acertos de cache; invalidações |
| R-014 | Eloquent vs Query Builder (evitar N+1; segurança/performance) | Architecture; C; D | Linters/reviews; métricas de queries |
| R-015 | Otimização/Índices estratégicos | Roadmap; F | Planos de execução melhorados |
| R-016 | Mobile-first | UX; F | Testes responsivos; audit UX |
| R-017 | Acessibilidade (WCAG) | UX; F | Checklist e ajustes |
| R-018 | Performance UI | UX; F | Lighthouse/perf budgets |
| R-019 | Temas (escuro/claro) | UX; Roadmap | Toggle e tokens |
| R-020 | Consistência visual (design system mínimo) | UX; D | Componentes padronizados |

## H. Riscos (registro estendido)

- Gate dinâmico dependente de dados inconsistentes → validar integridade; fallback seguro; logs.
- Regras SIC por campus podem mudar → externalizar configuração por ambiente; feature flag.
- Volume de anexos/imagens → quotas e compressão; limpeza programada.

## I. Decisões (ADRs leves)

- Padronizar modais em CoreUI; remover dependência de Bootstrap JS em views isoladas.
- Parametrizar queries de autorização e aplicar cache por usuário/perfil.
- Build com `composer install` (sem `update`); travar versões mínimas.

## J. Índice de origem (proveniência)

- Produto: [product.md](mdc:docs/product.md), [.kiro/steering/product.md](mdc:.kiro/steering/product.md)
- Estrutura: [structure.md](mdc:docs/structure.md), [.kiro/steering/structure.md](mdc:.kiro/steering/structure.md)
- Tech: [tech.md](mdc:docs/tech.md), [.kiro/steering/tech.md](mdc:.kiro/steering/tech.md)
- Diagnóstico: [diagnostico-geral-projeto.md](mdc:docs/diagnostico-geral-projeto.md)
- Requisitos: [requisitos.md](mdc:docs/requisitos.md)

## K. Snapshot (opcional; para automação)

```yaml
versions:
  php: "8.3"
  laravel: "12"
  postgres: "15"
  node: "21"
  vite: "4"
ui:
  framework: "CoreUI 4 + Bootstrap 5.1"
security:
  auth_hash: "Argon2 via Hash::check"
  cors: "restrito por ambiente"
  csp: "Nginx"
ops:
  build_policy: "composer install"
  nginx: ".docker/nginx/${APP_ENV}.conf"
integrations:
  sgs_db: "pgsql_sgs"
```

</PRD>

