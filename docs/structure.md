# Estrutura

- Organização de pastas e módulos:
  - app/ (Controllers, Models, Services, Repositories, Providers), resources/views (Blade), resources/js|css|sass, config/, routes/, public/.
- Rotas e páginas (inclua órfãs se houver):
  - Web: login, home, serviços, usuários, setores, chamados, regulação, relatórios.
  - API: /api/user (Sanctum).
  - Órfãs: não identificado neste momento.
- Layout base (header/main/footer) e templates:
  - layouts/app.blade.php com sidebar, header e footer; sections para styles/scripts e yield de conteúdo.
- Componentes reutilizáveis e padrões:
  - components/image-modal, components/mensagens/\*, components/regulacao-form.
- Estilos/temas e tokens:
  - CoreUI + Bootstrap; sem Tailwind; sem tokens/temas centralizados.
- Regras de dependência entre módulos:
  - Providers mapeiam Contracts → Repositories; Services encapsulam regras de negócio; Controllers orquestram fluxos.
