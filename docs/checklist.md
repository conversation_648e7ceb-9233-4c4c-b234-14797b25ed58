## Checklist de Ações

### Correções imediatas

- [ ] Ajustar link Home: `route('home')` → `route('home.index')`
- [ ] Corrigir `docker-entrypoint.sh`: `cd /var/www`
- [ ] Remover `composer update` do Dockerfile (usar apenas `composer install`)
- [ ] Remover `public/hot` do repositório
- [ ] Remover arquivo residual `app/Providers/AuthServiceProvider.php.install`

### Build/Assets

- [ ] Consolidar assets via Vite (remover cópias manuais para `public/js` quando redundantes)
- [ ] Validar entradas do Vite vs. uso real nas views
- [ ] Remover `axios` se não utilizado e `resources/js/bootstrap.js` se obsoleto

### UX

- [ ] Amarrar botão “Atualizar” (perfil) a rota de update ou remover
- [ ] Implementar ou ocultar navegação “Anterior/Próximo” na preview

### Infra

- [ ] Considerar Node LTS (18/20) para builds
- [ ] Garantir `.env` completo para `pgsql` e `pgsql_sgs`

### Documentação

- [ ] Manter versões resolvidas (Composer/NPM lockfiles) registradas em relatórios
- [ ] Atualizar READMEs quando padrões mudarem
