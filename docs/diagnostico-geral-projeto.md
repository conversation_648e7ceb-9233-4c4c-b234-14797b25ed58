### Diagnóstico geral do projeto

#### Tecnologias e dependências

- **Backend**: Lara<PERSON> 12 (PHP 8.2+), Elo<PERSON>, Gates dinâmicos por permissões
- **Pacotes PHP**: `barryvdh/laravel-dompdf`, `brian2694/laravel-toastr`, `guzzlehttp/guzzle`, `laravel/sanctum`, `laravel/ui`, Debugbar, Pint, PHPUnit
- **Frontend**: CoreUI 4, Bootstrap 5.1, Vite, FontAwesome 6, Chart.js 4, jQuery (copiado via vite-static-copy)
- **Banco de dados**: PostgreSQL (conexões `pgsql` e `pgsql_sgs`)
- **Container/Build**: Docker (php-fpm 8.3 + nginx), Make<PERSON><PERSON>, `vite-plugin-static-copy`

#### Funcionalidades principais

- **<PERSON><PERSON><PERSON>**: abrir, pré-visualizar, atender, conversar (mensagens com imagem), solicitar/confirmar fechamento, tombos
- **Regulação**: encaminhamento por unidade/setor/serviço/atendente
- **Serviços**: CRUD com criticidade/status, relatórios
- **Perfis/Permissões**: perfis com permissões, Gates dinâmicos a partir de `permissoes`
- **Relatórios (PDF)**: chamados e serviços via DomPDF
- **Autenticação**: login customizado usando `App\Services\SGS\UsuariosServicos`

#### Deploy e infra

- **Dev**: `docker compose up -d` (serviços `app`, `nginx`, `db`), app em `:8000`, Vite `:5173`
- **Prod**: `docker-compose.prod.yaml` (app+nginx); `docker-entrypoint.sh` executa migrations, caches e `npm run build`
- **Observações**:
  - Arquivo de conf do nginx esperado em `.docker/nginx/${APP_ENV}.conf` não está versionado
  - Exige `php artisan storage:link` (já incluído no Makefile)

#### Integrações com IA

- **Aplicação**: não utiliza IA em runtime
- **Ferramentas de dev**: existem configs Taskmaster/Gemini referenciando Anthropic/OpenAI/Perplexity/OpenRouter (somente para automação; não afeta o app)

#### Componentes de UI (reutilizáveis)

- **Blade Components**: `components/image-modal`, `components/mensagens/{usuario,sistema}`, `components/regulacao-form`
- **Partials**: `layouts/partials/{header,sidebar}` e parciais de setores/unidades

#### Estrutura da aplicação

- **Arquitetura**: MVC + Services + Repositories (integração SGS via `pgsql_sgs`)
- **Providers**: `AuthServiceProvider` cria Gates dinamicamente a partir de `Permissao`
- **Storage**: arquivos públicos em `storage/app/public` com link simbólico

---

### Estrutura do projeto (páginas, rotas, layout, arquivos)

#### Páginas e rotas (web)

- `GET /` → formulário de login (`auth.login`)
- `GET /login` → formulário de login
- `POST /login` → autenticação
- `POST /logout` → sair
- `Route::resource('home', HomeController)` → `GET /home` (dashboard/lista de chamados)
- `Route::resource('servicos', ServicoController)` → CRUD de serviços
- `GET /usuario/perfil` → perfil do usuário
- `GET /usuario/listagem` → listagem de usuários
- `GET /usuario/{id}/editar`, `PUT /usuario/{id}` → edição/atualização de usuário
- `GET /{tipoSetor}/listagem` → listagem (setor|unidadeOrganizacional)
- `GET /setor/{tipoSetor}/{id}/editar`, `PUT /setor/{tipoSetor}/{id}` → edição/atualização de setor/unidade
- `GET /chamado/selecionar-unidade` → seleção de unidade para abertura
- `GET /chamado/novo/{setorAtendenteId}`, `POST /chamado/novo` → abertura de chamado
- `GET /chamado/preview/{id}`, `GET /chamado/{id}` → prévia/atendimento do chamado
- `POST /chamado/{id}/mensagem` → enviar mensagem
- `POST /chamado/{id}/atender` → assumir atendimento
- `POST /chamado/{id}/fechamento` → solicitar/finalizar/continuar
- `GET /regulacao` → fila de regulação
- `GET /regulacao/{id}/editar`, `PUT /regulacao/{id}` → direcionamento
- Relatórios: `GET /relatorio/chamados`, `GET /relatorio/servicos`
- AJAX: `GET /get/setores/{campusId}`, `GET /get/servicos/{setorId}`, `GET /get/atendentes/{setorId}`

#### Páginas órfãs (sem rota/link direto)

- `resources/views/login/index.blade.php`
- `resources/views/layouts/navigation.blade.php`
- Views padrão em `resources/views/auth/*` (register/reset/verify/profile) não possuem rotas ativas

#### Estrutura geral de layout

- **Layout autenticado**: `layouts.app` → inclui `partials.sidebar` e `partials.header`; `@yield('content')`; footer com info de DB (não-prod)
- **Layout convidado**: `layouts.guest` com navbar fixa e footer fixo

#### Pastas/arquivos principais

- `app/Http/Controllers`, `app/Services`, `app/Models` (inclui `Models/SGS`), `app/View/Components`
- `resources/views` organizado por domínio (`chamado`, `regulacao`, `administrador/*`, `layouts`, `components`)
- `resources/js` (módulos: `chamados.js`, `regulacao.js`, `graficos.js`, `criterio.js`, `batepapo.js`, etc.)
- `resources/sass` (CoreUI + customizações mínimas), `resources/css`
- `routes/{web,api}.php`, `config/*`, `database/*` (migrations/seeders)
- `Dockerfile`, `docker-compose*.yaml`, `vite.config.js`, `Makefile`

#### Layouts incompletos/erros

- `layouts/navigation.blade.php` referencia `route('home')` (inexistente; correto é `home.index`) e não é utilizado

---

### Componentes

- **Reutilizáveis**: `image-modal`, `mensagens.usuario`, `mensagens.sistema`, `regulacao-form`
- **Duplicados/conflitos**: arquivo residual `app/Providers/AuthServiceProvider.php.install` (remover)
- **Não utilizados**: `layouts/navigation.blade.php`, `login/index.blade.php`, views de `auth/*` não usadas
- **Estrutura repetida**: modais de relatório (home/serviços) com marcação semelhante → extrair para componente

---

### Estilo e visual

- **Tema**: claro (CoreUI padrão)
- **Paleta e fontes**: tokens CoreUI/Bootstrap padrão (sem overrides efetivos em `_variables.scss`)
- **Responsividade**: uso consistente de grid; tabelas com `min-width` e imagens com altura fixa podem exigir rolagem em telas pequenas
- **Conflitos de estilo/JS**: mistura `data-coreui-*` com `data-bs-*` (Bootstrap JS só é carregado em algumas páginas)

---

### Interações e lógica

- **Ações configuradas**:
  - `onchange`: filtros, selects (campus/setor/serviço/atendente), ordenação, smart search
  - `submit`: login, filtros, criar/editar serviço, mensagens, fechamento/encaminhamento
  - `click`: abrir modais, adicionar/remover tombos, abrir imagem
  - `keydown`: Enter envia mensagem (sem Shift)
- **Interações incompletas/erros**:
  - Inconsistência de valor em filtro “Solicitado fechamento” (valor com espaço vs. comparação com underscore) na home
  - Links para `route('home')` em alguns arquivos (deve ser `route('home.index')`)
- **Modais/AJAX**:
  - Modais CoreUI (`data-coreui-*`) e Bootstrap (`data-bs-*`); fetch para `/get/setores`, `/get/servicos`, `/get/atendentes`

---

### Problemas e alertas

- **Arquivos residuais**: `app/Providers/AuthServiceProvider.php.install`
- **Rotas/links**: `route('home')` inexistente
- **JS UI**: mistura CoreUI/Bootstrap para modais → risco de não funcionamento onde Bootstrap JS não está incluso
- **Filtro**: valor “Solicitado fechamento” inconsistente na home
- **Deploy**: `.docker/nginx/${APP_ENV}.conf` ausente no repositório
- **Performance/boas práticas**: Gates com `DB::select` interpolado; considerar parametrização e cache

---

### Integrações externas

- **Supabase/Google/Auth externas**: não há
- **SGS (externo via BD)**: integração via conexão `pgsql_sgs`
- **Storage/RLS**: storage local público (sem RLS)

---

### Sugestões

- **Rotas/Navegação**: corrigir `route('home')` → `route('home.index')`; remover ou ativar views de auth não utilizadas
- **UI/JS**: padronizar modais em CoreUI (remover `data-bs-*` e dependência de Bootstrap JS em views isoladas); extrair modal de relatório para componente Blade
- **Código/Providers**: remover `AuthServiceProvider.php.install`; otimizar criação de Gates (parametrizar queries e cache por usuário/perfil)
- **Estilo/Responsivo**: reduzir alturas fixas de imagens, melhorar comportamento de tabelas em mobile (stack/scroll controlado)
- **Deploy**: versionar configs Nginx em `.docker/nginx/` (dev/prod); revisar `.env` para `DB_CONNECTION=pgsql`
- **Manutenção**: consolidar assets via Vite (evitar cópias estáticas desnecessárias); limpar views órfãs
