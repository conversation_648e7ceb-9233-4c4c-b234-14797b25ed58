## Deploy e Infraestrutura

### Contêineres

- App: `php:8.3-fpm` com Composer e Node (via NodeSource 21.x)
- Nginx: `nginx:1.27-alpine`
- Banco (dev): `postgres:15-alpine`

### docker-compose (dev)

- Serviços: `app`, `nginx`, `db`
- Portas: `nginx` 8000→80, Vite 5173→5173, Postgres 5432
- Monta `./:/var/www`

### docker-compose (prod)

- Serviços: `app`, `nginx` (com certs montados)
- `nginx` expõe 80/443

### Dockerfile — pipeline

1. Copia fonte e `composer`
2. Instala libs do sistema e extensões PHP (`pdo_pgsql`, `gd`, etc.)
3. `composer install --no-dev --optimize-autoloader`
4. Ajusta `php.ini` (upload, memory, timeouts)
5. Expõe 9000 (php-fpm)

Observação: evitar `composer update` em produção; preferir somente `install` com lockfile.

### EntryPoint

`docker-entrypoint.sh` (remoto via `deploy.sh`):

- Composer install, `php artisan migrate --seed --force`
- Limpeza/caches de rota/config/view
- `npm install && npm run build`

Correção sugerida: `cd /var/www` (o WORKDIR do container) em vez de `/var/www/html/`.

### Deploy remoto

`deploy/deploy.sh`:

- Prepara SSH (chave em `PRIVATE_KEY`)
- `rsync` do diretório fonte para `$DESTINATION_DIR` no servidor
- `docker-compose -f docker-compose.prod.yaml up -d`
- Executa `docker-entrypoint.sh` dentro do container `central-atendimento-app`

### Pós-deploy (recomendações)

- Remover `public/hot` do repo e garantir não existir em produção
- Validar permissões de `storage/` e `bootstrap/cache`
- Garantir variáveis `.env` para `pgsql` e `pgsql_sgs`
