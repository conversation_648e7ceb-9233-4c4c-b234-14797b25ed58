## Convenções e Padrões

### Código (PHP/Laravel)

- Estilo: PSR-12 (via `laravel/pint`)
- Nomeação:
  - Controllers: sufixo `Controller` (injeção de serviços por construtor)
  - Services: sufixo `<PERSON><PERSON><PERSON>` (regras de negócio)
  - Models: relacionamentos explícitos e conexões dedicadas quando necessário (`pgsql_sgs`)
  - Repositories: Contracts em `Repositories/Contracts`, binds em `RepositorioServicoProviders`
- Organização:
  - Controllers finos → delegam para Services
  - Services usam Repositories/Models
  - Views Blade com components: `x-image-modal`, `x-mensagens.*`, `x-regulacao-form`
  - Autoload de helpers: `app/Helpers/FormSaveInSessionHelper.php`

### Views/Blade

- Layout base: `layouts/app.blade.php` (sidebar/header/footer)
- Layout convidado: `layouts/guest.blade.php` (login)
- Partials: `layouts/partials/*` (header, sidebar, items, database-info)
- Componentes: `resources/views/components/*`

### Frontend (JS/CSS)

- Build com Vite, entradas explícitas por página
- Uso de `fetch` nativo para AJAX
- Bibliotecas: Bootstrap/CoreUI/Chart.js
- Scripts específicos: `chamados.js`, `regulacao.js`, `batepapo.js`, `criterio.js`, `graficos.js`

### Segurança e Autorização

- Autenticação: sessão Laravel, login contra SGS (`UsuariosServicos`)
- Autorização: `Gate::denies(...)` em controllers de áreas restritas

### Tradução/Locale

- `locale` padrão: `pt-BR`; fallback: `en`

### Rotas e Nomeação

- Resources nomeados (ex.: `home.index`, `servicos.index`)
- Convenção: usar os nomes de rota nas views (evitar caminhos hardcoded)
