## UI e Componentes

### Layouts

- `layouts/app.blade.php`: sidebar escura (CoreUI), header sticky, content em `container-lg`, footer simples; inclui `@vite` e scripts estáticos
- `layouts/guest.blade.php`: tela pública de login com navbar fixa e footer fixo

### Componentes Blade

- `x-image-modal` — modal para exibir imagens anexadas
- `x-mensagens.sistema` e `x-mensagens.usuario` — balões do chat
- `x-regulacao-form` — formulário componetizado de encaminhamento

### Padrões de página (exemplos)

- Home (dashboard): filtros, tabela responsiva com `min-width`, gr<PERSON><PERSON><PERSON> (Chart.js), modais de relatório
- Chamado (create/preview/show): formulários com selects dependentes e chat com upload de imagem
- Regulação (index/edit): tabela de fila e formulário de encaminhamento
- Administração (serviços, perfis, setores, usuários): tabelas e formulários CRUD

### Scripts

- `graficos.js` (Chart.js), `criterio.js` (filtros dinâmicos), `chamados.js` (create), `batepapo.js` (chat), `regulacao.js` (encaminhamento), `showImagePreview.js`

### Estilos

- Tema claro (Bootstrap/CoreUI)
- `custom.css` para cards uniformes em Selecionar Unidade
- `_variables.scss` com overrides comentados (atual sem customização global)

### Responsividade

- Uso de grid `col-lg/col-md`, tabelas com `table-responsive`
- Pontos de atenção: tabelas com `min-width` podem exigir scroll horizontal em telas pequenas
