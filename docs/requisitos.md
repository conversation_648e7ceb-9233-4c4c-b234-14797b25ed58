- Chefe de setor criar serviços;

- Setor que vai receber chamados e não tem setor interno (próprio setor é atendente);

- Regulação automática chamados para os setores de Serviço de informação ao cidadão (SIC) da Secretaria de Registro e Controle Acadêmico (SRCA). Um setor SIC por campus:
  -- SIC/Petrolina
  -- SIC/Ciências Agrárias
  -- SIC/Juazeiro
  -- SIC/Paulo Afonso
  -- SIC/Senhor do Bonfim
  -- SIC/São Raimundo Nonato
  -- SIC/Salgueiro

- Painel gerencial dos chamados para os chefes de unidade;
- Painel gerencial dos chamados para usuário admin (todas as unidades);

Testes e Qualidade

- Cobertura de testes completa (Testes de unidade, integração, E2E)
- Testes de unidade, integração, E2E automatizados
- Security testing
- Performance testing

- Cadastrar serviços (chamados) especificando o público alvo (categoria do usuário: aluno, servidor, técnico, docente, visitante, etc)

- Ao efetuar o login, disponibilizar os serviços filtrando pelo público alvo (categoria)

- Verificar a latência das consultas;

- Implementar cache

- Eloquent vs Raw SQL
  -- Eloquent para CRUD simples, Query Builder Laravel para consultas complexas
  -- Evitar Eloquent puro (problema N+1)
  -- Segurança contra SQL injection, performance otimizada para consultas complexas

### Performance do Banco

- **Risco**: Consultas lentas devido ao volume de dados (~10k alunos)
- **Mitigação**: Otimização de queries, indexação estratégica, implementação de cache

## Considerações de UI/UX

- **Mobile-First**: Design responsivo prioritário para dispositivos móveis
- **Acessibilidade WCAG**: Contraste adequado, navegação por teclado, textos alternativos
- **Performance**: Carregamento otimizado, interface fluida
- **Temas**: Suporte a modo escuro/claro
- **Consistência**: Sistema de design unificado com cores institucionais
