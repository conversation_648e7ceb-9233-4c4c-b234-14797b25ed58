## Integrações

### Banco de Dados

- PostgreSQL (aplicação): conexão `pgsql`
- PostgreSQL (SGS): conexão `pgsql_sgs` (schema `sgs`)

### Serviços externos

- PDFs: `barryvdh/laravel-dompdf` — geração e streaming de PDF
- Toastr: `brian2694/laravel-toastr` — notificações de UI

### IA / Supabase / Autenticações de terceiros

- Não há integrações com IA (OpenAI, Vertex, etc.)
- Não há uso de Supabase/Auth externos

### Rotas AJAX internas

- /get/setores/{campusId} — lista setores (SGS)
- /get/servicos/{setorId} — lista serviços por setor
- /get/atendentes/{setorId} — lista atendentes por setor

### Observações

- Login customizado contra SGS em `App\\Services\\SGS\\UsuariosServicos` (hash SHA-1 do par login+senha)
- Verificar se credenciais e hosts estão definidos no `.env` (`DB_*` e `DB_*_SGS`)
