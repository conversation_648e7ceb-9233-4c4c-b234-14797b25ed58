## Diagnóstico do Projeto — Central de Atendimento

Este conjunto de documentos consolida um diagnóstico técnico completo e padronizado do projeto, com foco em stack, convenções, rotas, UI, deploy, integrações e alertas.

- Consulte o stack: [stack.md](stack.md)
- Convenções e guias: [conventions.md](conventions.md)
- Processo de deploy: [deploy.md](deploy.md)
- Integrações e serviços: [integrations.md](integrations.md)
- Rotas e páginas: [routes.md](routes.md)
- UI e componentes: [ui.md](ui.md)
- Alertas e problemas: [alerts.md](alerts.md)
- Checklist de ações: [checklist.md](checklist.md)
- Diferenças frente ao README: [readme-diff.md](readme-diff.md)

Observações gerais:

- Ambiente padrão: Laravel 12 em PHP 8.3 (runtime via Docker) com Postgres; frontend em Vite + Bootstrap/CoreUI + Chart.js.
- Arquitetura com camadas Controller → Service → Repository → Model, e Views Blade com componentes reutilizáveis.
