## Stack e Vers<PERSON><PERSON>

Fontes de verdade consultadas: `composer.json`, `composer.lock` (quando existente), `package.json`, `package-lock.json`, `Dockerfile`, `vite.config.js`, `config/*.php`.

### Backend

- Linguagem: PHP (constraint: ^8.2; runtime Docker: 8.3-fpm)
- Framework: Laravel 12.x
- Pacotes principais (prod):
  - barryvdh/laravel-dompdf: ^3.1 — geração de PDF
  - brian2694/laravel-toastr: ^5.57 — notificações
  - laravel/sanctum: ^4.0 — autenticação/token
  - laravel/ui: ^4.4 — scaffolds clássicos
- Pacotes dev:
  - barryvdh/laravel-debugbar, phpunit/phpunit ^12, laravel/pint, laravel/sail

### Banco de Dados

- PostgreSQL
  - Conexões: `pgsql` (app) e `pgsql_sgs` (SGS), ver `config/database.php`
  - Schemas: `public` (app), `sgs` (SGS)

### Frontend

- Build: Vite 4
- UI: Bootstrap 5.1.3, CoreUI 4, Font Awesome
- Gráficos: Chart.js 4

### Node/JS

- Node no container: 21.x
- `package.json`:
  - devDependencies: `vite`, `laravel-vite-plugin`, `sass`, `@coreui/coreui`, `@popperjs/core`
  - dependencies: `@fortawesome/fontawesome-free`, `chart.js`, `lodash`

### Infra/DevOps

- Docker: `php:8.3-fpm`, Nginx `1.27-alpine`
- docker-compose (dev e prod)
- Deploy remoto via `deploy/deploy.sh` (rsync + docker-compose up + entrypoint)

### Observações de versão e drift

- PHP: constraint ^8.2 vs. runtime 8.3 (ok, compatível)
- Node: 21 (não LTS). Avaliar LTS (18/20) para pipelines e build previsível
- Vite/Assets: mistura de `@vite` e cópias para `public/js` via `viteStaticCopy`
