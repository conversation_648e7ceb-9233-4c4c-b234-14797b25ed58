## Alertas e Riscos

### Navegação

- Link para Home usa `route('home')` em `layouts/navigation.blade.php`; correto é `route('home.index')`

### Deploy/Infra

- `docker-entrypoint.sh` usa `cd /var/www/html/`, mas o `WORKDIR` é `/var/www`; ajustar para evitar falhas
- `Dockerfile` executa `composer update` — evitar em produção (usar apenas `install` com lockfile)
- `public/hot` presente: não deve existir em produção; remover do repositório

### Assets/Build

- Mistura entre `@vite` e cópias estáticas para `public/js` (jQuery, Toastr, app.js); consolidar apenas via Vite

### Código morto ou duplicado

- `app/Providers/AuthServiceProvider.php.install`: arquivo residual a remover
- `resources/js/bootstrap.js`: não utilizado
- Dependência `axios`: não utilizada (não há imports)

### UX/Funcionalidade

- Botão “Atualizar” em `home/perfil.blade.php` sem ação definida
- Navegação “Anterior/Próximo” na preview de chamado aponta para `#`

### Versões/Drift

- Node 21 (não-LTS) para build; avaliar mudança para LTS
