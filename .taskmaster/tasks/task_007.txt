# Task ID: 7
# Title: Desenvolver Regulação Automática SIC/SRCA por Campus
# Status: pending
# Dependencies: 6
# Priority: medium
# Description: Implementar regras automáticas de encaminhamento para SIC de cada campus baseado em unidade/serviço
# Details:
Criar sistema de regras automáticas para os 7 campus (Petrolina, Ciências Agrárias, Juazeiro, Paulo <PERSON>, Senhor do <PERSON>fim, São Raimundo Nonato, Salgueiro):

```php
// RegulacaoAutomaticaService
class RegulacaoAutomaticaService {
    private $regrasSIC = [
        'petrolina' => ['servicos' => ['informacao', 'transparencia'], 'setor_id' => 1],
        'juazeiro' => ['servicos' => ['informacao', 'transparencia'], 'setor_id' => 2],
        // ... outros campus
    ];
    
    public function aplicarRegrasAutomaticas(Chamado $chamado) {
        $campus = $this->getCampusFromUser($chamado->usuario);
        $servico = $chamado->servico;
        
        if ($this->isSICService($servico, $campus)) {
            $this->encaminharParaSIC($chamado, $campus);
        } elseif ($this->isSRCAService($servico, $campus)) {
            $this->encaminharParaSRCA($chamado, $campus);
        }
    }
    
    private function encaminharParaSIC(Chamado $chamado, string $campus) {
        $setorSIC = $this->regrasSIC[$campus]['setor_id'];
        $chamado->update(['setor_atendente_id' => $setorSIC, 'status' => 'regulado_automaticamente']);
        
        $this->historicoService->registrar($chamado, "Encaminhado automaticamente para SIC - {$campus}");
    }
}

// Event Listener
class ChamadoCriado {
    public function handle(ChamadoCriadoEvent $event) {
        $this->regulacaoAutomaticaService->aplicarRegrasAutomaticas($event->chamado);
    }
}
```

# Test Strategy:
Testes para cada campus e tipo de serviço, validação de regras SIC/SRCA, verificar auditoria no histórico, testes de fallback para regulação manual

# Subtasks:
## 1. Levantamento e Definição das Regras por Campus [pending]
### Dependencies: None
### Description: Mapear e documentar as regras de encaminhamento SIC/SRCA para cada campus, considerando unidades, serviços e setores responsáveis.
### Details:
Consultar gestores de cada campus, levantar serviços atendidos por SIC/SRCA, definir critérios de encaminhamento e validar regras com as áreas envolvidas.

## 2. Implementação do RegulacaoAutomaticaService [pending]
### Dependencies: 7.1
### Description: Desenvolver o serviço responsável por aplicar as regras automáticas de encaminhamento para SIC/SRCA conforme definido no levantamento.
### Details:
Codificar lógica de decisão, parametrizar regras por campus, implementar métodos de encaminhamento e integração com histórico.

## 3. Integração com Eventos do Sistema [pending]
### Dependencies: 7.2
### Description: Integrar o RegulacaoAutomaticaService aos eventos de criação e atualização de chamados para garantir o acionamento automático das regras.
### Details:
Configurar listeners para eventos relevantes, garantir execução automática do serviço ao criar/atualizar chamados.

## 4. Testes para Cada Campus e Serviço [pending]
### Dependencies: 7.3
### Description: Elaborar e executar testes abrangentes para validar o encaminhamento automático em todos os campus e tipos de serviço.
### Details:
Criar cenários de teste para cada campus, simular diferentes serviços, validar resultados e registrar evidências de conformidade.

## 5. Implementação de Fallback para Regulação Manual [pending]
### Dependencies: 7.4
### Description: Desenvolver mecanismo de fallback para permitir regulação manual caso as regras automáticas não sejam aplicáveis ou falhem.
### Details:
Adicionar lógica de exceção, interface para intervenção manual e registro de justificativas no histórico do chamado.

## 6. Auditoria e Registro no Histórico [pending]
### Dependencies: 7.5
### Description: Garantir que todas as ações automáticas e manuais de regulação sejam auditadas e registradas no histórico do chamado.
### Details:
Implementar logs detalhados, registrar decisões automáticas e manuais, garantir rastreabilidade e conformidade com requisitos de auditoria.

