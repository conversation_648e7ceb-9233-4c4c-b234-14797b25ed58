# Task ID: 15
# Title: Checklist: UX (navegação e ações pendentes)
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Correções de usabilidade: botão Atualizar do perfil e navegação Anterior/Próximo na preview de chamado.
# Details:


# Test Strategy:


# Subtasks:
## 1. <PERSON><PERSON> botão “Atualizar” (perfil) [pending]
### Dependencies: None
### Description: Em `resources/views/home/<USER>
### Details:


## 2. Implementar/ocultar navegação “Anterior/Próximo” na preview [pending]
### Dependencies: None
### Description: Corrigir links na preview de chamado (`resources/views/chamado/preview.blade.php`): implementar paginação válida ou ocultar.
### Details:


