# Task ID: 16
# Title: Checklist: Infra (Node LTS e .env)
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Ajustar versão do Node para LTS em builds e garantir variáveis de ambiente completas para pgsql e pgsql_sgs.
# Details:


# Test Strategy:


# Subtasks:
## 1. Atualizar Node para LTS no build [pending]
### Dependencies: None
### Description: Ajustar `Dockerfile`/CI para usar Node LTS (18/20) nas etapas de build front-end.
### Details:


## 2. Validar `.env` para `pgsql` e `pgsql_sgs` [pending]
### Dependencies: None
### Description: Garantir que variáveis de conexão existam e estão corretas para ambos bancos; documentar chaves obrigatórias.
### Details:


