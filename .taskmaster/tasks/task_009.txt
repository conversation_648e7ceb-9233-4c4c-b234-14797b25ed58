# Task ID: 9
# Title: Desenvolver Sistema de Tombos e Histórico Completo
# Status: pending
# Dependencies: 5
# Priority: medium
# Description: Implementar registro de tombos de equipamentos e sistema completo de auditoria/histórico de chamados
# Details:
Criar sistema de tombos para registro de equipamentos e histórico completo:

```php
// ChamadoTomboController
class ChamadoTomboController extends Controller {
    public function store(Request $request, Chamado $chamado) {
        Gate::authorize('manage-tombos');
        
        $request->validate([
            'numero_tombo' => 'required|string|max:50',
            'descricao_equipamento' => 'required|string|max:255',
            'observacoes' => 'nullable|string|max:500'
        ]);
        
        $tombo = ChamadoTombo::create([
            'chamado_id' => $chamado->id,
            'numero_tombo' => $request->numero_tombo,
            'descricao_equipamento' => $request->descricao_equipamento,
            'observacoes' => $request->observacoes,
            'usuario_id' => auth()->id()
        ]);
        
        $this->historicoService->registrar($chamado, "Tombo adicionado: {$request->numero_tombo}");
        
        return redirect()->back()->with('success', 'Tombo registrado com sucesso');
    }
}

// HistoricoService
class HistoricoService {
    public function registrar(Chamado $chamado, string $acao, array $dados_anteriores = null) {
        ChamadoHistorico::create([
            'chamado_id' => $chamado->id,
            'usuario_id' => auth()->id(),
            'acao' => $acao,
            'dados_anteriores' => $dados_anteriores ? json_encode($dados_anteriores) : null,
            'dados_atuais' => json_encode($chamado->toArray()),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }
}
```

# Test Strategy:
Testes CRUD para tombos, validação de autorização, verificar registro correto no histórico, testes de auditoria completa de mudanças

# Subtasks:
## 1. Implementação do ChamadoTomboController [pending]
### Dependencies: None
### Description: Desenvolver e configurar o controller responsável pelo registro de tombos vinculados aos chamados, incluindo validação de dados e integração com sistema de autorização.
### Details:
Implementar métodos de criação, edição e exclusão de tombos, garantir uso correto do Gate 'manage-tombos' e validação dos campos obrigatórios conforme regras de negócio.

## 2. Criação do Sistema de Tombos [pending]
### Dependencies: 9.1
### Description: Projetar e implementar a estrutura de dados e as interfaces necessárias para o cadastro, consulta e gerenciamento dos tombos de equipamentos.
### Details:
Criar migrations, models e views para registro de tombos, garantindo integridade referencial com chamados e usuários, além de interface amigável para consulta e edição.

## 3. Implementação do HistoricoService [pending]
### Dependencies: 9.2
### Description: Desenvolver o serviço responsável pelo registro detalhado de todas as ações realizadas nos chamados, incluindo alterações de tombos e demais eventos relevantes.
### Details:
Implementar lógica para registrar ações, armazenar dados anteriores e atuais, capturar IP e user agent, e garantir versionamento consistente do histórico.

## 4. Testes de Auditoria e Integridade [pending]
### Dependencies: 9.3
### Description: Elaborar e executar testes automatizados e manuais para validar a integridade dos registros de tombos e a consistência do histórico de auditoria.
### Details:
Realizar testes CRUD, simular cenários de alteração e exclusão, validar registros no histórico, testar permissões e garantir rastreabilidade completa das ações.

## 5. Documentação e Validação de Segurança [pending]
### Dependencies: 9.4
### Description: Documentar o funcionamento do sistema de tombos e histórico, além de revisar e validar todos os controles de segurança e autorização implementados.
### Details:
Produzir documentação técnica e de usuário, revisar uso de Gates, validar proteção contra acessos indevidos e garantir conformidade com requisitos de auditoria.

