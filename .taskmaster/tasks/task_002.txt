# Task ID: 2
# Title: Implementar Sistema de Permissões com Gates Dinâmicos
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Criar sistema de autorização baseado em Gates dinâmicos gerados a partir da tabela de permissões
# Details:
Implementar Gates dinâmicos no AuthServiceProvider baseados na tabela 'permissoes'. Criar cache por usuário/perfil para otimização. Implementar middleware de autorização:

```php
// AuthServiceProvider
public function boot() {
    $permissions = Permission::all();
    foreach ($permissions as $permission) {
        Gate::define($permission->name, function ($user) use ($permission) {
            return $user->hasPermission($permission->name);
        });
    }
}

// Middleware
if (!Gate::allows('manage-tickets')) {
    abort(403);
}
```

Implementar cache Redis/array para permissões por usuário com TTL de 1 hora.

# Test Strategy:
Testes unitários para Gates, testes de autorização por perfil, verificar cache de permissões, testes de middleware com diferentes níveis de acesso

# Subtasks:
## 1. Modelagem e Implementação da Tabela de Permissões [pending]
### Dependencies: None
### Description: Criar a tabela 'permissoes' no banco de dados, definir o modelo Permission e relacionamentos necessários com usuários e/ou perfis.
### Details:
Definir campos essenciais (id, nome, descrição, timestamps), criar migrations e seeders para permissões iniciais. Garantir integridade referencial e estrutura flexível para futuras permissões.

## 2. Implementação dos Gates Dinâmicos no AuthServiceProvider [pending]
### Dependencies: 2.1
### Description: Configurar o AuthServiceProvider para registrar dinamicamente os Gates a partir dos registros da tabela 'permissoes'.
### Details:
No método boot, buscar todas as permissões e criar um Gate para cada uma, utilizando closures que consultam se o usuário possui a permissão correspondente.

## 3. Criação do Cache de Permissões por Usuário/Perfil [pending]
### Dependencies: 2.2
### Description: Implementar cache (Redis ou array) para armazenar permissões de cada usuário/perfil, com TTL de 1 hora.
### Details:
Desenvolver lógica para armazenar e recuperar permissões do cache, invalidando e atualizando quando necessário. Garantir fallback para consulta ao banco caso o cache expire.

## 4. Implementação do Middleware de Autorização [pending]
### Dependencies: 2.2, 2.3
### Description: Desenvolver middleware customizado para verificar permissões via Gates antes de executar ações protegidas.
### Details:
Criar middleware que utilize Gate::allows para checar permissões, abortando com 403 em caso de acesso negado. Integrar middleware às rotas e controladores relevantes.

## 5. Testes Unitários e de Integração [pending]
### Dependencies: 2.4
### Description: Elaborar testes para validar o correto funcionamento dos Gates, cache de permissões e middleware de autorização.
### Details:
Cobrir cenários de permissões concedidas e negadas, cache hit/miss, e diferentes perfis de usuário. Garantir cobertura de código e simular situações de segurança.

## 6. Documentação e Validação de Segurança [pending]
### Dependencies: 2.5
### Description: Documentar a arquitetura do sistema de permissões, uso dos Gates, cache e middleware. Realizar revisão de segurança.
### Details:
Produzir documentação técnica e de uso, incluindo exemplos de integração. Realizar checklist de segurança para evitar brechas e validar a robustez do sistema.

