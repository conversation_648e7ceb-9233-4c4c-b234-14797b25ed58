# Task ID: 11
# Title: Implementar Otimizações de Performance e Cache
# Status: pending
# Dependencies: 10
# Priority: medium
# Description: Aplicar otimizações de consultas, índices estratégicos e sistema de cache para melhorar performance
# Details:
Implementar otimizações de performance e cache:

```php
// Índices estratégicos (migration)
Schema::table('chamados', function (Blueprint $table) {
    $table->index(['status', 'created_at']);
    $table->index(['setor_atendente_id', 'status']);
    $table->index(['usuario_id', 'created_at']);
});

Schema::table('chamado_mensagens', function (Blueprint $table) {
    $table->index(['chamado_id', 'created_at']);
});

// Cache Service
class CacheService {
    public function getChamadosUsuario($userId) {
        return Cache::remember("user_chamados_{$userId}", 300, function () use ($userId) {
            return Chamado::with(['servico', 'setor'])
                ->where('usuario_id', $userId)
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();
        });
    }
    
    public function getPermissoesUsuario($userId) {
        return Cache::remember("user_permissions_{$userId}", 3600, function () use ($userId) {
            return User::find($userId)->getAllPermissions();
        });
    }
    
    public function invalidarCacheUsuario($userId) {
        Cache::forget("user_chamados_{$userId}");
        Cache::forget("user_permissions_{$userId}");
    }
}

// Query Builder para consultas complexas
class ChamadoRepository {
    public function getRelatorioComplexo($filtros) {
        return DB::table('chamados as c')
            ->join('servicos as s', 'c.servico_id', '=', 's.id')
            ->join('setores as st', 'c.setor_atendente_id', '=', 'st.id')
            ->select([
                'c.id', 'c.titulo', 'c.status', 'c.created_at',
                's.nome as servico_nome',
                'st.nome as setor_nome',
                DB::raw('EXTRACT(EPOCH FROM (c.updated_at - c.created_at))/3600 as tempo_atendimento_horas')
            ])
            ->whereBetween('c.created_at', [$filtros['data_inicio'], $filtros['data_fim']])
            ->when($filtros['setor_id'], fn($q) => $q->where('c.setor_atendente_id', $filtros['setor_id']))
            ->orderBy('c.created_at', 'desc')
            ->get();
    }
}
```

# Test Strategy:
Testes de performance com dados de volume, verificar hit rate do cache, validar otimização de queries com EXPLAIN, testes de invalidação de cache

# Subtasks:
## 1. Análise de Queries e Identificação de Gargalos [pending]
### Dependencies: None
### Description: Realizar análise detalhada das queries existentes utilizando ferramentas como EXPLAIN e DB::listen para identificar gargalos de performance e consultas lentas.
### Details:
Utilizar EXPLAIN nas principais queries, analisar logs de execução e identificar consultas que causam alto consumo de I/O e CPU. Documentar os principais pontos de lentidão encontrados.

## 2. Implementação de Índices Estratégicos [pending]
### Dependencies: 11.1
### Description: Criar índices compostos e individuais nas tabelas de maior volume, conforme análise dos gargalos, para otimizar filtragem e ordenação.
### Details:
Adicionar índices nas colunas usadas em WHERE, ORDER BY e JOIN, conforme identificado na etapa anterior. Validar a criação dos índices via migrations.

## 3. Implementação do Sistema de Cache [pending]
### Dependencies: 11.1, 11.2
### Description: Desenvolver e integrar o sistema de cache para consultas frequentes e dados de usuários, utilizando Cache::remember e/ou pacotes especializados.
### Details:
Implementar cache para resultados de consultas e permissões de usuário, configurando tempo de expiração e prefixos. Avaliar uso de pacotes como Query From Cache para simplificar o processo.

## 4. Testes de Performance e Hit Rate [pending]
### Dependencies: 11.2, 11.3
### Description: Executar testes de performance para validar o impacto das otimizações e medir o hit rate do cache.
### Details:
Utilizar dados de volume para testar tempo de resposta das queries antes e depois das otimizações. Medir o hit rate do cache e identificar possíveis pontos de melhoria.

## 5. Invalidação e Atualização de Cache [pending]
### Dependencies: 11.3, 11.4
### Description: Implementar mecanismos para invalidação e atualização do cache sempre que houver alterações relevantes nos dados.
### Details:
Desenvolver métodos para invalidar o cache de usuários e permissões após alterações, garantindo que os dados estejam sempre atualizados e evitando inconsistências.

## 6. Documentação das Otimizações [pending]
### Dependencies: 11.1, 11.2, 11.3, 11.4, 11.5
### Description: Documentar todas as otimizações realizadas, incluindo decisões técnicas, configurações de índices e estratégias de cache.
### Details:
Produzir documentação técnica detalhada sobre as análises, índices criados, configuração do cache, testes realizados e resultados obtidos, facilitando futuras manutenções.

