# Task ID: 12
# Title: Configurar Segurança, CORS e Deploy com Docker/Nginx
# Status: pending
# Dependencies: 11
# Priority: medium
# Description: Implementar configurações de segurança, CORS restrito por ambiente e setup de deploy com Docker e Nginx
# Details:
Configurar segurança completa e deploy:

```php
// config/cors.php
return [
    'paths' => ['api/*', 'sanctum/csrf-cookie'],
    'allowed_methods' => ['*'],
    'allowed_origins' => env('APP_ENV') === 'production' 
        ? [env('APP_URL')] 
        : ['http://localhost:3000', 'http://127.0.0.1:8000'],
    'allowed_origins_patterns' => [],
    'allowed_headers' => ['*'],
    'exposed_headers' => [],
    'max_age' => 0,
    'supports_credentials' => true,
];
```

```nginx
# .docker/nginx/production.conf
server {
    listen 80;
    server_name central-atendimento.edu.br;
    root /var/www/html/public;
    index index.php;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:" always;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass app:9000;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

```dockerfile
# Dockerfile
FROM php:8.3-fpm

RUN apt-get update && apt-get install -y \
    libpq-dev \
    libzip-dev \
    unzip \
    && docker-php-ext-install pdo pdo_pgsql zip

COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

WORKDIR /var/www/html
COPY . .

RUN composer install --no-dev --optimize-autoloader
RUN php artisan config:cache
RUN php artisan route:cache
RUN php artisan view:cache

CMD ["php-fpm"]
```

# Test Strategy:
Testes de headers de segurança, validação de CORS por ambiente, testes de deploy em containers, verificar CSP e performance de assets estáticos

# Subtasks:
## 1. Configurar CORS restrito por ambiente [pending]
### Dependencies: None
### Description: Implementar a configuração do arquivo cors.php para permitir origens específicas conforme o ambiente (produção ou desenvolvimento), garantindo que apenas domínios autorizados possam acessar a API.
### Details:
Ajustar o array 'allowed_origins' para alternar entre APP_URL em produção e localhost em desenvolvimento. Validar funcionamento em ambos ambientes.

## 2. Implementar headers de segurança no Nginx [pending]
### Dependencies: None
### Description: Adicionar e validar headers de segurança no arquivo de configuração do Nginx, como X-Frame-Options, X-Content-Type-Options, X-XSS-Protection, Referrer-Policy e Content-Security-Policy.
### Details:
Editar o arquivo .docker/nginx/production.conf para incluir os headers recomendados e garantir que estejam ativos em todas as respostas do servidor.

## 3. Criar Dockerfile e setup dos containers [pending]
### Dependencies: None
### Description: Desenvolver o Dockerfile para PHP-FPM, configurar dependências, copiar arquivos do projeto e preparar o ambiente para rodar em containers junto ao Nginx.
### Details:
Montar o Dockerfile conforme especificação, instalar extensões necessárias, copiar composer e rodar comandos de cache. Integrar com container Nginx via docker-compose.

## 4. Realizar testes de deploy em diferentes ambientes [pending]
### Dependencies: 12.1, 12.2, 12.3
### Description: Executar testes de deploy nos ambientes de desenvolvimento e produção, validando funcionamento dos containers, acesso à aplicação e restrições de CORS.
### Details:
Subir containers em ambos ambientes, testar endpoints protegidos, verificar resposta dos headers e funcionamento do CORS conforme configuração.

## 5. Validar Content-Security-Policy e performance de assets [pending]
### Dependencies: 12.2, 12.4
### Description: Testar a aplicação das regras de Content-Security-Policy e analisar o carregamento e cache dos assets estáticos para garantir segurança e performance.
### Details:
Utilizar ferramentas de análise de headers e performance (ex: Lighthouse, curl) para validar CSP, cache-control e impacto no carregamento dos assets.

## 6. Documentar o processo de deploy e configurações [pending]
### Dependencies: 12.1, 12.2, 12.3, 12.4, 12.5
### Description: Produzir documentação detalhada do processo de configuração de CORS, headers de segurança, setup dos containers e procedimentos de deploy.
### Details:
Registrar passos, comandos utilizados, arquivos de configuração e recomendações para manutenção futura, facilitando onboarding e auditoria.

