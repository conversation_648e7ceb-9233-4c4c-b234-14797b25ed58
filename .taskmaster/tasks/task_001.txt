# Task ID: 1
# Title: Configurar Autenticação Segura e Integração SGS
# Status: pending
# Dependencies: None
# Priority: high
# Description: Implementar autenticação segura com migração de SHA1 para Argon2 e integração com SGS para usuários, perfis e setores
# Details:
Migrar de SHA1 para Hash::check/Argon2 no AuthServiceProvider. Implementar App\Services\SGS\UsuariosServicos para conexão pgsql_sgs. Configurar middleware de autenticação customizado. Mapear perfis e setores do SGS para o sistema local. Implementar validação de credenciais segura:

```php
// AuthServiceProvider
if (Hash::check($password, $user->password)) {
    Auth::login($user);
}

// SGS Service
class UsuariosServicos {
    public function authenticate($credentials) {
        // Conectar via pgsql_sgs
        // Validar usuário e mapear perfis
    }
}
```

# Test Strategy:
Testes unitários para validação de hash, testes de integração para conexão SGS, testes de autenticação com credenciais válidas/inválidas, verificar mapeamento correto de perfis e setores

# Subtasks:
## 1. Análise e planejamento da migração de SHA1 para Argon2 [pending]
### Dependencies: None
### Description: Avaliar o cenário atual de armazenamento de senhas com SHA1, identificar impactos da migração e definir a estratégia para adoção do Argon2, considerando compatibilidade e segurança.
### Details:
Mapear onde SHA1 é utilizado, analisar riscos de migração, definir plano de atualização dos hashes e garantir compatibilidade temporária para usuários antigos.

## 2. Refatoração do AuthServiceProvider para uso de Argon2 [pending]
### Dependencies: 1.1
### Description: Modificar o AuthServiceProvider para utilizar Hash::check e Hash::make com Argon2, garantindo validação e atualização segura das senhas dos usuários.
### Details:
Implementar lógica para rehash automático de senhas SHA1 para Argon2 após login bem-sucedido, utilizando as opções recomendadas de Argon2.

## 3. Implementação do serviço de autenticação SGS [pending]
### Dependencies: 1.2
### Description: Desenvolver o App\Services\SGS\UsuariosServicos para autenticação e integração via conexão pgsql_sgs, validando usuários e obtendo perfis/setores.
### Details:
Criar métodos para autenticação segura, conexão com o banco SGS e obtenção dos dados necessários para o sistema local.

## 4. Configuração do middleware de autenticação customizado [pending]
### Dependencies: 1.3
### Description: Desenvolver e registrar middleware customizado para autenticação, integrando validação local e SGS, e aplicando políticas de segurança.
### Details:
Garantir que o middleware valide credenciais, trate sessões e erros de autenticação, e aplique controles de acesso conforme regras do sistema.

## 5. Mapeamento de perfis e setores do SGS para o sistema local [pending]
### Dependencies: 1.3
### Description: Implementar lógica para mapear e sincronizar perfis e setores obtidos do SGS com as entidades locais, garantindo consistência e integridade.
### Details:
Definir regras de correspondência, atualizar entidades locais e tratar casos de perfis/setores inexistentes ou divergentes.

## 6. Implementação de testes unitários e de integração [pending]
### Dependencies: 1.2, 1.3, 1.4, 1.5
### Description: Desenvolver testes automatizados para validação de hash, autenticação, integração SGS e mapeamento de perfis/setores.
### Details:
Cobrir cenários de sucesso e falha, testar migração de hash, autenticação via SGS, e garantir cobertura dos fluxos críticos de segurança.

## 7. Documentação e validação de segurança [pending]
### Dependencies: 1.6
### Description: Documentar o processo de migração, integração e autenticação, além de validar requisitos de segurança e conformidade.
### Details:
Elaborar documentação técnica, registrar decisões de segurança, validar contra requisitos de compliance e revisar possíveis vulnerabilidades.

