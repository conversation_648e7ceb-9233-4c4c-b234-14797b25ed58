# Task ID: 13
# Title: Checklist: Correções imediatas
# Status: pending
# Dependencies: None
# Priority: high
# Description: Ajustes urgentes de navegação, Docker e limpeza de arquivos residuais conforme docs/alerts.md e docs/checklist.md.
# Details:


# Test Strategy:


# Subtasks:
## 1. Ajustar rota Home na navegação [pending]
### Dependencies: None
### Description: Atualizar link para Home em `resources/views/layouts/navigation.blade.php` de `route('home')` para `route('home.index')`; validar em runtime.
### Details:


## 2. Corrigir caminho `cd` no `docker-entrypoint.sh` [pending]
### Dependencies: None
### Description: Alterar `cd /var/www/html/` para `cd /var/www`, alinhado ao `WORKDIR`; validar que o container sobe corretamente.
### Details:


## 3. Atualizar Dockerfile para não usar `composer update` em produção [pending]
### Dependencies: None
### Description: Substituir por `composer install --no-dev --prefer-dist --no-interaction --optimize-autoloader` utilizando `composer.lock`; validar no CI/build.
### Details:


## 4. Remover `public/hot` do repositório e ignorar via `.gitignore` [pending]
### Dependencies: None
### Description: Deletar `public/hot` e adicionar regra `/public/hot` ao `.gitignore`; garantir inexistência em produção.
### Details:


## 5. Remover arquivo residual `app/Providers/AuthServiceProvider.php.install` [pending]
### Dependencies: None
### Description: Excluir arquivo residual e verificar que não há referências a ele.
### Details:


