# Task ID: 17
# Title: Checklist: Documentação (versões e READMEs)
# Status: pending
# Dependencies: None
# Priority: low
# Description: Manter versões resolvidas registradas e atualizar README(s) quando padrões mudarem.
# Details:


# Test Strategy:


# Subtasks:
## 1. Registrar versões resolvidas (Composer/NPM) [pending]
### Dependencies: None
### Description: Gerar e anexar relatório de versões a `docs/readme-diff.md` ou similar; incluir lockfiles no controle.
### Details:


## 2. Atualizar READMEs quando padrões mudarem [pending]
### Dependencies: None
### Description: Revisar `README.md` e `docs/*` para refletir mudanças em build, assets, infra e padrões adotados.
### Details:


