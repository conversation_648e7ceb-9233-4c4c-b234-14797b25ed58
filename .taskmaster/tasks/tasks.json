{"master": {"tasks": [{"id": 1, "title": "Configurar Autenticação Segura e Integração SGS", "description": "Implementar autenticação segura com migração de SHA1 para Argon2 e integração com SGS para usuários, perfis e setores", "details": "Migrar de SHA1 para Hash::check/Argon2 no AuthServiceProvider. Implementar App\\Services\\SGS\\UsuariosServicos para conexão pgsql_sgs. Configurar middleware de autenticação customizado. Mapear perfis e setores do SGS para o sistema local. Implementar validação de credenciais segura:\n\n```php\n// AuthServiceProvider\nif (Hash::check($password, $user->password)) {\n    Auth::login($user);\n}\n\n// SGS Service\nclass UsuariosServicos {\n    public function authenticate($credentials) {\n        // Conectar via pgsql_sgs\n        // Validar usuário e mapear perfis\n    }\n}\n```", "testStrategy": "Testes unitários para validação de hash, testes de integração para conexão SGS, testes de autenticação com credenciais válidas/inválidas, verificar mapeamento correto de perfis e setores", "priority": "high", "dependencies": [], "status": "pending", "subtasks": [{"id": 1, "title": "Análise e planejamento da migração de SHA1 para Argon2", "description": "Avaliar o cenário atual de armazenamento de senhas com SHA1, identificar impactos da migração e definir a estratégia para adoção do Argon2, considerando compatibilidade e segurança.", "dependencies": [], "details": "Mapear onde SHA1 é utilizado, analisar riscos de migração, definir plano de atualização dos hashes e garantir compatibilidade temporária para usuários antigos.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Refatoração do AuthServiceProvider para uso de Argon2", "description": "Modificar o AuthServiceProvider para utilizar Hash::check e Hash::make com Argon2, garantindo validação e atualização segura das senhas dos usuários.", "dependencies": ["1.1"], "details": "Implementar lógica para rehash automático de senhas SHA1 para Argon2 após login bem-sucedido, utilizando as opções recomendadas de Argon2.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Implementação do serviço de autenticação SGS", "description": "Desenvolver o App\\Services\\SGS\\UsuariosServicos para autenticação e integração via conexão pgsql_sgs, validando usuários e obtendo perfis/setores.", "dependencies": ["1.2"], "details": "Criar métodos para autenticação segura, conexão com o banco SGS e obtenção dos dados necessários para o sistema local.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Configuração do middleware de autenticação customizado", "description": "Desenvolver e registrar middleware customizado para autenticação, integrando validação local e SGS, e aplicando políticas de segurança.", "dependencies": ["1.3"], "details": "Garantir que o middleware valide credenciais, trate sessões e erros de autenticação, e aplique controles de acesso conforme regras do sistema.", "status": "pending", "testStrategy": ""}, {"id": 5, "title": "Mapeamento de perfis e setores do SGS para o sistema local", "description": "Implementar lógica para mapear e sincronizar perfis e setores obtidos do SGS com as entidades locais, garantindo consistência e integridade.", "dependencies": ["1.3"], "details": "Definir regras de correspondência, atualizar entidades locais e tratar casos de perfis/setores inexistentes ou divergentes.", "status": "pending", "testStrategy": ""}, {"id": 6, "title": "Implementação de testes unitários e de integração", "description": "Desenvolver testes automatizados para validação de hash, autenticação, integração SGS e mapeamento de perfis/setores.", "dependencies": ["1.2", "1.3", "1.4", "1.5"], "details": "Cobrir cenários de sucesso e falha, testar migração de hash, autenticação via SGS, e garantir cobertura dos fluxos críticos de segurança.", "status": "pending", "testStrategy": ""}, {"id": 7, "title": "Documentação e validação de segurança", "description": "Documentar o processo de migração, integração e autenticação, além de validar requisitos de segurança e conformidade.", "dependencies": ["1.6"], "details": "Elaborar documentação técnica, registrar decisões de segurança, validar contra requisitos de compliance e revisar possíveis vulnerabilidades.", "status": "pending", "testStrategy": ""}]}, {"id": 2, "title": "Implementar Sistema de Permissões com Gates Dinâmicos", "description": "Criar sistema de autorização baseado em Gates dinâmicos gerados a partir da tabela de permissões", "details": "Implementar Gates dinâmicos no AuthServiceProvider baseados na tabela 'permissoes'. Criar cache por usuário/perfil para otimização. Implementar middleware de autorização:\n\n```php\n// AuthServiceProvider\npublic function boot() {\n    $permissions = Permission::all();\n    foreach ($permissions as $permission) {\n        Gate::define($permission->name, function ($user) use ($permission) {\n            return $user->hasPermission($permission->name);\n        });\n    }\n}\n\n// Middleware\nif (!Gate::allows('manage-tickets')) {\n    abort(403);\n}\n```\n\nImplementar cache Redis/array para permissões por usuário com TTL de 1 hora.", "testStrategy": "Testes unitários para Gates, testes de autorização por perfil, verificar cache de permissões, testes de middleware com diferentes níveis de acesso", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": [{"id": 1, "title": "Modelagem e Implementação da Tabela de Permissões", "description": "Criar a tabela 'permissoes' no banco de dados, definir o modelo Permission e relacionamentos necessários com usuários e/ou perfis.", "dependencies": [], "details": "Definir campos essenciais (id, nome, descrição, timestamps), criar migrations e seeders para permissões iniciais. Garantir integridade referencial e estrutura flexível para futuras permissões.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Implementação dos Gates Dinâmicos no AuthServiceProvider", "description": "Configurar o AuthServiceProvider para registrar dinamicamente os Gates a partir dos registros da tabela 'permissoes'.", "dependencies": ["2.1"], "details": "No método boot, buscar todas as permissões e criar um Gate para cada uma, utilizando closures que consultam se o usuário possui a permissão correspondente.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Criação do Cache de Permissões por Usuário/Perfil", "description": "Implementar cache (Redis ou array) para armazenar permissões de cada usuário/perfil, com TTL de 1 hora.", "dependencies": ["2.2"], "details": "Desenvolver lógica para armazenar e recuperar permissões do cache, invalidando e atualizando quando necessário. Garantir fallback para consulta ao banco caso o cache expire.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Implementação do Middleware de Autorização", "description": "Desenvolver middleware customizado para verificar permissões via Gates antes de executar ações protegidas.", "dependencies": ["2.2", "2.3"], "details": "Criar middleware que utilize Gate::allows para checar permissões, abortando com 403 em caso de acesso negado. Integrar middleware às rotas e controladores relevantes.", "status": "pending", "testStrategy": ""}, {"id": 5, "title": "Testes Unitários e de Integração", "description": "Elaborar testes para validar o correto funcionamento dos Gates, cache de permissões e middleware de autorização.", "dependencies": ["2.4"], "details": "Cobrir cenários de permissões concedidas e negadas, cache hit/miss, e diferentes perfis de usuário. Garantir cobertura de código e simular situações de segurança.", "status": "pending", "testStrategy": ""}, {"id": 6, "title": "Documentação e Validação de Segurança", "description": "Documentar a arquitetura do sistema de permissões, uso dos Gates, cache e middleware. Realizar revisão de segurança.", "dependencies": ["2.5"], "details": "Produzir documentação técnica e de uso, incluindo exemplos de integração. Realizar checklist de segurança para evitar brechas e validar a robustez do sistema.", "status": "pending", "testStrategy": ""}]}, {"id": 3, "title": "Desenvolver Models e Migrations do Sistema de Chamados", "description": "Criar estrutura de dados completa para chamados, histórico, mensagens, tombos e atribuições", "details": "Implementar Models Eloquent e migrations para:\n- <PERSON><PERSON><PERSON> (status, prioridade, setor_atendente_id, usuario_id)\n- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (versionamento de estados)\n- ChamadoMensagem (chat com suporte a imagens)\n- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (registro de bens/equipamentos)\n- At<PERSON><PERSON><PERSON><PERSON> (controle de responsáveis)\n\n```php\n// Migration exemplo\nSchema::create('chamados', function (Blueprint $table) {\n    $table->id();\n    $table->string('titulo');\n    $table->text('descricao');\n    $table->enum('status', ['aberto', 'em_atendimento', 'fechado']);\n    $table->enum('prioridade', ['baixa', 'media', 'alta']);\n    $table->foreignId('usuario_id')->constrained();\n    $table->foreignId('setor_atendente_id')->nullable();\n    $table->timestamps();\n});\n```\n\nImplementar relacionamentos Eloquent e scopes para consultas otimizadas.", "testStrategy": "Testes de factory para models, validação de relacionamentos, testes de constraints de banco, verificar integridade referencial", "priority": "high", "dependencies": [2], "status": "pending", "subtasks": [{"id": 1, "title": "Modelagem do Banco de Dados", "description": "Definir as entidades, atributos e relacionamentos necessários para Chamado, ChamadoHistorico, ChamadoMensagem, ChamadoTombo e Atribuicao.", "dependencies": [], "details": "Elaborar o diagrama entidade-relacionamento (DER) e especificar os campos, tipos de dados, chaves primárias e estrangeiras para cada tabela.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Implementação das Migrations", "description": "<PERSON><PERSON><PERSON> as migrations para todas as tabelas definidas na modelagem, incluindo constraints e índices.", "dependencies": ["3.1"], "details": "Utilizar o Schema Builder do Laravel para implementar as migrations, garantindo integridade referencial e performance.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Criação dos Models Eloquent", "description": "Desenvolver os models Eloquent para cada entidade, se<PERSON><PERSON> as convenções de nomenclatura e estrutura do Laravel.", "dependencies": ["3.2"], "details": "Implementar as classes de modelo, definir os atributos fillable, casts e eventuais mutators/acessors necessários.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Implementação dos Relacionamentos e Scopes", "description": "Configurar os relacionamentos entre os models e criar scopes para consultas otimizadas.", "dependencies": ["3.3"], "details": "Definir métodos de relacionamento (hasMany, belongsTo, etc.) e implementar scopes locais para filtros frequentes.", "status": "pending", "testStrategy": ""}, {"id": 5, "title": "Testes de Integridade e Constraints", "description": "Validar a integridade das migrations, models e relacionamentos, incluindo constraints de banco e factories.", "dependencies": ["3.4"], "details": "Executar testes automatizados para garantir que as constraints, relacionamentos e factories estejam funcionando corretamente.", "status": "pending", "testStrategy": ""}]}, {"id": 4, "title": "Implementar CRUD de Catálogo de Serviços com Público-Alvo", "description": "Desenvolver sistema de gestão de serviços com categorização por público-alvo e filtros dinâmicos", "details": "C<PERSON><PERSON><PERSON>roller, Service e Repository para CRUD completo. Implementar categorização por público-alvo (aluno, servidor, técnico, docente, visitante). Adicionar filtros dinâmicos no login:\n\n```php\n// ServicoController\nclass ServicoController extends Controller {\n    public function index(Request $request) {\n        $categoria = $request->user()->categoria;\n        return $this->servicoService->getByCategoria($categoria);\n    }\n    \n    public function store(ServicoRequest $request) {\n        Gate::authorize('manage-services');\n        return $this->servicoService->create($request->validated());\n    }\n}\n\n// Model\nclass Servico extends Model {\n    protected $fillable = ['nome', 'descricao', 'categoria_publico', 'criticidade', 'status'];\n    \n    public function scopeByCategoria($query, $categoria) {\n        return $query->whereJsonContains('categoria_publico', $categoria);\n    }\n}\n```", "testStrategy": "Testes CRUD completos, validação de filtros por categoria, testes de autorização para criação/edição, verificar exibição correta no login", "priority": "medium", "dependencies": [2, 3], "status": "pending", "subtasks": [{"id": 1, "title": "Implementar o ServicoController com métodos CRUD", "description": "Criar o controlador responsável pelas operações de criação, leitura, atualização e exclusão dos serviços, seguindo o padrão resource do Laravel.", "dependencies": [], "details": "Utilizar o comando artisan para gerar o controller resource. Implementar métodos index, store, show, update e destroy, garantindo integração com o Service e aplicação das regras de autorização.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Desenvolver Service e Repository para Serviços", "description": "<PERSON><PERSON><PERSON> as camadas de Service e Repository para encapsular a lógica de negócio e acesso a dados do catálogo de serviços.", "dependencies": ["4.1"], "details": "Implementar métodos para manipulação dos dados (CRUD) no Service e Repository, garantindo separação de responsabilidades e facilitando testes.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Implementar categorização por público-alvo", "description": "Adicionar suporte à categorização dos serviços por público-alvo (aluno, servidor, técnico, docente, visitante) no modelo e nas operações CRUD.", "dependencies": ["4.2"], "details": "Ajustar o model Servico para suportar múltiplas categorias de público. Implementar métodos e validações para garantir o correto armazenamento e recuperação dessa informação.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Adicionar filtros dinâmicos por categoria no login", "description": "Implementar lógica para filtrar os serviços exibidos ao usuário com base na categoria do público-alvo durante o login.", "dependencies": ["4.3"], "details": "No método index do ServicoController, aplicar filtro dinâmico utilizando o escopo byCategoria, retornando apenas os serviços relevantes para o usuário logado.", "status": "pending", "testStrategy": ""}, {"id": 5, "title": "Realizar testes e validação de autorização", "description": "Testar todas as operações CRUD, filtros por categoria e regras de autorização para garantir o correto funcionamento e segurança do sistema.", "dependencies": ["4.4"], "details": "Executar testes manuais e/ou automatizados para validar as operações, filtros dinâmicos e autorização (Gate) para criação e edição de serviços.", "status": "pending", "testStrategy": ""}]}, {"id": 5, "title": "Desenvolver Fluxo Completo de Chamados (Abertura ao Fechamento)", "description": "Implementar ciclo completo de chamados: abertura, pré-visualização, atendimento, chat com imagens e fechamento", "details": "Implementar Controllers e Services para fluxo completo:\n\n```php\n// ChamadoController\npublic function store(ChamadoRequest $request) {\n    $chamado = $this->chamadoService->create([\n        'titulo' => $request->titulo,\n        'descricao' => $request->descricao,\n        'usuario_id' => auth()->id(),\n        'servico_id' => $request->servico_id,\n        'status' => 'aberto'\n    ]);\n    \n    $this->historicoService->registrar($chamado, 'Chamado criado');\n    return redirect()->route('chamado.preview', $chamado);\n}\n\npublic function atender(Chamado $chamado) {\n    Gate::authorize('attend-tickets');\n    $this->chamadoService->atribuir($chamado, auth()->user());\n    return redirect()->route('chamado.show', $chamado);\n}\n\npublic function adicionarMensagem(Request $request, Chamado $chamado) {\n    $mensagem = $this->mensagemService->create([\n        'chamado_id' => $chamado->id,\n        'usuario_id' => auth()->id(),\n        'conteudo' => $request->conteudo,\n        'imagem' => $request->file('imagem')?->store('mensagens', 'public')\n    ]);\n    \n    return response()->json($mensagem);\n}\n```\n\nImplementar upload de imagens com validação e storage público.", "testStrategy": "Testes de fluxo completo E2E, validação de upload de imagens, testes de autorização por etapa, verificar histórico de mudanças de status", "priority": "high", "dependencies": [3, 4], "status": "pending", "subtasks": [{"id": 1, "title": "Implementação do Fluxo de Abertura de Chamados", "description": "Desenvolver o processo de abertura de chamados, incluindo validação dos dados, registro inicial e integração com o histórico.", "dependencies": [], "details": "Criar métodos no Controller e Service para receber requisições, validar dados obrigatórios e registrar o chamado com status 'aberto'.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Desenvolver Pré-visualização de Chamados", "description": "Implementar funcionalidade para pré-visualizar detalhes do chamado recém-criado antes do atendimento.", "dependencies": ["5.1"], "details": "Criar rota e view para exibir informações do chamado, permitindo revisão pelo usuário antes do encaminhamento.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Implementar Atendimento e Atribuição de Chamados", "description": "Desenvolver lógica para atribuir chamados a atendentes autorizados e registrar a ação no histórico.", "dependencies": ["5.2"], "details": "Adicionar autorização via Gate, atribuir usuário responsável e atualizar status do chamado para 'em atendimento'.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Desenvolver Chat com Upload de Imagens", "description": "Implementar chat interno no chamado, permitindo envio de mensagens e imagens com validação e armazenamento seguro.", "dependencies": ["5.3"], "details": "<PERSON><PERSON><PERSON> endpoints para envio de mensagens, validar e armazenar imagens em storage público, garantir integridade dos dados.", "status": "pending", "testStrategy": ""}, {"id": 5, "title": "Implementar Fechamento do Chamado", "description": "Desenvolver processo para finalizar o chamado, atualizar status e registrar motivo do fechamento.", "dependencies": ["5.4"], "details": "Adicionar mé<PERSON> para fechar chamado, registrar ação no histórico e notificar envolvidos.", "status": "pending", "testStrategy": ""}, {"id": 6, "title": "Registrar <PERSON><PERSON><PERSON><PERSON><PERSON> Mu<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> que todas as ações relevantes no ciclo do chamado sejam registradas no histórico para auditoria.", "dependencies": ["5.1", "5.3", "5.4", "5.5"], "details": "Integrar serviço de histórico em cada etapa do fluxo, detalhando mudanças de status, mensagens e atribuições.", "status": "pending", "testStrategy": ""}, {"id": 7, "title": "Realizar Testes End-to-End do Fluxo Completo", "description": "Desenvolver e executar testes E2E para validar o funcionamento do ciclo completo de chamados, incluindo upload de imagens e autorizações.", "dependencies": ["5.6"], "details": "Criar cen<PERSON><PERSON><PERSON> de teste cobrindo abertura, pré-visualização, atendimento, chat, fechamento e histórico.", "status": "pending", "testStrategy": ""}, {"id": 8, "title": "Documentar o Fluxo Completo de Chamados", "description": "Produzir documentação técnica detalhada do fluxo, endpoints, regras de negócio e exemplos de uso.", "dependencies": ["5.7"], "details": "Elaborar documentação para desenvolvedores e usuários, incluindo diagramas, exemplos de requisições e respostas.", "status": "pending", "testStrategy": ""}]}, {"id": 6, "title": "Implementar Sistema de Regulação Manual", "description": "Desenvolver interface e lógica para regulação manual de chamados por unidade/setor/atendente", "details": "Criar RegulacaoController e Service para gerenciamento de fila de regulação:\n\n```php\n// RegulacaoController\nclass RegulacaoController extends Controller {\n    public function index() {\n        Gate::authorize('regulate-tickets');\n        $chamados = $this->regulacaoService->getFilaRegulacao();\n        return view('regulacao.index', compact('chamados'));\n    }\n    \n    public function encaminhar(Request $request, Chamado $chamado) {\n        $this->regulacaoService->encaminhar($chamado, [\n            'setor_destino_id' => $request->setor_id,\n            'atendente_id' => $request->atendente_id,\n            'observacoes' => $request->observacoes\n        ]);\n        \n        $this->historicoService->registrar($chamado, 'Encaminhado para ' . $request->setor_nome);\n        return redirect()->back()->with('success', 'Chamado encaminhado com sucesso');\n    }\n}\n\n// Service\nclass RegulacaoService {\n    public function encaminhar(Chamado $chamado, array $dados) {\n        $chamado->update([\n            'setor_atendente_id' => $dados['setor_destino_id'],\n            'status' => 'regulado'\n        ]);\n        \n        Atribuicao::create([\n            'chamado_id' => $chamado->id,\n            'usuario_id' => $dados['atendente_id'],\n            'tipo' => 'regulacao'\n        ]);\n    }\n}\n```", "testStrategy": "Testes de regulação por diferentes critérios, validação de autorização para reguladores, testes de atribuição correta, verificar registro no histórico", "priority": "medium", "dependencies": [5], "status": "pending", "subtasks": [{"id": 1, "title": "Implementar RegulacaoController", "description": "Criar o controller responsável pelas rotas de regulação manual, incluindo métodos para exibir a fila de chamados e encaminhar chamados para setores/atendentes.", "dependencies": [], "details": "O controller deve conter métodos como index() para listar chamados e encaminhar() para processar o encaminhamento, utilizando autorização adequada.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Desenvolver RegulacaoService", "description": "Implementar o serviço que centraliza a lógica de negócios da regulação, incluindo atualização de chamados e criação de atribuições.", "dependencies": ["6.1"], "details": "O service deve ter métodos para encaminhar chamados, atualizar status e criar registros de atribuição, garantindo separação da lógica do controller.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Implementar Lógica de Fila e Encaminhamento", "description": "Desenvolver a lógica para obtenção da fila de regulação e regras de encaminhamento de chamados para setores e atendentes.", "dependencies": ["6.2"], "details": "Incluir métodos para buscar chamados pendentes de regulação, aplicar filtros por unidade/setor/atendente e garantir integridade dos dados ao encaminhar.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Testar Autorização e Atribuição", "description": "C<PERSON>r testes para garantir que apenas usuários autorizados possam regular chamados e que a atribuição de chamados seja feita corretamente.", "dependencies": ["6.3"], "details": "Testar permissões via Gates, simular diferentes perfis de usuário e validar que a atribuição de chamados ocorre conforme esperado.", "status": "pending", "testStrategy": ""}, {"id": 5, "title": "Registrar e Validar Histórico de Regulação", "description": "Implementar e testar o registro de ações de regulação no histórico dos chamados, garantindo rastreabilidade e validação dos dados registrados.", "dependencies": ["6.4"], "details": "Registrar cada encaminhamento no histórico, validar informações como setor, atendente e observações, e garantir que o histórico seja consultável.", "status": "pending", "testStrategy": ""}]}, {"id": 7, "title": "Desenvolver Regulação Automática SIC/SRCA por Campus", "description": "Implementar regras automáticas de encaminhamento para SIC de cada campus baseado em unidade/serviço", "details": "Criar sistema de regras automáticas para os 7 campus (Petrolina, Ciências Agrárias, Juazeiro, Paulo <PERSON>, <PERSON><PERSON>, São Raimundo Nonato, Salgueiro):\n\n```php\n// RegulacaoAutomaticaService\nclass RegulacaoAutomaticaService {\n    private $regrasSIC = [\n        'petrolina' => ['servicos' => ['informacao', 'transparencia'], 'setor_id' => 1],\n        'juazeiro' => ['servicos' => ['informacao', 'transparencia'], 'setor_id' => 2],\n        // ... outros campus\n    ];\n    \n    public function aplicarRegrasAutomaticas(Chamado $chamado) {\n        $campus = $this->getCampusFromUser($chamado->usuario);\n        $servico = $chamado->servico;\n        \n        if ($this->isSICService($servico, $campus)) {\n            $this->encaminharParaSIC($chamado, $campus);\n        } elseif ($this->isSRCAService($servico, $campus)) {\n            $this->encaminharParaSRCA($chamado, $campus);\n        }\n    }\n    \n    private function encaminharParaSIC(Chamado $chamado, string $campus) {\n        $setorSIC = $this->regrasSIC[$campus]['setor_id'];\n        $chamado->update(['setor_atendente_id' => $setorSIC, 'status' => 'regulado_automaticamente']);\n        \n        $this->historicoService->registrar($chamado, \"Encaminhado automaticamente para SIC - {$campus}\");\n    }\n}\n\n// Event Listener\nclass ChamadoCriado {\n    public function handle(ChamadoCriadoEvent $event) {\n        $this->regulacaoAutomaticaService->aplicarRegrasAutomaticas($event->chamado);\n    }\n}\n```", "testStrategy": "Testes para cada campus e tipo de serviço, validação de regras SIC/SRCA, verificar auditoria no histórico, testes de fallback para regulação manual", "priority": "medium", "dependencies": [6], "status": "pending", "subtasks": [{"id": 1, "title": "Levantamento e Definição das Regras por Campus", "description": "Mapear e documentar as regras de encaminhamento SIC/SRCA para cada campus, considerando unidades, serviços e setores responsáveis.", "dependencies": [], "details": "Consultar gestores de cada campus, levantar serviços atendidos por SIC/SRCA, definir critérios de encaminhamento e validar regras com as áreas envolvidas.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Implementação do RegulacaoAutomaticaService", "description": "Desenvolver o serviço responsável por aplicar as regras automáticas de encaminhamento para SIC/SRCA conforme definido no levantamento.", "dependencies": ["7.1"], "details": "Codificar lógica de decisão, parametrizar regras por campus, implementar métodos de encaminhamento e integração com histórico.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Integração com Eventos do Sistema", "description": "Integrar o RegulacaoAutomaticaService aos eventos de criação e atualização de chamados para garantir o acionamento automático das regras.", "dependencies": ["7.2"], "details": "Configurar listeners para eventos relevantes, garantir execução automática do serviço ao criar/atualizar chamados.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Testes para Cada Campus e Serviço", "description": "Elaborar e executar testes abrangentes para validar o encaminhamento automático em todos os campus e tipos de serviço.", "dependencies": ["7.3"], "details": "Criar cenários de teste para cada campus, simular diferentes serviços, validar resultados e registrar evidências de conformidade.", "status": "pending", "testStrategy": ""}, {"id": 5, "title": "Implementação de Fallback para Regulação Manual", "description": "Desenvolver mecanismo de fallback para permitir regulação manual caso as regras automáticas não sejam aplicáveis ou falhem.", "dependencies": ["7.4"], "details": "Adicionar lógica de exceção, interface para intervenção manual e registro de justificativas no histórico do chamado.", "status": "pending", "testStrategy": ""}, {"id": 6, "title": "Auditoria e Registro no Histórico", "description": "G<PERSON><PERSON>r que todas as ações automáticas e manuais de regulação sejam auditadas e registradas no histórico do chamado.", "dependencies": ["7.5"], "details": "Implementar logs detalhados, registrar decisões automáticas e manuais, garantir rastreabilidade e conformidade com requisitos de auditoria.", "status": "pending", "testStrategy": ""}]}, {"id": 8, "title": "Implementar Interface de Chat com Upload de Imagens", "description": "Desenvolver sistema de mensagens em tempo real com suporte a imagens entre solicitantes e atendentes", "details": "Criar interface de chat responsiva com upload de imagens:\n\n```php\n// MensagemController\nclass MensagemController extends Controller {\n    public function store(Request $request, Chamado $chamado) {\n        $request->validate([\n            'conteudo' => 'required_without:imagem|string|max:1000',\n            'imagem' => 'nullable|image|max:2048'\n        ]);\n        \n        $mensagem = ChamadoMensagem::create([\n            'chamado_id' => $chamado->id,\n            'usuario_id' => auth()->id(),\n            'conteudo' => $request->conteudo,\n            'imagem_path' => $request->file('imagem')?->store('mensagens', 'public'),\n            'tipo' => auth()->user()->isAtendente($chamado) ? 'atendente' : 'solicitante'\n        ]);\n        \n        return response()->json([\n            'mensagem' => $mensagem->load('usuario'),\n            'html' => view('components.mensagens.usuario', compact('mensagem'))->render()\n        ]);\n    }\n}\n```\n\n```javascript\n// resources/js/batepapo.js\nclass ChatManager {\n    constructor(chamadoId) {\n        this.chamadoId = chamadoId;\n        this.initializeEventListeners();\n        this.loadMessages();\n    }\n    \n    sendMessage(formData) {\n        fetch(`/chamado/${this.chamadoId}/mensagem`, {\n            method: 'POST',\n            body: formData,\n            headers: {\n                'X-CSRF-TOKEN': document.querySelector('meta[name=\"csrf-token\"]').content\n            }\n        })\n        .then(response => response.json())\n        .then(data => this.appendMessage(data.html));\n    }\n}\n```", "testStrategy": "Testes de upload de imagem com validação de tamanho/tipo, testes de autorização para envio de mensagens, verificar renderização correta do chat, testes de responsividade mobile", "priority": "medium", "dependencies": [5], "status": "pending", "subtasks": [{"id": 1, "title": "Implementação do Backend de Mensagens em Tempo Real", "description": "Desenvolver a lógica de envio, recebimento e armazenamento de mensagens entre solicitantes e atendentes, utilizando eventos e filas para garantir comunicação em tempo real.", "dependencies": [], "details": "Utilizar Laravel Broadcasting e WebSockets para disparar eventos de novas mensagens e garantir atualização instantânea do chat para todos os participantes.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Implementação de Upload e Validação de Imagens", "description": "Adicionar suporte ao upload de imagens nas mensagens, incluindo validação de tipo e tamanho conforme requisitos do sistema.", "dependencies": ["8.1"], "details": "Configurar validação no backend para arquivos de imagem, armazenar imagens de forma segura e associar ao registro da mensagem.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Criação da Interface Responsiva do Chat", "description": "Desenvolver o frontend do chat com layout responsivo, garan<PERSON>do boa experiência em dispositivos móveis e desktop.", "dependencies": ["8.1", "8.2"], "details": "Utilizar frameworks modernos (ex: Vue.js, Tailwind) para criar componentes de chat, área de mensagens e upload de imagens.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Integração Frontend-Backend para Mensagens e Imagens", "description": "Conectar a interface do chat ao backend, permitindo envio e recebimento de mensagens e imagens em tempo real.", "dependencies": ["8.3"], "details": "Implementar chamadas AJAX/fetch, escutar eventos de WebSocket e atualizar a interface conforme novas mensagens ou imagens forem recebidas.", "status": "pending", "testStrategy": ""}, {"id": 5, "title": "Testes de Autorização e Responsividade", "description": "Realizar testes para garantir que apenas usuários autorizados possam enviar mensagens e imagens, além de validar o funcionamento em diferentes dispositivos.", "dependencies": ["8.4"], "details": "Testar regras de autorização, upload de imagens, renderização do chat em mobile e desktop, e atualização em tempo real.", "status": "pending", "testStrategy": ""}, {"id": 6, "title": "Documentação de Uso do Sistema de Chat", "description": "Produzir documentação clara sobre o funcionamento do chat, incluindo instruções de uso, limitações e exemplos de integração.", "dependencies": ["8.5"], "details": "Descrever fluxo de mensagens, upload de imagens, requisitos técnicos e orientações para usuários finais e desenvolvedores.", "status": "pending", "testStrategy": ""}]}, {"id": 9, "title": "Desenvolver Sistema de Tombos e Histórico Completo", "description": "Implementar registro de tombos de equipamentos e sistema completo de auditoria/histórico de chamados", "details": "Criar sistema de tombos para registro de equipamentos e histórico completo:\n\n```php\n// ChamadoTomboController\nclass ChamadoTomboController extends Controller {\n    public function store(Request $request, Chamado $chamado) {\n        Gate::authorize('manage-tombos');\n        \n        $request->validate([\n            'numero_tombo' => 'required|string|max:50',\n            'descricao_equipamento' => 'required|string|max:255',\n            'observacoes' => 'nullable|string|max:500'\n        ]);\n        \n        $tombo = ChamadoTombo::create([\n            'chamado_id' => $chamado->id,\n            'numero_tombo' => $request->numero_tombo,\n            'descricao_equipamento' => $request->descricao_equipamento,\n            'observacoes' => $request->observacoes,\n            'usuario_id' => auth()->id()\n        ]);\n        \n        $this->historicoService->registrar($chamado, \"Tombo adicionado: {$request->numero_tombo}\");\n        \n        return redirect()->back()->with('success', 'Tombo registrado com sucesso');\n    }\n}\n\n// HistoricoService\nclass HistoricoService {\n    public function registrar(Chamado $chamado, string $acao, array $dados_anteriores = null) {\n        ChamadoHistorico::create([\n            'chamado_id' => $chamado->id,\n            'usuario_id' => auth()->id(),\n            'acao' => $acao,\n            'dados_anteriores' => $dados_anteriores ? json_encode($dados_anteriores) : null,\n            'dados_atuais' => json_encode($chamado->toArray()),\n            'ip_address' => request()->ip(),\n            'user_agent' => request()->userAgent()\n        ]);\n    }\n}\n```", "testStrategy": "Testes CRUD para tombos, validação de autorização, verificar registro correto no histórico, testes de auditoria completa de mudanças", "priority": "medium", "dependencies": [5], "status": "pending", "subtasks": [{"id": 1, "title": "Implementação do ChamadoTomboController", "description": "Desenvolver e configurar o controller responsável pelo registro de tombos vinculados aos chamados, incluindo validação de dados e integração com sistema de autorização.", "dependencies": [], "details": "Implementar métodos de criação, edição e exclusão de tombos, garantir uso correto do Gate 'manage-tombos' e validação dos campos obrigatórios conforme regras de negócio.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Criação do Sistema de Tombos", "description": "Projetar e implementar a estrutura de dados e as interfaces necessárias para o cadastro, consulta e gerenciamento dos tombos de equipamentos.", "dependencies": ["9.1"], "details": "Criar migrations, models e views para registro de tombos, garantindo integridade referencial com chamados e usuários, além de interface amigável para consulta e edição.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Implementação do HistoricoService", "description": "Desenvolver o serviço responsável pelo registro detalhado de todas as ações realizadas nos chamados, incluindo alterações de tombos e demais eventos relevantes.", "dependencies": ["9.2"], "details": "Implementar lógica para registrar ações, armazenar dados anteriores e atuais, capturar IP e user agent, e garantir versionamento consistente do histórico.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Testes de Auditoria e Integridade", "description": "Elaborar e executar testes automatizados e manuais para validar a integridade dos registros de tombos e a consistência do histórico de auditoria.", "dependencies": ["9.3"], "details": "Realizar testes CRUD, simular cenários de alteração e exclusão, validar registros no histórico, testar permissões e garantir rastreabilidade completa das ações.", "status": "pending", "testStrategy": ""}, {"id": 5, "title": "Documentação e Validação de Segurança", "description": "Documentar o funcionamento do sistema de tombos e histórico, além de revisar e validar todos os controles de segurança e autorização implementados.", "dependencies": ["9.4"], "details": "Produzir documentação técnica e de usuário, revisar uso de Gates, validar proteção contra acessos indevidos e garantir conformidade com requisitos de auditoria.", "status": "pending", "testStrategy": ""}]}, {"id": 10, "title": "Implementar Relatórios PDF e Painéis Gerenciais", "description": "Desenvolver sistema de relatórios em PDF e painéis gerenciais para chefes de unidade e administradores", "details": "Implementar relatórios com DomPDF e painéis com Chart.js:\n\n```php\n// RelatorioController\nclass RelatorioController extends Controller {\n    public function chamadosPDF(Request $request) {\n        Gate::authorize('view-reports');\n        \n        $filtros = $request->validate([\n            'data_inicio' => 'required|date',\n            'data_fim' => 'required|date|after_or_equal:data_inicio',\n            'setor_id' => 'nullable|exists:setores,id',\n            'status' => 'nullable|in:aberto,em_atendimento,fechado'\n        ]);\n        \n        $chamados = $this->relatorioService->getChamadosRelatorio($filtros);\n        \n        $pdf = PDF::loadView('relatorios.chamados', compact('chamados', 'filtros'));\n        return $pdf->download('relatorio-chamados.pdf');\n    }\n    \n    public function painelGerencial() {\n        Gate::authorize('view-dashboard');\n        \n        $dados = [\n            'chamados_por_status' => $this->dashboardService->getChamadosPorStatus(),\n            'chamados_por_setor' => $this->dashboardService->getChamadosPorSetor(),\n            'tempo_medio_atendimento' => $this->dashboardService->getTempoMedioAtendimento(),\n            'satisfacao_media' => $this->dashboardService->getSatisfacaoMedia()\n        ];\n        \n        return view('dashboard.gerencial', compact('dados'));\n    }\n}\n\n// DashboardService\nclass DashboardService {\n    public function getChamadosPorStatus() {\n        return Chamado::selectRaw('status, COUNT(*) as total')\n            ->groupBy('status')\n            ->pluck('total', 'status');\n    }\n}\n```\n\n```javascript\n// resources/js/graficos.js\nclass DashboardCharts {\n    initStatusChart(data) {\n        new Chart(document.getElementById('statusChart'), {\n            type: 'doughnut',\n            data: {\n                labels: Object.keys(data),\n                datasets: [{\n                    data: Object.values(data),\n                    backgroundColor: ['#28a745', '#ffc107', '#dc3545']\n                }]\n            }\n        });\n    }\n}\n```", "testStrategy": "Testes de geração de PDF com diferentes filtros, validação de autorização por perfil, testes de performance dos painéis, verificar responsividade dos gráficos", "priority": "medium", "dependencies": [5, 6, 7], "status": "pending", "subtasks": [{"id": 1, "title": "Implementação dos Relatórios PDF com DomPDF", "description": "Desenvolver a funcionalidade de geração de relatórios em PDF utilizando DomPDF, integrando templates Blade e dados dinâmicos.", "dependencies": [], "details": "Configurar o pacote DomPDF no Laravel, criar templates para os relatórios, implementar métodos para carregar dados e gerar PDFs conforme filtros selecionados.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Criação dos Painéis Gerenciais com Chart.js", "description": "Desenvolver painéis interativos para chefes de unidade e administradores utilizando Chart.js para visualização dos dados.", "dependencies": [], "details": "Implementar gráficos dinâmicos (ex: status, setor, tempo médio, satisfação) no frontend, integrando com os dados fornecidos pelo backend.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Implementação de Filtros e Validação de Dados", "description": "Desenvolver e validar filtros para geração de relatórios e visualização dos painéis, garantindo integridade dos dados recebidos.", "dependencies": [], "details": "Criar regras de validação para datas, setor e status; garantir que apenas dados válidos sejam processados nos relatórios e dashboards.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Testes de Performance e Responsividade", "description": "Realizar testes de performance na geração dos PDFs e nos painéis, além de validar a responsividade dos gráficos em diferentes dispositivos.", "dependencies": ["10.1", "10.2", "10.3"], "details": "Executar testes de carga, medir tempo de geração dos relatórios, validar exibição dos painéis em dispositivos móveis e desktops.", "status": "pending", "testStrategy": ""}, {"id": 5, "title": "Implementação de Autorização e Segurança", "description": "Garantir que apenas usuários autorizados possam acessar relatórios e painéis, utilizando Gates e políticas de acesso.", "dependencies": ["10.1", "10.2", "10.3"], "details": "Configurar Gates para permissões específicas, proteger rotas e métodos, validar perfis de acesso antes de exibir dados sensíveis.", "status": "pending", "testStrategy": ""}, {"id": 6, "title": "Documentação de Uso dos Relatórios e Painéis", "description": "Elaborar documentação clara para usuários finais e administradores sobre como utilizar os relatórios PDF e painéis gerenciais.", "dependencies": ["10.1", "10.2", "10.3", "10.4", "10.5"], "details": "Descrever processos de geração de relatórios, filtros disponíveis, visualização dos painéis, permissões necessárias e exemplos de uso.", "status": "pending", "testStrategy": ""}]}, {"id": 11, "title": "Implementar Otimizações de Performance e Cache", "description": "Aplicar otimizações de consultas, índices estratégicos e sistema de cache para melhorar performance", "details": "Implementar otimizações de performance e cache:\n\n```php\n// Índices estratégicos (migration)\nSchema::table('chamados', function (Blueprint $table) {\n    $table->index(['status', 'created_at']);\n    $table->index(['setor_atendente_id', 'status']);\n    $table->index(['usuario_id', 'created_at']);\n});\n\nSchema::table('chamado_mensagens', function (Blueprint $table) {\n    $table->index(['chamado_id', 'created_at']);\n});\n\n// Cache Service\nclass CacheService {\n    public function getChamadosUsuario($userId) {\n        return Cache::remember(\"user_chamados_{$userId}\", 300, function () use ($userId) {\n            return Chamado::with(['servico', 'setor'])\n                ->where('usuario_id', $userId)\n                ->orderBy('created_at', 'desc')\n                ->limit(10)\n                ->get();\n        });\n    }\n    \n    public function getPermissoesUsuario($userId) {\n        return Cache::remember(\"user_permissions_{$userId}\", 3600, function () use ($userId) {\n            return User::find($userId)->getAllPermissions();\n        });\n    }\n    \n    public function invalidarCacheUsuario($userId) {\n        Cache::forget(\"user_chamados_{$userId}\");\n        Cache::forget(\"user_permissions_{$userId}\");\n    }\n}\n\n// Query Builder para consultas complexas\nclass ChamadoRepository {\n    public function getRelatorioComplexo($filtros) {\n        return DB::table('chamados as c')\n            ->join('servicos as s', 'c.servico_id', '=', 's.id')\n            ->join('setores as st', 'c.setor_atendente_id', '=', 'st.id')\n            ->select([\n                'c.id', 'c.titulo', 'c.status', 'c.created_at',\n                's.nome as servico_nome',\n                'st.nome as setor_nome',\n                DB::raw('EXTRACT(EPOCH FROM (c.updated_at - c.created_at))/3600 as tempo_atendimento_horas')\n            ])\n            ->whereBetween('c.created_at', [$filtros['data_inicio'], $filtros['data_fim']])\n            ->when($filtros['setor_id'], fn($q) => $q->where('c.setor_atendente_id', $filtros['setor_id']))\n            ->orderBy('c.created_at', 'desc')\n            ->get();\n    }\n}\n```", "testStrategy": "Testes de performance com dados de volume, verificar hit rate do cache, validar otimização de queries com EXPLAIN, testes de invalidação de cache", "priority": "medium", "dependencies": [10], "status": "pending", "subtasks": [{"id": 1, "title": "Análise de Queries e Identificação de Gargalos", "description": "Realizar análise detalhada das queries existentes utilizando ferramentas como EXPLAIN e DB::listen para identificar gargalos de performance e consultas lentas.", "dependencies": [], "details": "Utilizar EXPLAIN nas principais queries, analisar logs de execução e identificar consultas que causam alto consumo de I/O e CPU. Documentar os principais pontos de lentidão encontrados.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Implementação de Índices Estratégicos", "description": "Criar índices compostos e individuais nas tabelas de maior volume, conforme análise dos gargalos, para otimizar filtragem e ordenação.", "dependencies": ["11.1"], "details": "Adicionar índices nas colunas usadas em WHERE, ORDER BY e JOIN, conforme identificado na etapa anterior. Validar a criação dos índices via migrations.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Implementação do Sistema de Cache", "description": "Desenvolver e integrar o sistema de cache para consultas frequentes e dados de usuários, utilizando Cache::remember e/ou pacotes especializados.", "dependencies": ["11.1", "11.2"], "details": "Implementar cache para resultados de consultas e permissões de usuário, configurando tempo de expiração e prefixos. Avaliar uso de pacotes como Query From Cache para simplificar o processo.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Testes de Performance e Hit Rate", "description": "Executar testes de performance para validar o impacto das otimizações e medir o hit rate do cache.", "dependencies": ["11.2", "11.3"], "details": "Utilizar dados de volume para testar tempo de resposta das queries antes e depois das otimizações. Medir o hit rate do cache e identificar possíveis pontos de melhoria.", "status": "pending", "testStrategy": ""}, {"id": 5, "title": "Invalidação e Atualização de Cache", "description": "Implementar mecanismos para invalidação e atualização do cache sempre que houver alterações relevantes nos dados.", "dependencies": ["11.3", "11.4"], "details": "Desenvolver métodos para invalidar o cache de usuários e permissões após alterações, garantindo que os dados estejam sempre atualizados e evitando inconsistências.", "status": "pending", "testStrategy": ""}, {"id": 6, "title": "Documentação das Otimizações", "description": "Documentar todas as otimizações realizadas, incluindo decisões técnicas, configurações de índices e estratégias de cache.", "dependencies": ["11.1", "11.2", "11.3", "11.4", "11.5"], "details": "Produzir documentação técnica detalhada sobre as an<PERSON><PERSON><PERSON>, índices criados, configuração do cache, testes realizados e resultados obtidos, facilitando futuras manutenções.", "status": "pending", "testStrategy": ""}]}, {"id": 12, "title": "<PERSON><PERSON><PERSON><PERSON>, CORS e Deploy com Docker/Nginx", "description": "Implementar configurações de segurança, CORS restrito por ambiente e setup de deploy com Docker e Nginx", "details": "Configurar segurança completa e deploy:\n\n```php\n// config/cors.php\nreturn [\n    'paths' => ['api/*', 'sanctum/csrf-cookie'],\n    'allowed_methods' => ['*'],\n    'allowed_origins' => env('APP_ENV') === 'production' \n        ? [env('APP_URL')] \n        : ['http://localhost:3000', 'http://127.0.0.1:8000'],\n    'allowed_origins_patterns' => [],\n    'allowed_headers' => ['*'],\n    'exposed_headers' => [],\n    'max_age' => 0,\n    'supports_credentials' => true,\n];\n```\n\n```nginx\n# .docker/nginx/production.conf\nserver {\n    listen 80;\n    server_name central-atendimento.edu.br;\n    root /var/www/html/public;\n    index index.php;\n    \n    # Security headers\n    add_header X-Frame-Options \"SAMEORIGIN\" always;\n    add_header X-Content-Type-Options \"nosniff\" always;\n    add_header X-XSS-Protection \"1; mode=block\" always;\n    add_header Referrer-Policy \"strict-origin-when-cross-origin\" always;\n    add_header Content-Security-Policy \"default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:\" always;\n    \n    location / {\n        try_files $uri $uri/ /index.php?$query_string;\n    }\n    \n    location ~ \\.php$ {\n        fastcgi_pass app:9000;\n        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;\n        include fastcgi_params;\n    }\n    \n    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg)$ {\n        expires 1y;\n        add_header Cache-Control \"public, immutable\";\n    }\n}\n```\n\n```dockerfile\n# Dockerfile\nFROM php:8.3-fpm\n\nRUN apt-get update && apt-get install -y \\\n    libpq-dev \\\n    libzip-dev \\\n    unzip \\\n    && docker-php-ext-install pdo pdo_pgsql zip\n\nCOPY --from=composer:latest /usr/bin/composer /usr/bin/composer\n\nWORKDIR /var/www/html\nCOPY . .\n\nRUN composer install --no-dev --optimize-autoloader\nRUN php artisan config:cache\nRUN php artisan route:cache\nRUN php artisan view:cache\n\nCMD [\"php-fpm\"]\n```", "testStrategy": "Testes de headers de segurança, validação de CORS por ambiente, testes de deploy em containers, verificar CSP e performance de assets estáticos", "priority": "medium", "dependencies": [11], "status": "pending", "subtasks": [{"id": 1, "title": "Configurar CORS restrito por ambiente", "description": "Implementar a configuração do arquivo cors.php para permitir origens específicas conforme o ambiente (produção ou desenvolvimento), garantindo que apenas domínios autorizados possam acessar a API.", "dependencies": [], "details": "Ajustar o array 'allowed_origins' para alternar entre APP_URL em produção e localhost em desenvolvimento. Validar funcionamento em ambos ambientes.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Implementar headers de segurança no Nginx", "description": "Adicionar e validar headers de segurança no arquivo de configuração do Nginx, como X-Frame-Options, X-Content-Type-Options, X-XSS-Protection, Referrer-Policy e Content-Security-Policy.", "dependencies": [], "details": "Editar o arquivo .docker/nginx/production.conf para incluir os headers recomendados e garantir que estejam ativos em todas as respostas do servidor.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Criar Dockerfile e setup dos containers", "description": "Desenvolver o Dockerfile para PHP-FPM, configurar dependências, copiar arquivos do projeto e preparar o ambiente para rodar em containers junto ao Nginx.", "dependencies": [], "details": "Montar o Dockerfile conforme especificação, instalar extensões necessárias, copiar composer e rodar comandos de cache. Integrar com container Nginx via docker-compose.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Realizar testes de deploy em diferentes ambientes", "description": "Executar testes de deploy nos ambientes de desenvolvimento e produção, validando funcionamento dos containers, acesso à aplicação e restrições de CORS.", "dependencies": ["12.1", "12.2", "12.3"], "details": "Subir containers em ambos ambientes, testar endpoints protegidos, verificar resposta dos headers e funcionamento do CORS conforme configuração.", "status": "pending", "testStrategy": ""}, {"id": 5, "title": "Validar Content-Security-Policy e performance de assets", "description": "Testar a aplicação das regras de Content-Security-Policy e analisar o carregamento e cache dos assets estáticos para garantir segurança e performance.", "dependencies": ["12.2", "12.4"], "details": "Utilizar ferramentas de análise de headers e performance (ex: Lighthouse, curl) para validar CSP, cache-control e impacto no carregamento dos assets.", "status": "pending", "testStrategy": ""}, {"id": 6, "title": "Documentar o processo de deploy e configurações", "description": "Produzir documentação detalhada do processo de configuração de CORS, headers de segurança, setup dos containers e procedimentos de deploy.", "dependencies": ["12.1", "12.2", "12.3", "12.4", "12.5"], "details": "Registrar passos, comandos utilizados, arquivos de configuração e recomendações para manutenção futura, facilitando onboarding e auditoria.", "status": "pending", "testStrategy": ""}]}, {"id": 13, "title": "Checklist: Correções imediatas", "description": "Ajustes urgentes de navegação, Docker e limpeza de arquivos residuais conforme docs/alerts.md e docs/checklist.md.", "details": "", "testStrategy": "", "status": "pending", "dependencies": [], "priority": "high", "subtasks": [{"id": 1, "title": "Ajustar rota Home na navegação", "description": "Atualizar link para Home em `resources/views/layouts/navigation.blade.php` de `route('home')` para `route('home.index')`; validar em runtime.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 13}, {"id": 2, "title": "<PERSON>rri<PERSON><PERSON> caminho `cd` no `docker-entrypoint.sh`", "description": "Alterar `cd /var/www/html/` para `cd /var/www`, alinhado ao `WORKDIR`; validar que o container sobe corretamente.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 13}, {"id": 3, "title": "Atualizar Dockerfile para não usar `composer update` em produção", "description": "Substituir por `composer install --no-dev --prefer-dist --no-interaction --optimize-autoloader` utilizando `composer.lock`; validar no CI/build.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 13}, {"id": 4, "title": "Remover `public/hot` do repositório e ignorar via `.gitignore`", "description": "Deletar `public/hot` e adicionar regra `/public/hot` ao `.gitignore`; garantir inexistência em produção.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 13}, {"id": 5, "title": "Remover arquivo residual `app/Providers/AuthServiceProvider.php.install`", "description": "Excluir arquivo residual e verificar que não há referências a ele.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 13}]}, {"id": 14, "title": "Checklist: Build/Assets via Vite", "description": "Consolidar assets via Vite, remover cópias manuais para public/js e limpar dependências/arquivos obsoletos.", "details": "", "testStrategy": "", "status": "pending", "dependencies": [], "priority": "medium", "subtasks": [{"id": 1, "title": "Consolidar assets via Vite", "description": "Padronizar o carregamento de JS/CSS via `@vite` e `vite.config.js`, removendo referências diretas a `public/js` nas views.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 14}, {"id": 2, "title": "Remover cópias manuais redundantes em `public/js`", "description": "Eliminar `resources/js` copiados manualmente para `public/js` (jQuery, Toastr, app.js) quando já atendidos por Vite.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 14}, {"id": 3, "title": "Validar entradas do Vite e uso real nas views", "description": "Revisar `vite.config.js` e `resources/js/app.js` vs. includes nas blades, ajustando imports/aliases conforme necessário.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 14}, {"id": 4, "title": "Remover dependências/arquivos obsoletos", "description": "Remover `axios` se não utilizado e `resources/js/bootstrap.js` se obsoleto; ajustar `package.json`.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 14}]}, {"id": 15, "title": "Checklist: UX (navegação e ações pendentes)", "description": "Correções de usabilidade: botão Atualizar do perfil e navegação Anterior/Próximo na preview de chamado.", "details": "", "testStrategy": "", "status": "pending", "dependencies": [], "priority": "medium", "subtasks": [{"id": 1, "title": "<PERSON><PERSON> bot<PERSON> “Atualizar” (perfil)", "description": "Em `resources/views/home/<USER>", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 15}, {"id": 2, "title": "Implementar/ocultar navega<PERSON> “Anterior/Próximo” na preview", "description": "Corrigir links na preview de chamado (`resources/views/chamado/preview.blade.php`): implementar paginação válida ou ocultar.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 15}]}, {"id": 16, "title": "Checklist: Infra (Node LTS e .env)", "description": "Ajustar versão do Node para LTS em builds e garantir variáveis de ambiente completas para pgsql e pgsql_sgs.", "details": "", "testStrategy": "", "status": "pending", "dependencies": [], "priority": "medium", "subtasks": [{"id": 1, "title": "Atualizar Node para LTS no build", "description": "Ajustar `Dockerfile`/CI para usar Node LTS (18/20) nas etapas de build front-end.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 16}, {"id": 2, "title": "Validar `.env` para `pgsql` e `pgsql_sgs`", "description": "Garantir que variáveis de conexão existam e estão corretas para ambos bancos; documentar chaves obrigatórias.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 16}]}, {"id": 17, "title": "Checklist: Documentação (versões e READMEs)", "description": "Manter versões resolvidas registradas e atualizar README(s) quando padrões mudarem.", "details": "", "testStrategy": "", "status": "pending", "dependencies": [], "priority": "low", "subtasks": [{"id": 1, "title": "Registrar ve<PERSON><PERSON><PERSON> resolvid<PERSON> (Composer/NPM)", "description": "Gerar e anexar relatório de versões a `docs/readme-diff.md` ou similar; incluir lockfiles no controle.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 17}, {"id": 2, "title": "Atualizar READMEs quando padrões mudarem", "description": "Revisar `README.md` e `docs/*` para refletir mudanças em build, assets, infra e padrões adotados.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 17}]}], "metadata": {"created": "2025-08-13T18:13:10.949Z", "updated": "2025-08-14T13:23:04.937Z", "description": "Tasks for master context"}}}