# Task ID: 3
# Title: Desenvolver Models e Migrations do Sistema de Chamados
# Status: pending
# Dependencies: 2
# Priority: high
# Description: Criar estrutura de dados completa para chamados, histórico, mensagens, tombos e atribuições
# Details:
Implementar Models Eloquent e migrations para:
- <PERSON><PERSON><PERSON> (status, prioridade, setor_atendente_id, usuario_id)
- <PERSON>mad<PERSON>Histori<PERSON> (versionamento de estados)
- ChamadoMensagem (chat com suporte a imagens)
- <PERSON>madoTombo (registro de bens/equipamentos)
- Atribuicao (controle de responsáveis)

```php
// Migration exemplo
Schema::create('chamados', function (Blueprint $table) {
    $table->id();
    $table->string('titulo');
    $table->text('descricao');
    $table->enum('status', ['aberto', 'em_atendimento', 'fechado']);
    $table->enum('prioridade', ['baixa', 'media', 'alta']);
    $table->foreignId('usuario_id')->constrained();
    $table->foreignId('setor_atendente_id')->nullable();
    $table->timestamps();
});
```

Implementar relacionamentos Eloquent e scopes para consultas otimizadas.

# Test Strategy:
Testes de factory para models, validação de relacionamentos, testes de constraints de banco, verificar integridade referencial

# Subtasks:
## 1. Modelagem do Banco de Dados [pending]
### Dependencies: None
### Description: Definir as entidades, atributos e relacionamentos necessários para Chamado, ChamadoHistorico, ChamadoMensagem, ChamadoTombo e Atribuicao.
### Details:
Elaborar o diagrama entidade-relacionamento (DER) e especificar os campos, tipos de dados, chaves primárias e estrangeiras para cada tabela.

## 2. Implementação das Migrations [pending]
### Dependencies: 3.1
### Description: Criar as migrations para todas as tabelas definidas na modelagem, incluindo constraints e índices.
### Details:
Utilizar o Schema Builder do Laravel para implementar as migrations, garantindo integridade referencial e performance.

## 3. Criação dos Models Eloquent [pending]
### Dependencies: 3.2
### Description: Desenvolver os models Eloquent para cada entidade, seguindo as convenções de nomenclatura e estrutura do Laravel.
### Details:
Implementar as classes de modelo, definir os atributos fillable, casts e eventuais mutators/acessors necessários.

## 4. Implementação dos Relacionamentos e Scopes [pending]
### Dependencies: 3.3
### Description: Configurar os relacionamentos entre os models e criar scopes para consultas otimizadas.
### Details:
Definir métodos de relacionamento (hasMany, belongsTo, etc.) e implementar scopes locais para filtros frequentes.

## 5. Testes de Integridade e Constraints [pending]
### Dependencies: 3.4
### Description: Validar a integridade das migrations, models e relacionamentos, incluindo constraints de banco e factories.
### Details:
Executar testes automatizados para garantir que as constraints, relacionamentos e factories estejam funcionando corretamente.

