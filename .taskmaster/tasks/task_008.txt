# Task ID: 8
# Title: Implementar Interface de Chat com Upload de Imagens
# Status: pending
# Dependencies: 5
# Priority: medium
# Description: Desenvolver sistema de mensagens em tempo real com suporte a imagens entre solicitantes e atendentes
# Details:
Criar interface de chat responsiva com upload de imagens:

```php
// MensagemController
class MensagemController extends Controller {
    public function store(Request $request, Chamado $chamado) {
        $request->validate([
            'conteudo' => 'required_without:imagem|string|max:1000',
            'imagem' => 'nullable|image|max:2048'
        ]);
        
        $mensagem = ChamadoMensagem::create([
            'chamado_id' => $chamado->id,
            'usuario_id' => auth()->id(),
            'conteudo' => $request->conteudo,
            'imagem_path' => $request->file('imagem')?->store('mensagens', 'public'),
            'tipo' => auth()->user()->isAtendente($chamado) ? 'atendente' : 'solicitante'
        ]);
        
        return response()->json([
            'mensagem' => $mensagem->load('usuario'),
            'html' => view('components.mensagens.usuario', compact('mensagem'))->render()
        ]);
    }
}
```

```javascript
// resources/js/batepapo.js
class ChatManager {
    constructor(chamadoId) {
        this.chamadoId = chamadoId;
        this.initializeEventListeners();
        this.loadMessages();
    }
    
    sendMessage(formData) {
        fetch(`/chamado/${this.chamadoId}/mensagem`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => this.appendMessage(data.html));
    }
}
```

# Test Strategy:
Testes de upload de imagem com validação de tamanho/tipo, testes de autorização para envio de mensagens, verificar renderização correta do chat, testes de responsividade mobile

# Subtasks:
## 1. Implementação do Backend de Mensagens em Tempo Real [pending]
### Dependencies: None
### Description: Desenvolver a lógica de envio, recebimento e armazenamento de mensagens entre solicitantes e atendentes, utilizando eventos e filas para garantir comunicação em tempo real.
### Details:
Utilizar Laravel Broadcasting e WebSockets para disparar eventos de novas mensagens e garantir atualização instantânea do chat para todos os participantes.

## 2. Implementação de Upload e Validação de Imagens [pending]
### Dependencies: 8.1
### Description: Adicionar suporte ao upload de imagens nas mensagens, incluindo validação de tipo e tamanho conforme requisitos do sistema.
### Details:
Configurar validação no backend para arquivos de imagem, armazenar imagens de forma segura e associar ao registro da mensagem.

## 3. Criação da Interface Responsiva do Chat [pending]
### Dependencies: 8.1, 8.2
### Description: Desenvolver o frontend do chat com layout responsivo, garantindo boa experiência em dispositivos móveis e desktop.
### Details:
Utilizar frameworks modernos (ex: Vue.js, Tailwind) para criar componentes de chat, área de mensagens e upload de imagens.

## 4. Integração Frontend-Backend para Mensagens e Imagens [pending]
### Dependencies: 8.3
### Description: Conectar a interface do chat ao backend, permitindo envio e recebimento de mensagens e imagens em tempo real.
### Details:
Implementar chamadas AJAX/fetch, escutar eventos de WebSocket e atualizar a interface conforme novas mensagens ou imagens forem recebidas.

## 5. Testes de Autorização e Responsividade [pending]
### Dependencies: 8.4
### Description: Realizar testes para garantir que apenas usuários autorizados possam enviar mensagens e imagens, além de validar o funcionamento em diferentes dispositivos.
### Details:
Testar regras de autorização, upload de imagens, renderização do chat em mobile e desktop, e atualização em tempo real.

## 6. Documentação de Uso do Sistema de Chat [pending]
### Dependencies: 8.5
### Description: Produzir documentação clara sobre o funcionamento do chat, incluindo instruções de uso, limitações e exemplos de integração.
### Details:
Descrever fluxo de mensagens, upload de imagens, requisitos técnicos e orientações para usuários finais e desenvolvedores.

