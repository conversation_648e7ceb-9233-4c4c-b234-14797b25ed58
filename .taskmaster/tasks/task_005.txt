# Task ID: 5
# Title: Desenvolver Fluxo Completo de Chamados (Abertura ao Fechamento)
# Status: pending
# Dependencies: 3, 4
# Priority: high
# Description: Implementar ciclo completo de chamados: abertura, pré-visualização, atendimento, chat com imagens e fechamento
# Details:
Implementar Controllers e Services para fluxo completo:

```php
// ChamadoController
public function store(ChamadoRequest $request) {
    $chamado = $this->chamadoService->create([
        'titulo' => $request->titulo,
        'descricao' => $request->descricao,
        'usuario_id' => auth()->id(),
        'servico_id' => $request->servico_id,
        'status' => 'aberto'
    ]);
    
    $this->historicoService->registrar($chamado, 'Chamado criado');
    return redirect()->route('chamado.preview', $chamado);
}

public function atender(Chamado $chamado) {
    Gate::authorize('attend-tickets');
    $this->chamadoService->atribuir($chamado, auth()->user());
    return redirect()->route('chamado.show', $chamado);
}

public function adicionarMensagem(Request $request, Chamado $chamado) {
    $mensagem = $this->mensagemService->create([
        'chamado_id' => $chamado->id,
        'usuario_id' => auth()->id(),
        'conteudo' => $request->conteudo,
        'imagem' => $request->file('imagem')?->store('mensagens', 'public')
    ]);
    
    return response()->json($mensagem);
}
```

Implementar upload de imagens com validação e storage público.

# Test Strategy:
Testes de fluxo completo E2E, validação de upload de imagens, testes de autorização por etapa, verificar histórico de mudanças de status

# Subtasks:
## 1. Implementação do Fluxo de Abertura de Chamados [pending]
### Dependencies: None
### Description: Desenvolver o processo de abertura de chamados, incluindo validação dos dados, registro inicial e integração com o histórico.
### Details:
Criar métodos no Controller e Service para receber requisições, validar dados obrigatórios e registrar o chamado com status 'aberto'.

## 2. Desenvolver Pré-visualização de Chamados [pending]
### Dependencies: 5.1
### Description: Implementar funcionalidade para pré-visualizar detalhes do chamado recém-criado antes do atendimento.
### Details:
Criar rota e view para exibir informações do chamado, permitindo revisão pelo usuário antes do encaminhamento.

## 3. Implementar Atendimento e Atribuição de Chamados [pending]
### Dependencies: 5.2
### Description: Desenvolver lógica para atribuir chamados a atendentes autorizados e registrar a ação no histórico.
### Details:
Adicionar autorização via Gate, atribuir usuário responsável e atualizar status do chamado para 'em atendimento'.

## 4. Desenvolver Chat com Upload de Imagens [pending]
### Dependencies: 5.3
### Description: Implementar chat interno no chamado, permitindo envio de mensagens e imagens com validação e armazenamento seguro.
### Details:
Criar endpoints para envio de mensagens, validar e armazenar imagens em storage público, garantir integridade dos dados.

## 5. Implementar Fechamento do Chamado [pending]
### Dependencies: 5.4
### Description: Desenvolver processo para finalizar o chamado, atualizar status e registrar motivo do fechamento.
### Details:
Adicionar método para fechar chamado, registrar ação no histórico e notificar envolvidos.

## 6. Registrar Histórico de Mudanças [pending]
### Dependencies: 5.1, 5.3, 5.4, 5.5
### Description: Garantir que todas as ações relevantes no ciclo do chamado sejam registradas no histórico para auditoria.
### Details:
Integrar serviço de histórico em cada etapa do fluxo, detalhando mudanças de status, mensagens e atribuições.

## 7. Realizar Testes End-to-End do Fluxo Completo [pending]
### Dependencies: 5.6
### Description: Desenvolver e executar testes E2E para validar o funcionamento do ciclo completo de chamados, incluindo upload de imagens e autorizações.
### Details:
Criar cenários de teste cobrindo abertura, pré-visualização, atendimento, chat, fechamento e histórico.

## 8. Documentar o Fluxo Completo de Chamados [pending]
### Dependencies: 5.7
### Description: Produzir documentação técnica detalhada do fluxo, endpoints, regras de negócio e exemplos de uso.
### Details:
Elaborar documentação para desenvolvedores e usuários, incluindo diagramas, exemplos de requisições e respostas.

