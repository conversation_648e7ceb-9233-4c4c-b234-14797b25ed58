# Task ID: 4
# Title: Implementar CRUD de Catálogo de Serviços com Público-Alvo
# Status: pending
# Dependencies: 2, 3
# Priority: medium
# Description: Desenvolver sistema de gestão de serviços com categorização por público-alvo e filtros dinâmicos
# Details:
<PERSON><PERSON><PERSON>, Service e Repository para CRUD completo. Implementar categorização por público-alvo (aluno, servidor, técnico, docente, visitante). Adicionar filtros dinâmicos no login:

```php
// ServicoController
class ServicoController extends Controller {
    public function index(Request $request) {
        $categoria = $request->user()->categoria;
        return $this->servicoService->getByCategoria($categoria);
    }
    
    public function store(ServicoRequest $request) {
        Gate::authorize('manage-services');
        return $this->servicoService->create($request->validated());
    }
}

// Model
class Servico extends Model {
    protected $fillable = ['nome', 'descricao', 'categoria_publico', 'criticidade', 'status'];
    
    public function scopeByCategoria($query, $categoria) {
        return $query->whereJsonContains('categoria_publico', $categoria);
    }
}
```

# Test Strategy:
Testes CRUD completos, validação de filtros por categoria, testes de autorização para criação/edição, verificar exibição correta no login

# Subtasks:
## 1. Implementar o ServicoController com métodos CRUD [pending]
### Dependencies: None
### Description: Criar o controlador responsável pelas operações de criação, leitura, atualização e exclusão dos serviços, seguindo o padrão resource do Laravel.
### Details:
Utilizar o comando artisan para gerar o controller resource. Implementar métodos index, store, show, update e destroy, garantindo integração com o Service e aplicação das regras de autorização.

## 2. Desenvolver Service e Repository para Serviços [pending]
### Dependencies: 4.1
### Description: Criar as camadas de Service e Repository para encapsular a lógica de negócio e acesso a dados do catálogo de serviços.
### Details:
Implementar métodos para manipulação dos dados (CRUD) no Service e Repository, garantindo separação de responsabilidades e facilitando testes.

## 3. Implementar categorização por público-alvo [pending]
### Dependencies: 4.2
### Description: Adicionar suporte à categorização dos serviços por público-alvo (aluno, servidor, técnico, docente, visitante) no modelo e nas operações CRUD.
### Details:
Ajustar o model Servico para suportar múltiplas categorias de público. Implementar métodos e validações para garantir o correto armazenamento e recuperação dessa informação.

## 4. Adicionar filtros dinâmicos por categoria no login [pending]
### Dependencies: 4.3
### Description: Implementar lógica para filtrar os serviços exibidos ao usuário com base na categoria do público-alvo durante o login.
### Details:
No método index do ServicoController, aplicar filtro dinâmico utilizando o escopo byCategoria, retornando apenas os serviços relevantes para o usuário logado.

## 5. Realizar testes e validação de autorização [pending]
### Dependencies: 4.4
### Description: Testar todas as operações CRUD, filtros por categoria e regras de autorização para garantir o correto funcionamento e segurança do sistema.
### Details:
Executar testes manuais e/ou automatizados para validar as operações, filtros dinâmicos e autorização (Gate) para criação e edição de serviços.

