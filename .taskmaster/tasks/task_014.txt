# Task ID: 14
# Title: Checklist: Build/Assets via Vite
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Consolidar assets via Vite, remover cópias manuais para public/js e limpar dependências/arquivos obsoletos.
# Details:


# Test Strategy:


# Subtasks:
## 1. Consolidar assets via Vite [pending]
### Dependencies: None
### Description: Padronizar o carregamento de JS/CSS via `@vite` e `vite.config.js`, removendo referências diretas a `public/js` nas views.
### Details:


## 2. Remover cópias manuais redundantes em `public/js` [pending]
### Dependencies: None
### Description: Eliminar `resources/js` copiados manualmente para `public/js` (jQuery, Toastr, app.js) quando já atendidos por Vite.
### Details:


## 3. Validar entradas do Vite e uso real nas views [pending]
### Dependencies: None
### Description: Revisar `vite.config.js` e `resources/js/app.js` vs. includes nas blades, ajustando imports/aliases conforme necessário.
### Details:


## 4. Remover dependências/arquivos obsoletos [pending]
### Dependencies: None
### Description: Remover `axios` se não utilizado e `resources/js/bootstrap.js` se obsoleto; ajustar `package.json`.
### Details:


