# Task ID: 6
# Title: Implementar Sistema de Regulação Manual
# Status: pending
# Dependencies: 5
# Priority: medium
# Description: Desenvolver interface e lógica para regulação manual de chamados por unidade/setor/atendente
# Details:
Criar RegulacaoController e Service para gerenciamento de fila de regulação:

```php
// RegulacaoController
class RegulacaoController extends Controller {
    public function index() {
        Gate::authorize('regulate-tickets');
        $chamados = $this->regulacaoService->getFilaRegulacao();
        return view('regulacao.index', compact('chamados'));
    }
    
    public function encaminhar(Request $request, Chamado $chamado) {
        $this->regulacaoService->encaminhar($chamado, [
            'setor_destino_id' => $request->setor_id,
            'atendente_id' => $request->atendente_id,
            'observacoes' => $request->observacoes
        ]);
        
        $this->historicoService->registrar($chamado, 'Encaminhado para ' . $request->setor_nome);
        return redirect()->back()->with('success', 'Chamado encaminhado com sucesso');
    }
}

// Service
class RegulacaoService {
    public function encaminhar(Chamado $chamado, array $dados) {
        $chamado->update([
            'setor_atendente_id' => $dados['setor_destino_id'],
            'status' => 'regulado'
        ]);
        
        Atribuicao::create([
            'chamado_id' => $chamado->id,
            'usuario_id' => $dados['atendente_id'],
            'tipo' => 'regulacao'
        ]);
    }
}
```

# Test Strategy:
Testes de regulação por diferentes critérios, validação de autorização para reguladores, testes de atribuição correta, verificar registro no histórico

# Subtasks:
## 1. Implementar RegulacaoController [pending]
### Dependencies: None
### Description: Criar o controller responsável pelas rotas de regulação manual, incluindo métodos para exibir a fila de chamados e encaminhar chamados para setores/atendentes.
### Details:
O controller deve conter métodos como index() para listar chamados e encaminhar() para processar o encaminhamento, utilizando autorização adequada.

## 2. Desenvolver RegulacaoService [pending]
### Dependencies: 6.1
### Description: Implementar o serviço que centraliza a lógica de negócios da regulação, incluindo atualização de chamados e criação de atribuições.
### Details:
O service deve ter métodos para encaminhar chamados, atualizar status e criar registros de atribuição, garantindo separação da lógica do controller.

## 3. Implementar Lógica de Fila e Encaminhamento [pending]
### Dependencies: 6.2
### Description: Desenvolver a lógica para obtenção da fila de regulação e regras de encaminhamento de chamados para setores e atendentes.
### Details:
Incluir métodos para buscar chamados pendentes de regulação, aplicar filtros por unidade/setor/atendente e garantir integridade dos dados ao encaminhar.

## 4. Testar Autorização e Atribuição [pending]
### Dependencies: 6.3
### Description: Criar testes para garantir que apenas usuários autorizados possam regular chamados e que a atribuição de chamados seja feita corretamente.
### Details:
Testar permissões via Gates, simular diferentes perfis de usuário e validar que a atribuição de chamados ocorre conforme esperado.

## 5. Registrar e Validar Histórico de Regulação [pending]
### Dependencies: 6.4
### Description: Implementar e testar o registro de ações de regulação no histórico dos chamados, garantindo rastreabilidade e validação dos dados registrados.
### Details:
Registrar cada encaminhamento no histórico, validar informações como setor, atendente e observações, e garantir que o histórico seja consultável.

