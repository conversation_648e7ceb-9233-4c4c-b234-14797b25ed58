stages:
  # - staging
  - sonarqube-check
    # - building

cache:
  key: "${CI_COMMIT_REF_SLUG}"

# staging:
#   image: php:8.1.2
#   stage: staging
#   rules:
#     - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
#   script:
#     - apt update -y && apt install -y openssh-client rsync
    #- bash deploy/deploy.sh

sonarqube-check:
  stage: sonarqube-check
  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar" 
    GIT_DEPTH: "0" 
    DOCKER_NETWORK_MODE: "host"
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - git config --global user.name "<PERSON>"
    - git config --global user.email <EMAIL>
    - git config --global --add safe.directory /builds/sti/central-atendimento
    - git fetch origin
    - git checkout $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
    - git merge $CI_COMMIT_SHA --no-commit --no-ff
    # Executar análise do SonarQube
    - sonar-scanner
      -Dsonar.qualitygate.wait=true
      -Dsonar.host.url=$SONAR_HOST_URL
      -Dsonar.login=$SONAR_TOKEN
      -Dsonar.gitlab.project_id=$CI_PROJECT_ID  
      -Dsonar.gitlab.merge_request_iid=$CI_MERGE_REQUEST_IID
      -Dsonar.gitlab.commit_sha=$CI_COMMIT_SHA    
  allow_failure: true
  only:
    - develop
    - merge_request
