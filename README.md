# Central de Atendimento

Sistema de gerenciamento de chamados (tickets) desenvolvido em Laravel para atendimento e suporte organizacional.

## Sobre o Sistema

O **Central de Atendimento** é uma aplicação web para gestão de solicitações de serviços e tickets de suporte, com integração ao Sistema de Gestão de Serviços (SGS). O sistema permite criar, acompanhar e gerenciar chamados com fluxo de trabalho completo, desde a abertura até a conclusão.

### Principais Funcionalidades

- **Gestão de Chamados**: Criação, acompanhamento e resolução de tickets
- **Catálogo de Serviços**: Serviços organizados por setores e unidades
- **Sistema de Mensagens**: Chat integrado para comunicação nos chamados
- **Roteamento Inteligente**: Direcionamento automático por setor e especialidade
- **Relatórios**: Geração de relatórios de chamados e serviços
- **Anexos**: Suporte a upload de imagens e arquivos
- **Integração SGS**: Autenticação e dados de usuários do sistema SGS

## Tecnologias

- **Backend**: Laravel 12.0, PHP 8.2+
- **Frontend**: CoreUI 4.0, Bootstrap 5.1, Vite
- **Banco de Dados**: PostgreSQL (aplicação + SGS)
- **Containerização**: Docker Compose
- **Ferramentas**: Laravel Pint, PHPUnit, Laravel Debugbar

## Configuração e Instalação

### Pré-requisitos

- Docker e Docker Compose
- Git

### Setup Inicial

1. **Clone o repositório**

```bash
git clone <repository-url>
cd central-atendimento
```

2. **Configuração do ambiente**

```bash
cp .env.example .env
```

3. **Subir os containers**

```bash
docker compose up -d
```

4. **Acessar o container da aplicação**

```bash
docker exec -it central-atendimento-app bash
```

5. **Instalar dependências (dentro do container)**

```bash
npm install
composer install
npm run build
php artisan key:generate
```

### Comandos Make Disponíveis

```bash
make setup        # Setup inicial completo
make up           # Subir containers
make down         # Parar containers
make migrateup    # Executar migrations e seeds
make test         # Executar testes
make freshinstall # Instalação completa do zero
```

## Acesso ao Sistema

Após a instalação, o sistema estará disponível em:

**URL**: http://localhost:8000/

**Credenciais**:

- **Login**: CPF cadastrado no SGS
- **Senha**: Qualquer senha (integração com SGS)

## Estrutura do Projeto

O projeto segue o padrão **Service-Repository** com separação clara de responsabilidades:

- **Controllers**: Manipulação de requisições HTTP
- **Services**: Lógica de negócio e operações complexas
- **Models**: Modelos Eloquent com relacionamentos
- **Views**: Templates Blade organizados por funcionalidade

### Diretórios Principais

```
app/
├── Http/Controllers/    # Controladores HTTP
├── Services/           # Camada de serviços
├── Models/            # Modelos Eloquent
│   └── SGS/          # Modelos do sistema SGS
├── Util/             # Classes utilitárias
└── Helpers/          # Funções auxiliares

resources/views/
├── chamado/          # Views de chamados
├── regulacao/        # Views de regulação
├── relatorios/       # Views de relatórios
└── layouts/          # Layouts base
```

## Desenvolvimento

### Comandos Úteis

```bash
# Desenvolvimento de assets
npm run dev          # Build de desenvolvimento
npm run build        # Build de produção

# Laravel
php artisan migrate  # Executar migrations
php artisan seed     # Executar seeders
php artisan tinker   # REPL do Laravel

# Testes
vendor/bin/phpunit   # Executar testes
```

### Padrões de Código

- **Nomenclatura**: Termos em português para entidades de negócio
- **Controllers**: Sufixo `Controller`, injeção de dependência via construtor
- **Services**: Sufixo `Servicos`, contém lógica de negócio
- **Models**: Relacionamentos bem definidos, conexões específicas para SGS

## Documentação Adicional

Para informações detalhadas sobre arquitetura, tecnologias e padrões utilizados, consulte os documentos de orientação em `.kiro/steering/`:

- **product.md**: Visão geral do produto e funcionalidades
- **tech.md**: Stack tecnológico e comandos de desenvolvimento
- **structure.md**: Arquitetura e organização do projeto

## Contribuição

1. Siga os padrões estabelecidos no projeto
2. Utilize os Services para lógica de negócio
3. Mantenha Controllers enxutos
4. Documente mudanças significativas
5. Execute testes antes de commits

## Suporte

Para dúvidas sobre o sistema, consulte a documentação interna ou entre em contato com a equipe de desenvolvimento.

<!-- TASKMASTER_EXPORT_START -->
> 🎯 **Taskmaster Export** - 2025-08-18 13:38:45 UTC
> 📋 Export: without subtasks • Status filter: none
> 🔗 Powered by [Task Master](https://task-master.dev?utm_source=github-readme&utm_medium=readme-export&utm_campaign=central-atendimento&utm_content=task-export-link)

| Project Dashboard |  |
| :-                |:-|
| Task Progress     | ░░░░░░░░░░░░░░░░░░░░ 0% |
| Done | 0 |
| In Progress | 0 |
| Pending | 17 |
| Deferred | 0 |
| Cancelled | 0 |
|-|-|
| Subtask Progress | ░░░░░░░░░░░░░░░░░░░░ 0% |
| Completed | 0 |
| In Progress | 0 |
| Pending | 86 |


| ID | Title | Status | Priority | Dependencies | Complexity |
| :- | :-    | :-     | :-       | :-           | :-         |
| 1 | Configurar Autenticação Segura e Integração SGS | ○&nbsp;pending | high | None | ● 9 |
| 2 | Implementar Sistema de Permissões com Gates Dinâmicos | ○&nbsp;pending | high | 1 | ● 8 |
| 3 | Desenvolver Models e Migrations do Sistema de Chamados | ○&nbsp;pending | high | 2 | ● 7 |
| 4 | Implementar CRUD de Catálogo de Serviços com Público-Alvo | ○&nbsp;pending | medium | 2, 3 | ● 6 |
| 5 | Desenvolver Fluxo Completo de Chamados (Abertura ao Fechamento) | ○&nbsp;pending | high | 3, 4 | ● 9 |
| 6 | Implementar Sistema de Regulação Manual | ○&nbsp;pending | medium | 5 | ● 5 |
| 7 | Desenvolver Regulação Automática SIC/SRCA por Campus | ○&nbsp;pending | medium | 6 | ● 5 |
| 8 | Implementar Interface de Chat com Upload de Imagens | ○&nbsp;pending | medium | 5 | ● 5 |
| 9 | Desenvolver Sistema de Tombos e Histórico Completo | ○&nbsp;pending | medium | 5 | ● 6 |
| 10 | Implementar Relatórios PDF e Painéis Gerenciais | ○&nbsp;pending | medium | 5, 6, 7 | ● 6 |
| 11 | Implementar Otimizações de Performance e Cache | ○&nbsp;pending | medium | 10 | ● 7 |
| 12 | Configurar Segurança, CORS e Deploy com Docker/Nginx | ○&nbsp;pending | medium | 11 | ● 8 |
| 13 | Checklist: Correções imediatas | ○&nbsp;pending | high | None | ● 5 |
| 14 | Checklist: Build/Assets via Vite | ○&nbsp;pending | medium | None | ● 4 |
| 15 | Checklist: UX (navegação e ações pendentes) | ○&nbsp;pending | medium | None | ● 5 |
| 16 | Checklist: Infra (Node LTS e .env) | ○&nbsp;pending | medium | None | ● 5 |
| 17 | Checklist: Documentação (versões e READMEs) | ○&nbsp;pending | low | None | ● 5 |

> 📋 **End of Taskmaster Export** - Tasks are synced from your project using the `sync-readme` command.
<!-- TASKMASTER_EXPORT_END -->
