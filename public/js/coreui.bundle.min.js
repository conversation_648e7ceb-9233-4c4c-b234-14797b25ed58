/*!
  * CoreUI v4.2.3 (https://coreui.io)
  * Copyright 2022 The CoreUI Team (https://github.com/orgs/coreui/people)
  * Licensed under MIT (https://coreui.io)
  */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).coreui=e()}(this,(function(){"use strict";const t="transitionend",e=t=>(t&&window.CSS&&window.CSS.escape&&(t=t.replace(/#([^\s"#']+)/g,((t,e)=>`#${CSS.escape(e)}`))),t),i=e=>{e.dispatchEvent(new Event(t))},n=t=>!(!t||"object"!=typeof t)&&(void 0!==t.jquery&&(t=t[0]),void 0!==t.nodeType),s=t=>n(t)?t.jquery?t[0]:t:"string"==typeof t&&t.length>0?document.querySelector(e(t)):null,o=t=>{if(!n(t)||0===t.getClientRects().length)return!1;const e="visible"===getComputedStyle(t).getPropertyValue("visibility"),i=t.closest("details:not([open])");if(!i)return e;if(i!==t){const e=t.closest("summary");if(e&&e.parentNode!==i)return!1;if(null===e)return!1}return e},r=t=>!t||t.nodeType!==Node.ELEMENT_NODE||!!t.classList.contains("disabled")||(void 0!==t.disabled?t.disabled:t.hasAttribute("disabled")&&"false"!==t.getAttribute("disabled")),a=t=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof t.getRootNode){const e=t.getRootNode();return e instanceof ShadowRoot?e:null}return t instanceof ShadowRoot?t:t.parentNode?a(t.parentNode):null},l=()=>{},c=t=>{t.offsetHeight},h=()=>window.jQuery&&!document.body.hasAttribute("data-coreui-no-jquery")?window.jQuery:null,u=[],d=()=>"rtl"===document.documentElement.dir,f=t=>{var e;e=()=>{const e=h();if(e){const i=t.NAME,n=e.fn[i];e.fn[i]=t.jQueryInterface,e.fn[i].Constructor=t,e.fn[i].noConflict=()=>(e.fn[i]=n,t.jQueryInterface)}},"loading"===document.readyState?(u.length||document.addEventListener("DOMContentLoaded",(()=>{for(const t of u)t()})),u.push(e)):e()},p=(t,e=[],i=t)=>"function"==typeof t?t(...e):i,g=(e,n,s=!0)=>{if(!s)return void p(e);const o=(t=>{if(!t)return 0;let{transitionDuration:e,transitionDelay:i}=window.getComputedStyle(t);const n=Number.parseFloat(e),s=Number.parseFloat(i);return n||s?(e=e.split(",")[0],i=i.split(",")[0],1e3*(Number.parseFloat(e)+Number.parseFloat(i))):0})(n)+5;let r=!1;const a=({target:i})=>{i===n&&(r=!0,n.removeEventListener(t,a),p(e))};n.addEventListener(t,a),setTimeout((()=>{r||i(n)}),o)},m=(t,e,i,n)=>{const s=t.length;let o=t.indexOf(e);return-1===o?!i&&n?t[s-1]:t[0]:(o+=i?1:-1,n&&(o=(o+s)%s),t[Math.max(0,Math.min(o,s-1))])},_=/[^.]*(?=\..*)\.|.*/,b=/\..*/,v=/::\d+$/,y={};let w=1;const A={mouseenter:"mouseover",mouseleave:"mouseout"},E=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function C(t,e){return e&&`${e}::${w++}`||t.uidEvent||w++}function T(t){const e=C(t);return t.uidEvent=e,y[e]=y[e]||{},y[e]}function O(t,e,i=null){return Object.values(t).find((t=>t.callable===e&&t.delegationSelector===i))}function k(t,e,i){const n="string"==typeof e,s=n?i:e||i;let o=S(t);return E.has(o)||(o=t),[n,s,o]}function x(t,e,i,n,s){if("string"!=typeof e||!t)return;let[o,r,a]=k(e,i,n);if(e in A){const t=t=>function(e){if(!e.relatedTarget||e.relatedTarget!==e.delegateTarget&&!e.delegateTarget.contains(e.relatedTarget))return t.call(this,e)};r=t(r)}const l=T(t),c=l[a]||(l[a]={}),h=O(c,r,o?i:null);if(h)return void(h.oneOff=h.oneOff&&s);const u=C(r,e.replace(_,"")),d=o?function(t,e,i){return function n(s){const o=t.querySelectorAll(e);for(let{target:r}=s;r&&r!==this;r=r.parentNode)for(const a of o)if(a===r)return I(s,{delegateTarget:r}),n.oneOff&&N.off(t,s.type,e,i),i.apply(r,[s])}}(t,i,r):function(t,e){return function i(n){return I(n,{delegateTarget:t}),i.oneOff&&N.off(t,n.type,e),e.apply(t,[n])}}(t,r);d.delegationSelector=o?i:null,d.callable=r,d.oneOff=s,d.uidEvent=u,c[u]=d,t.addEventListener(a,d,o)}function L(t,e,i,n,s){const o=O(e[i],n,s);o&&(t.removeEventListener(i,o,Boolean(s)),delete e[i][o.uidEvent])}function D(t,e,i,n){const s=e[i]||{};for(const[o,r]of Object.entries(s))o.includes(n)&&L(t,e,i,r.callable,r.delegationSelector)}function S(t){return t=t.replace(b,""),A[t]||t}const N={on(t,e,i,n){x(t,e,i,n,!1)},one(t,e,i,n){x(t,e,i,n,!0)},off(t,e,i,n){if("string"!=typeof e||!t)return;const[s,o,r]=k(e,i,n),a=r!==e,l=T(t),c=l[r]||{},h=e.startsWith(".");if(void 0===o){if(h)for(const i of Object.keys(l))D(t,l,i,e.slice(1));for(const[i,n]of Object.entries(c)){const s=i.replace(v,"");a&&!e.includes(s)||L(t,l,r,n.callable,n.delegationSelector)}}else{if(!Object.keys(c).length)return;L(t,l,r,o,s?i:null)}},trigger(t,e,i){if("string"!=typeof e||!t)return null;const n=h();let s=null,o=!0,r=!0,a=!1;e!==S(e)&&n&&(s=n.Event(e,i),n(t).trigger(s),o=!s.isPropagationStopped(),r=!s.isImmediatePropagationStopped(),a=s.isDefaultPrevented());let l=new Event(e,{bubbles:o,cancelable:!0});return l=I(l,i),a&&l.preventDefault(),r&&t.dispatchEvent(l),l.defaultPrevented&&s&&s.preventDefault(),l}};function I(t,e={}){for(const[i,n]of Object.entries(e))try{t[i]=n}catch(e){Object.defineProperty(t,i,{configurable:!0,get:()=>n})}return t}const M=new Map,P={set(t,e,i){M.has(t)||M.set(t,new Map);const n=M.get(t);n.has(e)||0===n.size?n.set(e,i):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(n.keys())[0]}.`)},get:(t,e)=>M.has(t)&&M.get(t).get(e)||null,remove(t,e){if(!M.has(t))return;const i=M.get(t);i.delete(e),0===i.size&&M.delete(t)}};function j(t){if("true"===t)return!0;if("false"===t)return!1;if(t===Number(t).toString())return Number(t);if(""===t||"null"===t)return null;if("string"!=typeof t)return t;try{return JSON.parse(decodeURIComponent(t))}catch(e){return t}}function $(t){return t.replace(/[A-Z]/g,(t=>`-${t.toLowerCase()}`))}const F={setDataAttribute(t,e,i){t.setAttribute(`data-coreui-${$(e)}`,i)},removeDataAttribute(t,e){t.removeAttribute(`data-coreui-${$(e)}`)},getDataAttributes(t){if(!t)return{};const e={},i=Object.keys(t.dataset).filter((t=>t.startsWith("coreui")&&!t.startsWith("coreuiConfig")));for(const n of i){let i=n.replace(/^coreui/,"");i=i.charAt(0).toLowerCase()+i.slice(1,i.length),e[i]=j(t.dataset[n])}return e},getDataAttribute:(t,e)=>j(t.getAttribute(`data-coreui-${$(e)}`))};class H{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,e){const i=n(e)?F.getDataAttribute(e,"config"):{};return{...this.constructor.Default,..."object"==typeof i?i:{},...n(e)?F.getDataAttributes(e):{},..."object"==typeof t?t:{}}}_typeCheckConfig(t,e=this.constructor.DefaultType){for(const[s,o]of Object.entries(e)){const e=t[s],r=n(e)?"element":null==(i=e)?`${i}`:Object.prototype.toString.call(i).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(o).test(r))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${s}" provided type "${r}" but expected type "${o}".`)}var i}}class B extends H{constructor(t,e){super(),(t=s(t))&&(this._element=t,this._config=this._getConfig(e),P.set(this._element,this.constructor.DATA_KEY,this))}dispose(){P.remove(this._element,this.constructor.DATA_KEY),N.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,e,i=!0){g(t,e,i)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return P.get(s(t),this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,"object"==typeof e?e:null)}static get VERSION(){return"4.2.3"}static get DATA_KEY(){return`coreui.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const W=t=>{let i=t.getAttribute("data-coreui-target");if(!i||"#"===i){let e=t.getAttribute("href");if(!e||!e.includes("#")&&!e.startsWith("."))return null;e.includes("#")&&!e.startsWith("#")&&(e=`#${e.split("#")[1]}`),i=e&&"#"!==e?e.trim():null}return e(i)},z={find:(t,e=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(e,t)),findOne:(t,e=document.documentElement)=>Element.prototype.querySelector.call(e,t),children:(t,e)=>[].concat(...t.children).filter((t=>t.matches(e))),parents(t,e){const i=[];let n=t.parentNode.closest(e);for(;n;)i.push(n),n=n.parentNode.closest(e);return i},prev(t,e){let i=t.previousElementSibling;for(;i;){if(i.matches(e))return[i];i=i.previousElementSibling}return[]},next(t,e){let i=t.nextElementSibling;for(;i;){if(i.matches(e))return[i];i=i.nextElementSibling}return[]},focusableChildren(t){const e=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((t=>`${t}:not([tabindex^="-"])`)).join(",");return this.find(e,t).filter((t=>!r(t)&&o(t)))},getSelectorFromElement(t){const e=W(t);return e&&z.findOne(e)?e:null},getElementFromSelector(t){const e=W(t);return e?z.findOne(e):null},getMultipleElementsFromSelector(t){const e=W(t);return e?z.find(e):[]}},q=(t,e="hide")=>{const i=`click.dismiss${t.EVENT_KEY}`,n=t.NAME;N.on(document,i,`[data-coreui-dismiss="${n}"]`,(function(i){if(["A","AREA"].includes(this.tagName)&&i.preventDefault(),r(this))return;const s=z.getElementFromSelector(this)||this.closest(`.${n}`);t.getOrCreateInstance(s)[e]()}))};class R extends B{static get NAME(){return"alert"}close(){if(N.trigger(this._element,"close.coreui.alert").defaultPrevented)return;this._element.classList.remove("show");const t=this._element.classList.contains("fade");this._queueCallback((()=>this._destroyElement()),this._element,t)}_destroyElement(){this._element.remove(),N.trigger(this._element,"closed.coreui.alert"),this.dispose()}static jQueryInterface(t){return this.each((function(){const e=R.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}q(R,"close"),f(R);const V='[data-coreui-toggle="button"]';class U extends B{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(t){return this.each((function(){const e=U.getOrCreateInstance(this);"toggle"===t&&e[t]()}))}}N.on(document,"click.coreui.button.data-api",V,(t=>{t.preventDefault();const e=t.target.closest(V);U.getOrCreateInstance(e).toggle()})),f(U);const K={endCallback:null,leftCallback:null,rightCallback:null},Q={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class Y extends H{constructor(t,e){super(),this._element=t,t&&Y.isSupported()&&(this._config=this._getConfig(e),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return K}static get DefaultType(){return Q}static get NAME(){return"swipe"}dispose(){N.off(this._element,".coreui.swipe")}_start(t){this._supportPointerEvents?this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX):this._deltaX=t.touches[0].clientX}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),p(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=40)return;const e=t/this._deltaX;this._deltaX=0,e&&p(e>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(N.on(this._element,"pointerdown.coreui.swipe",(t=>this._start(t))),N.on(this._element,"pointerup.coreui.swipe",(t=>this._end(t))),this._element.classList.add("pointer-event")):(N.on(this._element,"touchstart.coreui.swipe",(t=>this._start(t))),N.on(this._element,"touchmove.coreui.swipe",(t=>this._move(t))),N.on(this._element,"touchend.coreui.swipe",(t=>this._end(t))))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&("pen"===t.pointerType||"touch"===t.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const X=".coreui.carousel",G="next",J="prev",Z="left",tt="right",et=`slide${X}`,it=`slid${X}`,nt=`keydown${X}`,st=`mouseenter${X}`,ot=`mouseleave${X}`,rt=`dragstart${X}`,at=`load${X}.data-api`,lt=`click${X}.data-api`,ct="carousel",ht="active",ut={ArrowLeft:tt,ArrowRight:Z},dt={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},ft={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class pt extends B{constructor(t,e){super(t,e),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=z.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===ct&&this.cycle()}static get Default(){return dt}static get DefaultType(){return ft}static get NAME(){return"carousel"}next(){this._slide(G)}nextWhenVisible(){!document.hidden&&o(this._element)&&this.next()}prev(){this._slide(J)}pause(){this._isSliding&&i(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval((()=>this.nextWhenVisible()),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?N.one(this._element,it,(()=>this.cycle())):this.cycle())}to(t){const e=this._getItems();if(t>e.length-1||t<0)return;if(this._isSliding)return void N.one(this._element,it,(()=>this.to(t)));const i=this._getItemIndex(this._getActive());if(i===t)return;const n=t>i?G:J;this._slide(n,e[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&N.on(this._element,nt,(t=>this._keydown(t))),"hover"===this._config.pause&&(N.on(this._element,st,(()=>this.pause())),N.on(this._element,ot,(()=>this._maybeEnableCycle()))),this._config.touch&&Y.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const t of z.find(".carousel-item img",this._element))N.on(t,rt,(t=>t.preventDefault()));const t={leftCallback:()=>this._slide(this._directionToOrder(Z)),rightCallback:()=>this._slide(this._directionToOrder(tt)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout((()=>this._maybeEnableCycle()),500+this._config.interval))}};this._swipeHelper=new Y(this._element,t)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const e=ut[t.key];e&&(t.preventDefault(),this._slide(this._directionToOrder(e)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const e=z.findOne(".active",this._indicatorsElement);e.classList.remove(ht),e.removeAttribute("aria-current");const i=z.findOne(`[data-coreui-slide-to="${t}"]`,this._indicatorsElement);i&&(i.classList.add(ht),i.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const e=Number.parseInt(t.getAttribute("data-coreui-interval"),10);this._config.interval=e||this._config.defaultInterval}_slide(t,e=null){if(this._isSliding)return;const i=this._getActive(),n=t===G,s=e||m(this._getItems(),i,n,this._config.wrap);if(s===i)return;const o=this._getItemIndex(s),r=e=>N.trigger(this._element,e,{relatedTarget:s,direction:this._orderToDirection(t),from:this._getItemIndex(i),to:o});if(r(et).defaultPrevented)return;if(!i||!s)return;const a=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=s;const l=n?"carousel-item-start":"carousel-item-end",h=n?"carousel-item-next":"carousel-item-prev";s.classList.add(h),c(s),i.classList.add(l),s.classList.add(l),this._queueCallback((()=>{s.classList.remove(l,h),s.classList.add(ht),i.classList.remove(ht,h,l),this._isSliding=!1,r(it)}),i,this._isAnimated()),a&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return z.findOne(".active.carousel-item",this._element)}_getItems(){return z.find(".carousel-item",this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return d()?t===Z?J:G:t===Z?G:J}_orderToDirection(t){return d()?t===J?Z:tt:t===J?tt:Z}static jQueryInterface(t){return this.each((function(){const e=pt.getOrCreateInstance(this,t);if("number"!=typeof t){if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}else e.to(t)}))}}N.on(document,lt,"[data-coreui-slide], [data-coreui-slide-to]",(function(t){const e=z.getElementFromSelector(this);if(!e||!e.classList.contains(ct))return;t.preventDefault();const i=pt.getOrCreateInstance(e),n=this.getAttribute("data-coreui-slide-to");return n?(i.to(n),void i._maybeEnableCycle()):"next"===F.getDataAttribute(this,"slide")?(i.next(),void i._maybeEnableCycle()):(i.prev(),void i._maybeEnableCycle())})),N.on(window,at,(()=>{const t=z.find('[data-coreui-ride="carousel"]');for(const e of t)pt.getOrCreateInstance(e)})),f(pt);const gt=".coreui.collapse",mt=`show${gt}`,_t=`shown${gt}`,bt=`hide${gt}`,vt=`hidden${gt}`,yt=`click${gt}.data-api`,wt="show",At="collapse",Et="collapsing",Ct='[data-coreui-toggle="collapse"]',Tt={parent:null,toggle:!0},Ot={parent:"(null|element)",toggle:"boolean"};class kt extends B{constructor(t,e){super(t,e),this._isTransitioning=!1,this._triggerArray=[];const i=z.find(Ct);for(const t of i){const e=z.getSelectorFromElement(t),i=z.find(e).filter((t=>t===this._element));null!==e&&i.length&&this._triggerArray.push(t)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Tt}static get DefaultType(){return Ot}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter((t=>t!==this._element)).map((t=>kt.getOrCreateInstance(t,{toggle:!1})))),t.length&&t[0]._isTransitioning)return;if(N.trigger(this._element,mt).defaultPrevented)return;for(const e of t)e.hide();const e=this._getDimension();this._element.classList.remove(At),this._element.classList.add(Et),this._element.style[e]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const i=`scroll${e[0].toUpperCase()+e.slice(1)}`;this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(Et),this._element.classList.add(At,wt),this._element.style[e]="",N.trigger(this._element,_t)}),this._element,!0),this._element.style[e]=`${this._element[i]}px`}hide(){if(this._isTransitioning||!this._isShown())return;if(N.trigger(this._element,bt).defaultPrevented)return;const t=this._getDimension();this._element.style[t]=`${this._element.getBoundingClientRect()[t]}px`,c(this._element),this._element.classList.add(Et),this._element.classList.remove(At,wt);for(const t of this._triggerArray){const e=z.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0,this._element.style[t]="",this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(Et),this._element.classList.add(At),N.trigger(this._element,vt)}),this._element,!0)}_isShown(t=this._element){return t.classList.contains(wt)}_configAfterMerge(t){return t.toggle=Boolean(t.toggle),t.parent=s(t.parent),t}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(Ct);for(const e of t){const t=z.getElementFromSelector(e);t&&this._addAriaAndCollapsedClass([e],this._isShown(t))}}_getFirstLevelChildren(t){const e=z.find(":scope .collapse .collapse",this._config.parent);return z.find(t,this._config.parent).filter((t=>!e.includes(t)))}_addAriaAndCollapsedClass(t,e){if(t.length)for(const i of t)i.classList.toggle("collapsed",!e),i.setAttribute("aria-expanded",e)}static jQueryInterface(t){const e={};return"string"==typeof t&&/show|hide/.test(t)&&(e.toggle=!1),this.each((function(){const i=kt.getOrCreateInstance(this,e);if("string"==typeof t){if(void 0===i[t])throw new TypeError(`No method named "${t}"`);i[t]()}}))}}N.on(document,yt,Ct,(function(t){("A"===t.target.tagName||t.delegateTarget&&"A"===t.delegateTarget.tagName)&&t.preventDefault();for(const t of z.getMultipleElementsFromSelector(this))kt.getOrCreateInstance(t,{toggle:!1}).toggle()})),f(kt);var xt="top",Lt="bottom",Dt="right",St="left",Nt="auto",It=[xt,Lt,Dt,St],Mt="start",Pt="end",jt="clippingParents",$t="viewport",Ft="popper",Ht="reference",Bt=It.reduce((function(t,e){return t.concat([e+"-"+Mt,e+"-"+Pt])}),[]),Wt=[].concat(It,[Nt]).reduce((function(t,e){return t.concat([e,e+"-"+Mt,e+"-"+Pt])}),[]),zt="beforeRead",qt="read",Rt="afterRead",Vt="beforeMain",Ut="main",Kt="afterMain",Qt="beforeWrite",Yt="write",Xt="afterWrite",Gt=[zt,qt,Rt,Vt,Ut,Kt,Qt,Yt,Xt];function Jt(t){return t?(t.nodeName||"").toLowerCase():null}function Zt(t){if(null==t)return window;if("[object Window]"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function te(t){return t instanceof Zt(t).Element||t instanceof Element}function ee(t){return t instanceof Zt(t).HTMLElement||t instanceof HTMLElement}function ie(t){return"undefined"!=typeof ShadowRoot&&(t instanceof Zt(t).ShadowRoot||t instanceof ShadowRoot)}const ne={name:"applyStyles",enabled:!0,phase:"write",fn:function(t){var e=t.state;Object.keys(e.elements).forEach((function(t){var i=e.styles[t]||{},n=e.attributes[t]||{},s=e.elements[t];ee(s)&&Jt(s)&&(Object.assign(s.style,i),Object.keys(n).forEach((function(t){var e=n[t];!1===e?s.removeAttribute(t):s.setAttribute(t,!0===e?"":e)})))}))},effect:function(t){var e=t.state,i={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,i.popper),e.styles=i,e.elements.arrow&&Object.assign(e.elements.arrow.style,i.arrow),function(){Object.keys(e.elements).forEach((function(t){var n=e.elements[t],s=e.attributes[t]||{},o=Object.keys(e.styles.hasOwnProperty(t)?e.styles[t]:i[t]).reduce((function(t,e){return t[e]="",t}),{});ee(n)&&Jt(n)&&(Object.assign(n.style,o),Object.keys(s).forEach((function(t){n.removeAttribute(t)})))}))}},requires:["computeStyles"]};function se(t){return t.split("-")[0]}var oe=Math.max,re=Math.min,ae=Math.round;function le(){var t=navigator.userAgentData;return null!=t&&t.brands?t.brands.map((function(t){return t.brand+"/"+t.version})).join(" "):navigator.userAgent}function ce(){return!/^((?!chrome|android).)*safari/i.test(le())}function he(t,e,i){void 0===e&&(e=!1),void 0===i&&(i=!1);var n=t.getBoundingClientRect(),s=1,o=1;e&&ee(t)&&(s=t.offsetWidth>0&&ae(n.width)/t.offsetWidth||1,o=t.offsetHeight>0&&ae(n.height)/t.offsetHeight||1);var r=(te(t)?Zt(t):window).visualViewport,a=!ce()&&i,l=(n.left+(a&&r?r.offsetLeft:0))/s,c=(n.top+(a&&r?r.offsetTop:0))/o,h=n.width/s,u=n.height/o;return{width:h,height:u,top:c,right:l+h,bottom:c+u,left:l,x:l,y:c}}function ue(t){var e=he(t),i=t.offsetWidth,n=t.offsetHeight;return Math.abs(e.width-i)<=1&&(i=e.width),Math.abs(e.height-n)<=1&&(n=e.height),{x:t.offsetLeft,y:t.offsetTop,width:i,height:n}}function de(t,e){var i=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(i&&ie(i)){var n=e;do{if(n&&t.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function fe(t){return Zt(t).getComputedStyle(t)}function pe(t){return["table","td","th"].indexOf(Jt(t))>=0}function ge(t){return((te(t)?t.ownerDocument:t.document)||window.document).documentElement}function me(t){return"html"===Jt(t)?t:t.assignedSlot||t.parentNode||(ie(t)?t.host:null)||ge(t)}function _e(t){return ee(t)&&"fixed"!==fe(t).position?t.offsetParent:null}function be(t){for(var e=Zt(t),i=_e(t);i&&pe(i)&&"static"===fe(i).position;)i=_e(i);return i&&("html"===Jt(i)||"body"===Jt(i)&&"static"===fe(i).position)?e:i||function(t){var e=/firefox/i.test(le());if(/Trident/i.test(le())&&ee(t)&&"fixed"===fe(t).position)return null;var i=me(t);for(ie(i)&&(i=i.host);ee(i)&&["html","body"].indexOf(Jt(i))<0;){var n=fe(i);if("none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||-1!==["transform","perspective"].indexOf(n.willChange)||e&&"filter"===n.willChange||e&&n.filter&&"none"!==n.filter)return i;i=i.parentNode}return null}(t)||e}function ve(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function ye(t,e,i){return oe(t,re(e,i))}function we(t){return Object.assign({},{top:0,right:0,bottom:0,left:0},t)}function Ae(t,e){return e.reduce((function(e,i){return e[i]=t,e}),{})}const Ee={name:"arrow",enabled:!0,phase:"main",fn:function(t){var e,i=t.state,n=t.name,s=t.options,o=i.elements.arrow,r=i.modifiersData.popperOffsets,a=se(i.placement),l=ve(a),c=[St,Dt].indexOf(a)>=0?"height":"width";if(o&&r){var h=function(t,e){return we("number"!=typeof(t="function"==typeof t?t(Object.assign({},e.rects,{placement:e.placement})):t)?t:Ae(t,It))}(s.padding,i),u=ue(o),d="y"===l?xt:St,f="y"===l?Lt:Dt,p=i.rects.reference[c]+i.rects.reference[l]-r[l]-i.rects.popper[c],g=r[l]-i.rects.reference[l],m=be(o),_=m?"y"===l?m.clientHeight||0:m.clientWidth||0:0,b=p/2-g/2,v=h[d],y=_-u[c]-h[f],w=_/2-u[c]/2+b,A=ye(v,w,y),E=l;i.modifiersData[n]=((e={})[E]=A,e.centerOffset=A-w,e)}},effect:function(t){var e=t.state,i=t.options.element,n=void 0===i?"[data-popper-arrow]":i;null!=n&&("string"!=typeof n||(n=e.elements.popper.querySelector(n)))&&de(e.elements.popper,n)&&(e.elements.arrow=n)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Ce(t){return t.split("-")[1]}var Te={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Oe(t){var e,i=t.popper,n=t.popperRect,s=t.placement,o=t.variation,r=t.offsets,a=t.position,l=t.gpuAcceleration,c=t.adaptive,h=t.roundOffsets,u=t.isFixed,d=r.x,f=void 0===d?0:d,p=r.y,g=void 0===p?0:p,m="function"==typeof h?h({x:f,y:g}):{x:f,y:g};f=m.x,g=m.y;var _=r.hasOwnProperty("x"),b=r.hasOwnProperty("y"),v=St,y=xt,w=window;if(c){var A=be(i),E="clientHeight",C="clientWidth";A===Zt(i)&&"static"!==fe(A=ge(i)).position&&"absolute"===a&&(E="scrollHeight",C="scrollWidth"),(s===xt||(s===St||s===Dt)&&o===Pt)&&(y=Lt,g-=(u&&A===w&&w.visualViewport?w.visualViewport.height:A[E])-n.height,g*=l?1:-1),s!==St&&(s!==xt&&s!==Lt||o!==Pt)||(v=Dt,f-=(u&&A===w&&w.visualViewport?w.visualViewport.width:A[C])-n.width,f*=l?1:-1)}var T,O=Object.assign({position:a},c&&Te),k=!0===h?function(t){var e=t.x,i=t.y,n=window.devicePixelRatio||1;return{x:ae(e*n)/n||0,y:ae(i*n)/n||0}}({x:f,y:g}):{x:f,y:g};return f=k.x,g=k.y,l?Object.assign({},O,((T={})[y]=b?"0":"",T[v]=_?"0":"",T.transform=(w.devicePixelRatio||1)<=1?"translate("+f+"px, "+g+"px)":"translate3d("+f+"px, "+g+"px, 0)",T)):Object.assign({},O,((e={})[y]=b?g+"px":"",e[v]=_?f+"px":"",e.transform="",e))}const ke={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(t){var e=t.state,i=t.options,n=i.gpuAcceleration,s=void 0===n||n,o=i.adaptive,r=void 0===o||o,a=i.roundOffsets,l=void 0===a||a,c={placement:se(e.placement),variation:Ce(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:s,isFixed:"fixed"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,Oe(Object.assign({},c,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:r,roundOffsets:l})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,Oe(Object.assign({},c,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}};var xe={passive:!0};const Le={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(t){var e=t.state,i=t.instance,n=t.options,s=n.scroll,o=void 0===s||s,r=n.resize,a=void 0===r||r,l=Zt(e.elements.popper),c=[].concat(e.scrollParents.reference,e.scrollParents.popper);return o&&c.forEach((function(t){t.addEventListener("scroll",i.update,xe)})),a&&l.addEventListener("resize",i.update,xe),function(){o&&c.forEach((function(t){t.removeEventListener("scroll",i.update,xe)})),a&&l.removeEventListener("resize",i.update,xe)}},data:{}};var De={left:"right",right:"left",bottom:"top",top:"bottom"};function Se(t){return t.replace(/left|right|bottom|top/g,(function(t){return De[t]}))}var Ne={start:"end",end:"start"};function Ie(t){return t.replace(/start|end/g,(function(t){return Ne[t]}))}function Me(t){var e=Zt(t);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Pe(t){return he(ge(t)).left+Me(t).scrollLeft}function je(t){var e=fe(t),i=e.overflow,n=e.overflowX,s=e.overflowY;return/auto|scroll|overlay|hidden/.test(i+s+n)}function $e(t){return["html","body","#document"].indexOf(Jt(t))>=0?t.ownerDocument.body:ee(t)&&je(t)?t:$e(me(t))}function Fe(t,e){var i;void 0===e&&(e=[]);var n=$e(t),s=n===(null==(i=t.ownerDocument)?void 0:i.body),o=Zt(n),r=s?[o].concat(o.visualViewport||[],je(n)?n:[]):n,a=e.concat(r);return s?a:a.concat(Fe(me(r)))}function He(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function Be(t,e,i){return e===$t?He(function(t,e){var i=Zt(t),n=ge(t),s=i.visualViewport,o=n.clientWidth,r=n.clientHeight,a=0,l=0;if(s){o=s.width,r=s.height;var c=ce();(c||!c&&"fixed"===e)&&(a=s.offsetLeft,l=s.offsetTop)}return{width:o,height:r,x:a+Pe(t),y:l}}(t,i)):te(e)?function(t,e){var i=he(t,!1,"fixed"===e);return i.top=i.top+t.clientTop,i.left=i.left+t.clientLeft,i.bottom=i.top+t.clientHeight,i.right=i.left+t.clientWidth,i.width=t.clientWidth,i.height=t.clientHeight,i.x=i.left,i.y=i.top,i}(e,i):He(function(t){var e,i=ge(t),n=Me(t),s=null==(e=t.ownerDocument)?void 0:e.body,o=oe(i.scrollWidth,i.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),r=oe(i.scrollHeight,i.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),a=-n.scrollLeft+Pe(t),l=-n.scrollTop;return"rtl"===fe(s||i).direction&&(a+=oe(i.clientWidth,s?s.clientWidth:0)-o),{width:o,height:r,x:a,y:l}}(ge(t)))}function We(t){var e,i=t.reference,n=t.element,s=t.placement,o=s?se(s):null,r=s?Ce(s):null,a=i.x+i.width/2-n.width/2,l=i.y+i.height/2-n.height/2;switch(o){case xt:e={x:a,y:i.y-n.height};break;case Lt:e={x:a,y:i.y+i.height};break;case Dt:e={x:i.x+i.width,y:l};break;case St:e={x:i.x-n.width,y:l};break;default:e={x:i.x,y:i.y}}var c=o?ve(o):null;if(null!=c){var h="y"===c?"height":"width";switch(r){case Mt:e[c]=e[c]-(i[h]/2-n[h]/2);break;case Pt:e[c]=e[c]+(i[h]/2-n[h]/2)}}return e}function ze(t,e){void 0===e&&(e={});var i=e,n=i.placement,s=void 0===n?t.placement:n,o=i.strategy,r=void 0===o?t.strategy:o,a=i.boundary,l=void 0===a?jt:a,c=i.rootBoundary,h=void 0===c?$t:c,u=i.elementContext,d=void 0===u?Ft:u,f=i.altBoundary,p=void 0!==f&&f,g=i.padding,m=void 0===g?0:g,_=we("number"!=typeof m?m:Ae(m,It)),b=d===Ft?Ht:Ft,v=t.rects.popper,y=t.elements[p?b:d],w=function(t,e,i,n){var s="clippingParents"===e?function(t){var e=Fe(me(t)),i=["absolute","fixed"].indexOf(fe(t).position)>=0&&ee(t)?be(t):t;return te(i)?e.filter((function(t){return te(t)&&de(t,i)&&"body"!==Jt(t)})):[]}(t):[].concat(e),o=[].concat(s,[i]),r=o[0],a=o.reduce((function(e,i){var s=Be(t,i,n);return e.top=oe(s.top,e.top),e.right=re(s.right,e.right),e.bottom=re(s.bottom,e.bottom),e.left=oe(s.left,e.left),e}),Be(t,r,n));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}(te(y)?y:y.contextElement||ge(t.elements.popper),l,h,r),A=he(t.elements.reference),E=We({reference:A,element:v,strategy:"absolute",placement:s}),C=He(Object.assign({},v,E)),T=d===Ft?C:A,O={top:w.top-T.top+_.top,bottom:T.bottom-w.bottom+_.bottom,left:w.left-T.left+_.left,right:T.right-w.right+_.right},k=t.modifiersData.offset;if(d===Ft&&k){var x=k[s];Object.keys(O).forEach((function(t){var e=[Dt,Lt].indexOf(t)>=0?1:-1,i=[xt,Lt].indexOf(t)>=0?"y":"x";O[t]+=x[i]*e}))}return O}function qe(t,e){void 0===e&&(e={});var i=e,n=i.placement,s=i.boundary,o=i.rootBoundary,r=i.padding,a=i.flipVariations,l=i.allowedAutoPlacements,c=void 0===l?Wt:l,h=Ce(n),u=h?a?Bt:Bt.filter((function(t){return Ce(t)===h})):It,d=u.filter((function(t){return c.indexOf(t)>=0}));0===d.length&&(d=u);var f=d.reduce((function(e,i){return e[i]=ze(t,{placement:i,boundary:s,rootBoundary:o,padding:r})[se(i)],e}),{});return Object.keys(f).sort((function(t,e){return f[t]-f[e]}))}const Re={name:"flip",enabled:!0,phase:"main",fn:function(t){var e=t.state,i=t.options,n=t.name;if(!e.modifiersData[n]._skip){for(var s=i.mainAxis,o=void 0===s||s,r=i.altAxis,a=void 0===r||r,l=i.fallbackPlacements,c=i.padding,h=i.boundary,u=i.rootBoundary,d=i.altBoundary,f=i.flipVariations,p=void 0===f||f,g=i.allowedAutoPlacements,m=e.options.placement,_=se(m),b=l||(_!==m&&p?function(t){if(se(t)===Nt)return[];var e=Se(t);return[Ie(t),e,Ie(e)]}(m):[Se(m)]),v=[m].concat(b).reduce((function(t,i){return t.concat(se(i)===Nt?qe(e,{placement:i,boundary:h,rootBoundary:u,padding:c,flipVariations:p,allowedAutoPlacements:g}):i)}),[]),y=e.rects.reference,w=e.rects.popper,A=new Map,E=!0,C=v[0],T=0;T<v.length;T++){var O=v[T],k=se(O),x=Ce(O)===Mt,L=[xt,Lt].indexOf(k)>=0,D=L?"width":"height",S=ze(e,{placement:O,boundary:h,rootBoundary:u,altBoundary:d,padding:c}),N=L?x?Dt:St:x?Lt:xt;y[D]>w[D]&&(N=Se(N));var I=Se(N),M=[];if(o&&M.push(S[k]<=0),a&&M.push(S[N]<=0,S[I]<=0),M.every((function(t){return t}))){C=O,E=!1;break}A.set(O,M)}if(E)for(var P=function(t){var e=v.find((function(e){var i=A.get(e);if(i)return i.slice(0,t).every((function(t){return t}))}));if(e)return C=e,"break"},j=p?3:1;j>0&&"break"!==P(j);j--);e.placement!==C&&(e.modifiersData[n]._skip=!0,e.placement=C,e.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function Ve(t,e,i){return void 0===i&&(i={x:0,y:0}),{top:t.top-e.height-i.y,right:t.right-e.width+i.x,bottom:t.bottom-e.height+i.y,left:t.left-e.width-i.x}}function Ue(t){return[xt,Dt,Lt,St].some((function(e){return t[e]>=0}))}const Ke={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(t){var e=t.state,i=t.name,n=e.rects.reference,s=e.rects.popper,o=e.modifiersData.preventOverflow,r=ze(e,{elementContext:"reference"}),a=ze(e,{altBoundary:!0}),l=Ve(r,n),c=Ve(a,s,o),h=Ue(l),u=Ue(c);e.modifiersData[i]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:h,hasPopperEscaped:u},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":h,"data-popper-escaped":u})}},Qe={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var e=t.state,i=t.options,n=t.name,s=i.offset,o=void 0===s?[0,0]:s,r=Wt.reduce((function(t,i){return t[i]=function(t,e,i){var n=se(t),s=[St,xt].indexOf(n)>=0?-1:1,o="function"==typeof i?i(Object.assign({},e,{placement:t})):i,r=o[0],a=o[1];return r=r||0,a=(a||0)*s,[St,Dt].indexOf(n)>=0?{x:a,y:r}:{x:r,y:a}}(i,e.rects,o),t}),{}),a=r[e.placement],l=a.x,c=a.y;null!=e.modifiersData.popperOffsets&&(e.modifiersData.popperOffsets.x+=l,e.modifiersData.popperOffsets.y+=c),e.modifiersData[n]=r}},Ye={name:"popperOffsets",enabled:!0,phase:"read",fn:function(t){var e=t.state,i=t.name;e.modifiersData[i]=We({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})},data:{}},Xe={name:"preventOverflow",enabled:!0,phase:"main",fn:function(t){var e=t.state,i=t.options,n=t.name,s=i.mainAxis,o=void 0===s||s,r=i.altAxis,a=void 0!==r&&r,l=i.boundary,c=i.rootBoundary,h=i.altBoundary,u=i.padding,d=i.tether,f=void 0===d||d,p=i.tetherOffset,g=void 0===p?0:p,m=ze(e,{boundary:l,rootBoundary:c,padding:u,altBoundary:h}),_=se(e.placement),b=Ce(e.placement),v=!b,y=ve(_),w="x"===y?"y":"x",A=e.modifiersData.popperOffsets,E=e.rects.reference,C=e.rects.popper,T="function"==typeof g?g(Object.assign({},e.rects,{placement:e.placement})):g,O="number"==typeof T?{mainAxis:T,altAxis:T}:Object.assign({mainAxis:0,altAxis:0},T),k=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,x={x:0,y:0};if(A){if(o){var L,D="y"===y?xt:St,S="y"===y?Lt:Dt,N="y"===y?"height":"width",I=A[y],M=I+m[D],P=I-m[S],j=f?-C[N]/2:0,$=b===Mt?E[N]:C[N],F=b===Mt?-C[N]:-E[N],H=e.elements.arrow,B=f&&H?ue(H):{width:0,height:0},W=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},z=W[D],q=W[S],R=ye(0,E[N],B[N]),V=v?E[N]/2-j-R-z-O.mainAxis:$-R-z-O.mainAxis,U=v?-E[N]/2+j+R+q+O.mainAxis:F+R+q+O.mainAxis,K=e.elements.arrow&&be(e.elements.arrow),Q=K?"y"===y?K.clientTop||0:K.clientLeft||0:0,Y=null!=(L=null==k?void 0:k[y])?L:0,X=I+U-Y,G=ye(f?re(M,I+V-Y-Q):M,I,f?oe(P,X):P);A[y]=G,x[y]=G-I}if(a){var J,Z="x"===y?xt:St,tt="x"===y?Lt:Dt,et=A[w],it="y"===w?"height":"width",nt=et+m[Z],st=et-m[tt],ot=-1!==[xt,St].indexOf(_),rt=null!=(J=null==k?void 0:k[w])?J:0,at=ot?nt:et-E[it]-C[it]-rt+O.altAxis,lt=ot?et+E[it]+C[it]-rt-O.altAxis:st,ct=f&&ot?function(t,e,i){var n=ye(t,e,i);return n>i?i:n}(at,et,lt):ye(f?at:nt,et,f?lt:st);A[w]=ct,x[w]=ct-et}e.modifiersData[n]=x}},requiresIfExists:["offset"]};function Ge(t,e,i){void 0===i&&(i=!1);var n,s,o=ee(e),r=ee(e)&&function(t){var e=t.getBoundingClientRect(),i=ae(e.width)/t.offsetWidth||1,n=ae(e.height)/t.offsetHeight||1;return 1!==i||1!==n}(e),a=ge(e),l=he(t,r,i),c={scrollLeft:0,scrollTop:0},h={x:0,y:0};return(o||!o&&!i)&&(("body"!==Jt(e)||je(a))&&(c=(n=e)!==Zt(n)&&ee(n)?{scrollLeft:(s=n).scrollLeft,scrollTop:s.scrollTop}:Me(n)),ee(e)?((h=he(e,!0)).x+=e.clientLeft,h.y+=e.clientTop):a&&(h.x=Pe(a))),{x:l.left+c.scrollLeft-h.x,y:l.top+c.scrollTop-h.y,width:l.width,height:l.height}}function Je(t){var e=new Map,i=new Set,n=[];function s(t){i.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach((function(t){if(!i.has(t)){var n=e.get(t);n&&s(n)}})),n.push(t)}return t.forEach((function(t){e.set(t.name,t)})),t.forEach((function(t){i.has(t.name)||s(t)})),n}var Ze={placement:"bottom",modifiers:[],strategy:"absolute"};function ti(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return!e.some((function(t){return!(t&&"function"==typeof t.getBoundingClientRect)}))}function ei(t){void 0===t&&(t={});var e=t,i=e.defaultModifiers,n=void 0===i?[]:i,s=e.defaultOptions,o=void 0===s?Ze:s;return function(t,e,i){void 0===i&&(i=o);var s,r,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},Ze,o),modifiersData:{},elements:{reference:t,popper:e},attributes:{},styles:{}},l=[],c=!1,h={state:a,setOptions:function(i){var s="function"==typeof i?i(a.options):i;u(),a.options=Object.assign({},o,a.options,s),a.scrollParents={reference:te(t)?Fe(t):t.contextElement?Fe(t.contextElement):[],popper:Fe(e)};var r,c,d=function(t){var e=Je(t);return Gt.reduce((function(t,i){return t.concat(e.filter((function(t){return t.phase===i})))}),[])}((r=[].concat(n,a.options.modifiers),c=r.reduce((function(t,e){var i=t[e.name];return t[e.name]=i?Object.assign({},i,e,{options:Object.assign({},i.options,e.options),data:Object.assign({},i.data,e.data)}):e,t}),{}),Object.keys(c).map((function(t){return c[t]}))));return a.orderedModifiers=d.filter((function(t){return t.enabled})),a.orderedModifiers.forEach((function(t){var e=t.name,i=t.options,n=void 0===i?{}:i,s=t.effect;if("function"==typeof s){var o=s({state:a,name:e,instance:h,options:n});l.push(o||function(){})}})),h.update()},forceUpdate:function(){if(!c){var t=a.elements,e=t.reference,i=t.popper;if(ti(e,i)){a.rects={reference:Ge(e,be(i),"fixed"===a.options.strategy),popper:ue(i)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach((function(t){return a.modifiersData[t.name]=Object.assign({},t.data)}));for(var n=0;n<a.orderedModifiers.length;n++)if(!0!==a.reset){var s=a.orderedModifiers[n],o=s.fn,r=s.options,l=void 0===r?{}:r,u=s.name;"function"==typeof o&&(a=o({state:a,options:l,name:u,instance:h})||a)}else a.reset=!1,n=-1}}},update:(s=function(){return new Promise((function(t){h.forceUpdate(),t(a)}))},function(){return r||(r=new Promise((function(t){Promise.resolve().then((function(){r=void 0,t(s())}))}))),r}),destroy:function(){u(),c=!0}};if(!ti(t,e))return h;function u(){l.forEach((function(t){return t()})),l=[]}return h.setOptions(i).then((function(t){!c&&i.onFirstUpdate&&i.onFirstUpdate(t)})),h}}var ii=ei(),ni=ei({defaultModifiers:[Le,Ye,ke,ne]}),si=ei({defaultModifiers:[Le,Ye,ke,ne,Qe,Re,Xe,Ee,Ke]});const oi=Object.freeze(Object.defineProperty({__proto__:null,popperGenerator:ei,detectOverflow:ze,createPopperBase:ii,createPopper:si,createPopperLite:ni,top:xt,bottom:Lt,right:Dt,left:St,auto:Nt,basePlacements:It,start:Mt,end:Pt,clippingParents:jt,viewport:$t,popper:Ft,reference:Ht,variationPlacements:Bt,placements:Wt,beforeRead:zt,read:qt,afterRead:Rt,beforeMain:Vt,main:Ut,afterMain:Kt,beforeWrite:Qt,write:Yt,afterWrite:Xt,modifierPhases:Gt,applyStyles:ne,arrow:Ee,computeStyles:ke,eventListeners:Le,flip:Re,hide:Ke,offset:Qe,popperOffsets:Ye,preventOverflow:Xe},Symbol.toStringTag,{value:"Module"})),ri="dropdown",ai=".coreui.dropdown",li="ArrowUp",ci="ArrowDown",hi=`hide${ai}`,ui=`hidden${ai}`,di=`show${ai}`,fi=`shown${ai}`,pi=`click${ai}.data-api`,gi=`keydown${ai}.data-api`,mi=`keyup${ai}.data-api`,_i="show",bi='[data-coreui-toggle="dropdown"]:not(.disabled):not(:disabled)',vi=`${bi}.show`,yi=".dropdown-menu",wi=d()?"top-end":"top-start",Ai=d()?"top-start":"top-end",Ei=d()?"bottom-end":"bottom-start",Ci=d()?"bottom-start":"bottom-end",Ti=d()?"left-start":"right-start",Oi=d()?"right-start":"left-start",ki={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},xi={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class Li extends B{constructor(t,e){super(t,e),this._popper=null,this._parent=this._element.parentNode,this._menu=z.next(this._element,yi)[0]||z.prev(this._element,yi)[0]||z.findOne(yi,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return ki}static get DefaultType(){return xi}static get NAME(){return ri}toggle(){return this._isShown()?this.hide():this.show()}show(){if(r(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!N.trigger(this._element,di,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const t of[].concat(...document.body.children))N.on(t,"mouseover",l);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(_i),this._element.classList.add(_i),N.trigger(this._element,fi,t)}}hide(){if(r(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!N.trigger(this._element,hi,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))N.off(t,"mouseover",l);this._popper&&this._popper.destroy(),this._menu.classList.remove(_i),this._element.classList.remove(_i),this._element.setAttribute("aria-expanded","false"),F.removeDataAttribute(this._menu,"popper"),N.trigger(this._element,ui,t)}}_getConfig(t){if("object"==typeof(t=super._getConfig(t)).reference&&!n(t.reference)&&"function"!=typeof t.reference.getBoundingClientRect)throw new TypeError(`${ri.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(void 0===oi)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let t=this._element;"parent"===this._config.reference?t=this._parent:n(this._config.reference)?t=s(this._config.reference):"object"==typeof this._config.reference&&(t=this._config.reference);const e=this._getPopperConfig();this._popper=si(t,this._menu,e)}_isShown(){return this._menu.classList.contains(_i)}_getPlacement(){const t=this._parent;if(t.classList.contains("dropend"))return Ti;if(t.classList.contains("dropstart"))return Oi;if(t.classList.contains("dropup-center"))return"top";if(t.classList.contains("dropdown-center"))return"bottom";const e="end"===getComputedStyle(this._menu).getPropertyValue("--cui-position").trim();return t.classList.contains("dropup")?e?Ai:wi:e?Ci:Ei}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map((t=>Number.parseInt(t,10))):"function"==typeof t?e=>t(e,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(F.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...p(this._config.popperConfig,[t])}}_selectMenuItem({key:t,target:e}){const i=z.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter((t=>o(t)));i.length&&m(i,e,t===ci,!i.includes(e)).focus()}static jQueryInterface(t){return this.each((function(){const e=Li.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}static clearMenus(t){if(2===t.button||"keyup"===t.type&&"Tab"!==t.key)return;const e=z.find(vi);for(const i of e){const e=Li.getInstance(i);if(!e||!1===e._config.autoClose)continue;const n=t.composedPath(),s=n.includes(e._menu);if(n.includes(e._element)||"inside"===e._config.autoClose&&!s||"outside"===e._config.autoClose&&s)continue;if(e._menu.contains(t.target)&&("keyup"===t.type&&"Tab"===t.key||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const o={relatedTarget:e._element};"click"===t.type&&(o.clickEvent=t),e._completeHide(o)}}static dataApiKeydownHandler(t){const e=/input|textarea/i.test(t.target.tagName),i="Escape"===t.key,n=[li,ci].includes(t.key);if(!n&&!i)return;if(e&&!i)return;t.preventDefault();const s=this.matches(bi)?this:z.prev(this,bi)[0]||z.next(this,bi)[0]||z.findOne(bi,t.delegateTarget.parentNode),o=Li.getOrCreateInstance(s);if(n)return t.stopPropagation(),o.show(),void o._selectMenuItem(t);o._isShown()&&(t.stopPropagation(),o.hide(),s.focus())}}N.on(document,gi,bi,Li.dataApiKeydownHandler),N.on(document,gi,yi,Li.dataApiKeydownHandler),N.on(document,pi,Li.clearMenus),N.on(document,mi,Li.clearMenus),N.on(document,pi,bi,(function(t){t.preventDefault(),Li.getOrCreateInstance(this).toggle()})),f(Li);const Di=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Si=".sticky-top",Ni="padding-right",Ii="margin-right";class Mi{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,Ni,(e=>e+t)),this._setElementAttributes(Di,Ni,(e=>e+t)),this._setElementAttributes(Si,Ii,(e=>e-t))}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,Ni),this._resetElementAttributes(Di,Ni),this._resetElementAttributes(Si,Ii)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,e,i){const n=this.getWidth();this._applyManipulationCallback(t,(t=>{if(t!==this._element&&window.innerWidth>t.clientWidth+n)return;this._saveInitialAttribute(t,e);const s=window.getComputedStyle(t).getPropertyValue(e);t.style.setProperty(e,`${i(Number.parseFloat(s))}px`)}))}_saveInitialAttribute(t,e){const i=t.style.getPropertyValue(e);i&&F.setDataAttribute(t,e,i)}_resetElementAttributes(t,e){this._applyManipulationCallback(t,(t=>{const i=F.getDataAttribute(t,e);null!==i?(F.removeDataAttribute(t,e),t.style.setProperty(e,i)):t.style.removeProperty(e)}))}_applyManipulationCallback(t,e){if(n(t))e(t);else for(const i of z.find(t,this._element))e(i)}}const Pi="show",ji="mousedown.coreui.backdrop",$i={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},Fi={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class Hi extends H{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return $i}static get DefaultType(){return Fi}static get NAME(){return"backdrop"}show(t){if(!this._config.isVisible)return void p(t);this._append();const e=this._getElement();this._config.isAnimated&&c(e),e.classList.add(Pi),this._emulateAnimation((()=>{p(t)}))}hide(t){this._config.isVisible?(this._getElement().classList.remove(Pi),this._emulateAnimation((()=>{this.dispose(),p(t)}))):p(t)}dispose(){this._isAppended&&(N.off(this._element,ji),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add("fade"),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=s(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),N.on(t,ji,(()=>{p(this._config.clickCallback)})),this._isAppended=!0}_emulateAnimation(t){g(t,this._getElement(),this._config.isAnimated)}}const Bi=".coreui.focustrap",Wi=`focusin${Bi}`,zi=`keydown.tab${Bi}`,qi="backward",Ri={autofocus:!0,trapElement:null},Vi={autofocus:"boolean",trapElement:"element"};class Ui extends H{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return Ri}static get DefaultType(){return Vi}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),N.off(document,Bi),N.on(document,Wi,(t=>this._handleFocusin(t))),N.on(document,zi,(t=>this._handleKeydown(t))),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,N.off(document,Bi))}_handleFocusin(t){const{trapElement:e}=this._config;if(t.target===document||t.target===e||e.contains(t.target))return;const i=z.focusableChildren(e);0===i.length?e.focus():this._lastTabNavDirection===qi?i[i.length-1].focus():i[0].focus()}_handleKeydown(t){"Tab"===t.key&&(this._lastTabNavDirection=t.shiftKey?qi:"forward")}}const Ki="hidden.coreui.modal",Qi="show.coreui.modal",Yi="modal-open",Xi="show",Gi="modal-static",Ji={backdrop:!0,focus:!0,keyboard:!0},Zi={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class tn extends B{constructor(t,e){super(t,e),this._dialog=z.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new Mi,this._addEventListeners()}static get Default(){return Ji}static get DefaultType(){return Zi}static get NAME(){return"modal"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||N.trigger(this._element,Qi,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(Yi),this._adjustDialog(),this._backdrop.show((()=>this._showElement(t))))}hide(){this._isShown&&!this._isTransitioning&&(N.trigger(this._element,"hide.coreui.modal").defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Xi),this._queueCallback((()=>this._hideModal()),this._element,this._isAnimated())))}dispose(){for(const t of[window,this._dialog])N.off(t,".coreui.modal");this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new Hi({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new Ui({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const e=z.findOne(".modal-body",this._dialog);e&&(e.scrollTop=0),c(this._element),this._element.classList.add(Xi),this._queueCallback((()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,N.trigger(this._element,"shown.coreui.modal",{relatedTarget:t})}),this._dialog,this._isAnimated())}_addEventListeners(){N.on(this._element,"keydown.dismiss.coreui.modal",(t=>{if("Escape"===t.key)return this._config.keyboard?(t.preventDefault(),void this.hide()):void this._triggerBackdropTransition()})),N.on(window,"resize.coreui.modal",(()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()})),N.on(this._element,"mousedown.dismiss.coreui.modal",(t=>{N.one(this._element,"click.dismiss.coreui.modal",(e=>{this._element===t.target&&this._element===e.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())}))}))}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide((()=>{document.body.classList.remove(Yi),this._resetAdjustments(),this._scrollBar.reset(),N.trigger(this._element,Ki)}))}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(N.trigger(this._element,"hidePrevented.coreui.modal").defaultPrevented)return;const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._element.style.overflowY;"hidden"===e||this._element.classList.contains(Gi)||(t||(this._element.style.overflowY="hidden"),this._element.classList.add(Gi),this._queueCallback((()=>{this._element.classList.remove(Gi),this._queueCallback((()=>{this._element.style.overflowY=e}),this._dialog)}),this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),i=e>0;if(i&&!t){const t=d()?"paddingLeft":"paddingRight";this._element.style[t]=`${e}px`}if(!i&&t){const t=d()?"paddingRight":"paddingLeft";this._element.style[t]=`${e}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,e){return this.each((function(){const i=tn.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===i[t])throw new TypeError(`No method named "${t}"`);i[t](e)}}))}}N.on(document,"click.coreui.modal.data-api",'[data-coreui-toggle="modal"]',(function(t){const e=z.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&t.preventDefault(),N.one(e,Qi,(t=>{t.defaultPrevented||N.one(e,Ki,(()=>{o(this)&&this.focus()}))}));const i=z.findOne(".modal.show");i&&tn.getInstance(i).hide(),tn.getOrCreateInstance(e).toggle(this)})),q(tn),f(tn);const en="coreui.navigation",nn=`.${en}`,sn={activeLinksExact:!0,groupsAutoCollapse:!0},on={activeLinksExact:"boolean",groupsAutoCollapse:"(string|boolean)"},rn="active",an="show",ln="nav-group-toggle",cn=`click${nn}.data-api`,hn=`load${nn}.data-api`,un=".nav-group",dn=".nav-group-items",fn=".nav-group-toggle";class pn extends B{constructor(t,e){super(t),this._config=this._getConfig(e),this._setActiveLink(),this._addEventListeners(),P.set(t,en,this)}static get Default(){return sn}static get DATA_KEY(){return en}static get DefaultType(){return on}static get NAME(){return"navigation"}_getConfig(t){return{...sn,...F.getDataAttributes(this._element),..."object"==typeof t?t:{}}}_setActiveLink(){for(const t of Array.from(this._element.querySelectorAll(".nav-link"))){if(t.classList.contains(ln))continue;let e=String(window.location);const i=/\?./,n=/#./;(/\?.*=/.test(e)||i.test(e))&&(e=e.split("?")[0]),n.test(e)&&(e=e.split("#")[0]),this._config.activeLinksExact&&t.href===e&&(t.classList.add(rn),Array.from(this._getParents(t,un)).forEach((t=>{t.classList.add(an),t.setAttribute("aria-expanded",!0)}))),!this._config.activeLinksExact&&t.href.startsWith(e)&&(t.classList.add(rn),Array.from(this._getParents(t,un)).forEach((t=>{t.classList.add(an),t.setAttribute("aria-expanded",!0)})))}}_getParents(t,e){const i=[];for(;t&&t!==document;t=t.parentNode)e?t.matches(e)&&i.push(t):i.push(t);return i}_getAllSiblings(t,e){const i=[];t=t.parentNode.firstChild;do{3!==t.nodeType&&8!==t.nodeType&&(e&&!e(t)||i.push(t))}while(t=t.nextSibling);return i}_getChildren(t,e){const i=[];for(;t;t=t.nextSibling)1===t.nodeType&&t!==e&&i.push(t);return i}_getSiblings(t,e){return this._getChildren(t.parentNode.firstChild,t).filter(e)}_slideDown(t){t.style.height="auto";const e=t.clientHeight;t.style.height="0px",setTimeout((()=>{t.style.height=`${e}px`}),0),this._queueCallback((()=>{t.style.height="auto"}),t,!0)}_slideUp(t,e){const i=t.clientHeight;t.style.height=`${i}px`,setTimeout((()=>{t.style.height="0px"}),0),this._queueCallback((()=>{"function"==typeof e&&e()}),t,!0)}_toggleGroupItems(t){let e=t.target;e.classList.contains(ln)||(e=e.closest(fn));const i=t=>Boolean(t.classList.contains("nav-group")&&t.classList.contains(an));if(!0===this._config.groupsAutoCollapse)for(const t of this._getSiblings(e.parentNode,i))this._slideUp(z.findOne(dn,t),(()=>{t.classList.remove(an),t.setAttribute("aria-expanded",!1)}));e.parentNode.classList.contains(an)?this._slideUp(z.findOne(dn,e.parentNode),(()=>{e.parentNode.classList.remove(an),e.parentNode.setAttribute("aria-expanded",!1)})):(e.parentNode.classList.add(an),e.parentNode.setAttribute("aria-expanded",!0),this._slideDown(z.findOne(dn,e.parentNode)))}_addEventListeners(){N.on(this._element,cn,fn,(t=>{t.preventDefault(),this._toggleGroupItems(t,this)}))}static navigationInterface(t,e){const i=pn.getOrCreateInstance(t,e);if("string"==typeof e){if(void 0===i[e])throw new TypeError(`No method named "${e}"`);i[e]()}}static jQueryInterface(t){return this.each((function(){pn.navigationInterface(this,t)}))}}N.on(window,hn,(()=>{for(const t of Array.from(document.querySelectorAll('[data-coreui="navigation"]')))pn.navigationInterface(t)})),f(pn);const gn=".coreui.offcanvas",mn=`load${gn}.data-api`,_n="show",bn="showing",vn="hiding",yn=".offcanvas.show",wn=`show${gn}`,An=`shown${gn}`,En=`hide${gn}`,Cn=`hidePrevented${gn}`,Tn=`hidden${gn}`,On=`resize${gn}`,kn=`click${gn}.data-api`,xn=`keydown.dismiss${gn}`,Ln={backdrop:!0,keyboard:!0,scroll:!1},Dn={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class Sn extends B{constructor(t,e){super(t,e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Ln}static get DefaultType(){return Dn}static get NAME(){return"offcanvas"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||N.trigger(this._element,wn,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||(new Mi).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(bn),this._queueCallback((()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(_n),this._element.classList.remove(bn),N.trigger(this._element,An,{relatedTarget:t})}),this._element,!0))}hide(){this._isShown&&(N.trigger(this._element,En).defaultPrevented||(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(vn),this._backdrop.hide(),this._queueCallback((()=>{this._element.classList.remove(_n,vn),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new Mi).reset(),N.trigger(this._element,Tn)}),this._element,!0)))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=Boolean(this._config.backdrop);return new Hi({className:"offcanvas-backdrop",isVisible:t,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:t?()=>{"static"!==this._config.backdrop?this.hide():N.trigger(this._element,Cn)}:null})}_initializeFocusTrap(){return new Ui({trapElement:this._element})}_addEventListeners(){N.on(this._element,xn,(t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():N.trigger(this._element,Cn))}))}static jQueryInterface(t){return this.each((function(){const e=Sn.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}N.on(document,kn,'[data-coreui-toggle="offcanvas"]',(function(t){const e=z.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&t.preventDefault(),r(this))return;N.one(e,Tn,(()=>{o(this)&&this.focus()}));const i=z.findOne(yn);i&&i!==e&&Sn.getInstance(i).hide(),Sn.getOrCreateInstance(e).toggle(this)})),N.on(window,mn,(()=>{for(const t of z.find(yn))Sn.getOrCreateInstance(t).show()})),N.on(window,On,(()=>{for(const t of z.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(t).position&&Sn.getOrCreateInstance(t).hide()})),q(Sn),f(Sn);const Nn=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),In=/^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i,Mn=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i,Pn=(t,e)=>{const i=t.nodeName.toLowerCase();return e.includes(i)?!Nn.has(i)||Boolean(In.test(t.nodeValue)||Mn.test(t.nodeValue)):e.filter((t=>t instanceof RegExp)).some((t=>t.test(i)))},jn={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},$n={allowList:jn,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},Fn={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},Hn={entry:"(string|element|function|null)",selector:"(string|element)"};class Bn extends H{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return $n}static get DefaultType(){return Fn}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map((t=>this._resolvePossibleFunction(t))).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[e,i]of Object.entries(this._config.content))this._setContent(t,i,e);const e=t.children[0],i=this._resolvePossibleFunction(this._config.extraClass);return i&&e.classList.add(...i.split(" ")),e}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[e,i]of Object.entries(t))super._typeCheckConfig({selector:e,entry:i},Hn)}_setContent(t,e,i){const o=z.findOne(i,t);o&&((e=this._resolvePossibleFunction(e))?n(e)?this._putElementInTemplate(s(e),o):this._config.html?o.innerHTML=this._maybeSanitize(e):o.textContent=e:o.remove())}_maybeSanitize(t){return this._config.sanitize?function(t,e,i){if(!t.length)return t;if(i&&"function"==typeof i)return i(t);const n=(new window.DOMParser).parseFromString(t,"text/html"),s=[].concat(...n.body.querySelectorAll("*"));for(const t of s){const i=t.nodeName.toLowerCase();if(!Object.keys(e).includes(i)){t.remove();continue}const n=[].concat(...t.attributes),s=[].concat(e["*"]||[],e[i]||[]);for(const e of n)Pn(e,s)||t.removeAttribute(e.nodeName)}return n.body.innerHTML}(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return p(t,[this])}_putElementInTemplate(t,e){if(this._config.html)return e.innerHTML="",void e.append(t);e.textContent=t.textContent}}const Wn=new Set(["sanitize","allowList","sanitizeFn"]),zn="fade",qn="show",Rn=".modal",Vn="hide.coreui.modal",Un="hover",Kn="focus",Qn={AUTO:"auto",TOP:"top",RIGHT:d()?"left":"right",BOTTOM:"bottom",LEFT:d()?"right":"left"},Yn={allowList:jn,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,0],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},Xn={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class Gn extends B{constructor(t,e){if(void 0===oi)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(t,e),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return Yn}static get DefaultType(){return Xn}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),N.off(this._element.closest(Rn),Vn,this._hideModalHandler),this._element.getAttribute("data-coreui-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-coreui-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const t=N.trigger(this._element,this.constructor.eventName("show")),e=(a(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!e)return;this._disposePopper();const i=this._getTipElement();this._element.setAttribute("aria-describedby",i.getAttribute("id"));const{container:n}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(n.append(i),N.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(i),i.classList.add(qn),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))N.on(t,"mouseover",l);this._queueCallback((()=>{N.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1}),this.tip,this._isAnimated())}hide(){if(this._isShown()&&!N.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(qn),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))N.off(t,"mouseover",l);this._activeTrigger.click=!1,this._activeTrigger.focus=!1,this._activeTrigger.hover=!1,this._isHovered=null,this._queueCallback((()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),N.trigger(this._element,this.constructor.eventName("hidden")))}),this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const e=this._getTemplateFactory(t).toHtml();if(!e)return null;e.classList.remove(zn,qn),e.classList.add(`bs-${this.constructor.NAME}-auto`);const i=(t=>{do{t+=Math.floor(1e6*Math.random())}while(document.getElementById(t));return t})(this.constructor.NAME).toString();return e.setAttribute("id",i),this._isAnimated()&&e.classList.add(zn),e}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new Bn({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{".tooltip-inner":this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-coreui-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(zn)}_isShown(){return this.tip&&this.tip.classList.contains(qn)}_createPopper(t){const e=p(this._config.placement,[this,t,this._element]),i=Qn[e.toUpperCase()];return si(this._element,t,this._getPopperConfig(i))}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map((t=>Number.parseInt(t,10))):"function"==typeof t?e=>t(e,this._element):t}_resolvePossibleFunction(t){return p(t,[this._element])}_getPopperConfig(t){const e={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:t=>{this._getTipElement().setAttribute("data-popper-placement",t.state.placement)}}]};return{...e,...p(this._config.popperConfig,[e])}}_setListeners(){const t=this._config.trigger.split(" ");for(const e of t)if("click"===e)N.on(this._element,this.constructor.eventName("click"),this._config.selector,(t=>{this._initializeOnDelegatedTarget(t).toggle()}));else if("manual"!==e){const t=e===Un?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),i=e===Un?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");N.on(this._element,t,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusin"===t.type?Kn:Un]=!0,e._enter()})),N.on(this._element,i,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusout"===t.type?Kn:Un]=e._element.contains(t.relatedTarget),e._leave()}))}this._hideModalHandler=()=>{this._element&&this.hide()},N.on(this._element.closest(Rn),Vn,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",t),this._element.setAttribute("data-coreui-original-title",t),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((()=>{this._isHovered&&this.show()}),this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((()=>{this._isHovered||this.hide()}),this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const e=F.getDataAttributes(this._element);for(const t of Object.keys(e))Wn.has(t)&&delete e[t];return t={...e,..."object"==typeof t&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=!1===t.container?document.body:s(t.container),"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[e,i]of Object.entries(this._config))this.constructor.Default[e]!==i&&(t[e]=i);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each((function(){const e=Gn.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}f(Gn);const Jn={...Gn.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},Zn={...Gn.DefaultType,content:"(null|string|element|function)"};class ts extends Gn{static get Default(){return Jn}static get DefaultType(){return Zn}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{".popover-header":this._getTitle(),".popover-body":this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each((function(){const e=ts.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}f(ts);const es=".coreui.scrollspy",is=`activate${es}`,ns=`click${es}`,ss=`load${es}.data-api`,os="active",rs="[href]",as={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},ls={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class cs extends B{constructor(t,e){super(t,e),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return as}static get DefaultType(){return ls}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=s(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,"string"==typeof t.threshold&&(t.threshold=t.threshold.split(",").map((t=>Number.parseFloat(t)))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(N.off(this._config.target,ns),N.on(this._config.target,ns,rs,(t=>{const e=this._observableSections.get(t.target.hash);if(e){t.preventDefault();const i=this._rootElement||window,n=e.offsetTop-this._element.offsetTop;if(i.scrollTo)return void i.scrollTo({top:n,behavior:"smooth"});i.scrollTop=n}})))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver((t=>this._observerCallback(t)),t)}_observerCallback(t){const e=t=>this._targetLinks.get(`#${t.target.id}`),i=t=>{this._previousScrollData.visibleEntryTop=t.target.offsetTop,this._process(e(t))},n=(this._rootElement||document.documentElement).scrollTop,s=n>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=n;for(const o of t){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(e(o));continue}const t=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(s&&t){if(i(o),!n)return}else s||t||i(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=z.find(rs,this._config.target);for(const e of t){if(!e.hash||r(e))continue;const t=z.findOne(e.hash,this._element);o(t)&&(this._targetLinks.set(e.hash,e),this._observableSections.set(e.hash,t))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(os),this._activateParents(t),N.trigger(this._element,is,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains("dropdown-item"))z.findOne(".dropdown-toggle",t.closest(".dropdown")).classList.add(os);else for(const e of z.parents(t,".nav, .list-group"))for(const t of z.prev(e,".nav-link, .nav-item > .nav-link, .list-group-item"))t.classList.add(os)}_clearActiveClass(t){t.classList.remove(os);const e=z.find("[href].active",t);for(const t of e)t.classList.remove(os)}static jQueryInterface(t){return this.each((function(){const e=cs.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}))}}N.on(window,ss,(()=>{for(const t of z.find('[data-coreui-spy="scroll"]'))cs.getOrCreateInstance(t)})),f(cs);const hs=".coreui.sidebar",us={},ds={},fs="hide",ps="show",gs="sidebar-narrow",ms="sidebar-narrow-unfoldable",_s=`hide${hs}`,bs=`hidden${hs}`,vs=`show${hs}`,ys=`shown${hs}`,ws=`click${hs}.data-api`,As=`load${hs}.data-api`,Es=".sidebar";class Cs extends B{constructor(t,e){super(t),this._config=this._getConfig(e),this._show=this._isVisible(),this._mobile=this._isMobile(),this._overlaid=this._isOverlaid(),this._narrow=this._isNarrow(),this._unfoldable=this._isUnfoldable(),this._backdrop=this._initializeBackDrop(),this._addEventListeners()}static get Default(){return us}static get DefaultType(){return ds}static get NAME(){return"sidebar"}show(){N.trigger(this._element,vs),this._element.classList.contains(fs)&&this._element.classList.remove(fs),this._isMobile()&&(this._element.classList.add(ps),this._backdrop.show(),(new Mi).hide()),this._queueCallback((()=>{!0===this._isVisible()&&(this._show=!0,(this._isMobile()||this._isOverlaid())&&this._addClickOutListener(),N.trigger(this._element,ys))}),this._element,!0)}hide(){N.trigger(this._element,_s),this._element.classList.contains(ps)&&this._element.classList.remove(ps),this._isMobile()?(this._backdrop.hide(),(new Mi).reset()):this._element.classList.add(fs),this._queueCallback((()=>{!1===this._isVisible()&&(this._show=!1,(this._isMobile()||this._isOverlaid())&&this._removeClickOutListener(),N.trigger(this._element,bs))}),this._element,!0)}toggle(){this._isVisible()?this.hide():this.show()}narrow(){this._isMobile()||(this._addClassName(gs),this._narrow=!0)}unfoldable(){this._isMobile()||(this._addClassName(ms),this._unfoldable=!0)}reset(){this._isMobile()||(this._narrow&&(this._element.classList.remove(gs),this._narrow=!1),this._unfoldable&&(this._element.classList.remove(ms),this._unfoldable=!1))}toggleNarrow(){this._narrow?this.reset():this.narrow()}toggleUnfoldable(){this._unfoldable?this.reset():this.unfoldable()}_getConfig(t){return{...us,...F.getDataAttributes(this._element),..."object"==typeof t?t:{}}}_initializeBackDrop(){return new Hi({className:"sidebar-backdrop",isVisible:this._isMobile(),isAnimated:!0,rootElement:this._element.parentNode,clickCallback:()=>this.hide()})}_isMobile(){return Boolean(window.getComputedStyle(this._element,null).getPropertyValue("--cui-is-mobile"))}_isNarrow(){return this._element.classList.contains(gs)}_isOverlaid(){return this._element.classList.contains("sidebar-overlaid")}_isUnfoldable(){return this._element.classList.contains(ms)}_isVisible(){const t=this._element.getBoundingClientRect();return t.top>=0&&t.left>=0&&Math.floor(t.bottom)<=(window.innerHeight||document.documentElement.clientHeight)&&Math.floor(t.right)<=(window.innerWidth||document.documentElement.clientWidth)}_addClassName(t){this._element.classList.add(t)}_clickOutListener(t,e){null===t.target.closest(Es)&&(t.preventDefault(),t.stopPropagation(),e.hide())}_addClickOutListener(){N.on(document,ws,(t=>{this._clickOutListener(t,this)}))}_removeClickOutListener(){N.off(document,ws)}_addEventListeners(){this._mobile&&this._show&&this._addClickOutListener(),this._overlaid&&this._show&&this._addClickOutListener(),N.on(this._element,ws,"[data-coreui-toggle]",(t=>{t.preventDefault();const e=F.getDataAttribute(t.target,"toggle");"narrow"===e&&this.toggleNarrow(),"unfoldable"===e&&this.toggleUnfoldable()})),N.on(this._element,ws,'[data-coreui-close="sidebar"]',(t=>{t.preventDefault(),this.hide()})),N.on(window,"resize",(()=>{this._isMobile()&&this._isVisible()&&(this.hide(),this._backdrop=this._initializeBackDrop())}))}static sidebarInterface(t,e){const i=Cs.getOrCreateInstance(t,e);if("string"==typeof e){if(void 0===i[e])throw new TypeError(`No method named "${e}"`);i[e]()}}static jQueryInterface(t){return this.each((function(){Cs.sidebarInterface(this,t)}))}}N.on(window,As,(()=>{for(const t of Array.from(document.querySelectorAll(Es)))Cs.sidebarInterface(t)})),f(Cs);const Ts="ArrowLeft",Os="ArrowRight",ks="ArrowUp",xs="ArrowDown",Ls="active",Ds="fade",Ss="show",Ns='[data-coreui-toggle="tab"], [data-coreui-toggle="pill"], [data-coreui-toggle="list"]',Is=`.nav-link:not(.dropdown-toggle), .list-group-item:not(.dropdown-toggle), [role="tab"]:not(.dropdown-toggle), ${Ns}`;class Ms extends B{constructor(t){super(t),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),N.on(this._element,"keydown.coreui.tab",(t=>this._keydown(t))))}static get NAME(){return"tab"}show(){const t=this._element;if(this._elemIsActive(t))return;const e=this._getActiveElem(),i=e?N.trigger(e,"hide.coreui.tab",{relatedTarget:t}):null;N.trigger(t,"show.coreui.tab",{relatedTarget:e}).defaultPrevented||i&&i.defaultPrevented||(this._deactivate(e,t),this._activate(t,e))}_activate(t,e){t&&(t.classList.add(Ls),this._activate(z.getElementFromSelector(t)),this._queueCallback((()=>{"tab"===t.getAttribute("role")?(t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),N.trigger(t,"shown.coreui.tab",{relatedTarget:e})):t.classList.add(Ss)}),t,t.classList.contains(Ds)))}_deactivate(t,e){t&&(t.classList.remove(Ls),t.blur(),this._deactivate(z.getElementFromSelector(t)),this._queueCallback((()=>{"tab"===t.getAttribute("role")?(t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),N.trigger(t,"hidden.coreui.tab",{relatedTarget:e})):t.classList.remove(Ss)}),t,t.classList.contains(Ds)))}_keydown(t){if(![Ts,Os,ks,xs].includes(t.key))return;t.stopPropagation(),t.preventDefault();const e=[Os,xs].includes(t.key),i=m(this._getChildren().filter((t=>!r(t))),t.target,e,!0);i&&(i.focus({preventScroll:!0}),Ms.getOrCreateInstance(i).show())}_getChildren(){return z.find(Is,this._parent)}_getActiveElem(){return this._getChildren().find((t=>this._elemIsActive(t)))||null}_setInitialAttributes(t,e){this._setAttributeIfNotExists(t,"role","tablist");for(const t of e)this._setInitialAttributesOnChild(t)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const e=this._elemIsActive(t),i=this._getOuterElement(t);t.setAttribute("aria-selected",e),i!==t&&this._setAttributeIfNotExists(i,"role","presentation"),e||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const e=z.getElementFromSelector(t);e&&(this._setAttributeIfNotExists(e,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(e,"aria-labelledby",`#${t.id}`))}_toggleDropDown(t,e){const i=this._getOuterElement(t);if(!i.classList.contains("dropdown"))return;const n=(t,n)=>{const s=z.findOne(t,i);s&&s.classList.toggle(n,e)};n(".dropdown-toggle",Ls),n(".dropdown-menu",Ss),i.setAttribute("aria-expanded",e)}_setAttributeIfNotExists(t,e,i){t.hasAttribute(e)||t.setAttribute(e,i)}_elemIsActive(t){return t.classList.contains(Ls)}_getInnerElement(t){return t.matches(Is)?t:z.findOne(Is,t)}_getOuterElement(t){return t.closest(".nav-item, .list-group-item")||t}static jQueryInterface(t){return this.each((function(){const e=Ms.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}))}}N.on(document,"click.coreui.tab",Ns,(function(t){["A","AREA"].includes(this.tagName)&&t.preventDefault(),r(this)||Ms.getOrCreateInstance(this).show()})),N.on(window,"load.coreui.tab",(()=>{for(const t of z.find('.active[data-coreui-toggle="tab"], .active[data-coreui-toggle="pill"], .active[data-coreui-toggle="list"]'))Ms.getOrCreateInstance(t)})),f(Ms);const Ps=".coreui.toast",js=`mouseover${Ps}`,$s=`mouseout${Ps}`,Fs=`focusin${Ps}`,Hs=`focusout${Ps}`,Bs=`hide${Ps}`,Ws=`hidden${Ps}`,zs=`show${Ps}`,qs=`shown${Ps}`,Rs="hide",Vs="show",Us="showing",Ks={animation:"boolean",autohide:"boolean",delay:"number"},Qs={animation:!0,autohide:!0,delay:5e3};class Ys extends B{constructor(t,e){super(t,e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return Qs}static get DefaultType(){return Ks}static get NAME(){return"toast"}show(){N.trigger(this._element,zs).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(Rs),c(this._element),this._element.classList.add(Vs,Us),this._queueCallback((()=>{this._element.classList.remove(Us),N.trigger(this._element,qs),this._maybeScheduleHide()}),this._element,this._config.animation))}hide(){this.isShown()&&(N.trigger(this._element,Bs).defaultPrevented||(this._element.classList.add(Us),this._queueCallback((()=>{this._element.classList.add(Rs),this._element.classList.remove(Us,Vs),N.trigger(this._element,Ws)}),this._element,this._config.animation)))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(Vs),super.dispose()}isShown(){return this._element.classList.contains(Vs)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout((()=>{this.hide()}),this._config.delay)))}_onInteraction(t,e){switch(t.type){case"mouseover":case"mouseout":this._hasMouseInteraction=e;break;case"focusin":case"focusout":this._hasKeyboardInteraction=e}if(e)return void this._clearTimeout();const i=t.relatedTarget;this._element===i||this._element.contains(i)||this._maybeScheduleHide()}_setListeners(){N.on(this._element,js,(t=>this._onInteraction(t,!0))),N.on(this._element,$s,(t=>this._onInteraction(t,!1))),N.on(this._element,Fs,(t=>this._onInteraction(t,!0))),N.on(this._element,Hs,(t=>this._onInteraction(t,!1)))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each((function(){const e=Ys.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}return q(Ys),f(Ys),{Alert:R,Button:U,Carousel:pt,Collapse:kt,Dropdown:Li,Modal:tn,Navigation:pn,OffCanvas:Sn,Popover:ts,ScrollSpy:cs,Sidebar:Cs,Tab:Ms,Toast:Ys,Tooltip:Gn}}));
//# sourceMappingURL=coreui.bundle.min.js.map